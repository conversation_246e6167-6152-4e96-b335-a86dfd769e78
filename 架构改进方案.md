# 规则引擎架构改进方案

## 改进背景

在初始实现中发现了以下问题：

1. **过度特化**：DeviceStateManager、MqttMessageListener等组件针对特定设备类型（如占用传感器）进行了硬编码
2. **缺乏抽象性**：OccupancyState等模型类只适用于特定场景，不具备通用性
3. **扩展性差**：添加新的设备类型或条件类型需要修改核心代码

## 改进目标

1. **通用化设备状态管理**：支持任意设备点位的状态跟踪和条件判断
2. **抽象化消息处理**：统一处理各种设备数据，不再针对特定设备类型
3. **灵活的条件定义**：支持各种操作符和持续时间判断
4. **良好的扩展性**：新增设备类型或条件类型无需修改核心代码

## 核心改进

### 1. 通用设备状态管理

#### 1.1 DevicePointState - 通用点位状态模型

```java
public class DevicePointState {
    private String deviceId;
    private String pointId;
    private Object currentValue;
    private String dataType;
    private Object previousValue;
    private LocalDateTime currentValueStartTime;
    private LocalDateTime lastUpdateTime;
    private Map<LocalDateTime, Object> valueHistory;

    // 通用方法：判断当前值是否满足指定条件并持续了指定时间
    public boolean matchesConditionForDuration(String operator, Object targetValue, long durationMinutes);

    // 通用方法：获取当前值持续时间
    public long getCurrentValueDurationMinutes();
}
```

**支持的应用场景：**

- 占用状态 = "UNOCCUPIED" 持续 15分钟
- 温度 > 30度 持续 10分钟
- 照度 < 100 持续 5分钟
- 湿度介于 40-60% 持续 30分钟

#### 1.2 StateCondition - 通用状态条件定义

```java
public class StateCondition {
    private String conditionId;
    private String deviceId;
    private String pointId;
    private String operator; // EQUALS, GREATER_THAN, LESS_THAN, BETWEEN, IN, etc.
    private Object targetValue;
    private Object targetValue2; // 用于BETWEEN操作符
    private long durationMinutes;
    private String dataType;

    // 支持的操作符示例：
    // EQUALS, NOT_EQUALS, GREATER_THAN, GREATER_THAN_OR_EQUAL
    // LESS_THAN, LESS_THAN_OR_EQUAL, BETWEEN, IN, NOT_IN
    // CONTAINS, NOT_CONTAINS, STARTS_WITH, ENDS_WITH
}
```

#### 1.3 ValueComparator - 通用值比较器

```java
public class ValueComparator {
    public static boolean compare(Object actualValue, String operator, Object targetValue, String dataType);
    public static boolean compare(Object actualValue, String operator, Object targetValue, Object targetValue2, String dataType);

    // 支持各种数据类型的比较：
    // - 数值类型：INTEGER, DOUBLE, FLOAT, LONG
    // - 字符串类型：STRING
    // - 布尔类型：BOOLEAN
    // - JSON类型：JSON
}
```

### 2. 通用消息处理

#### 2.1 MqttMessageListener - 通用MQTT监听器

```java
@Component
public class MqttMessageListener {

    public void handleMessage(String topic, String payload) {
        // 1. 解析主题，提取设备ID和点位ID（支持多种主题格式）
        DeviceTopicInfo topicInfo = parseTopicInfo(topic);

        // 2. 解析消息内容（支持JSON和简单值格式）
        DeviceDataMessage dataMessage = parsePayload(payload);

        // 3. 推断数据类型（如果未提供）
        String dataType = inferDataType(value);

        // 4. 通用处理：更新设备状态管理器
        deviceStateManager.processDevicePointUpdate(deviceId, pointId, value, dataType);
    }

    // 支持的主题格式：
    // - /device/{deviceId}/{pointId}
    // - /data/{deviceId}/{pointId}
    // - /sensor/{deviceId}/{pointId}
    // - /iot/{deviceId}/{pointId}
    // - /{deviceId}/{pointId}
}
```

#### 2.2 批量消息处理

```java
public void handleBatchMessages(String topic, String payload) {
    // 支持批量数据上报格式：
    // [
    //   {"deviceId": "dev1", "pointId": "temp", "value": 25.5},
    //   {"deviceId": "dev1", "pointId": "humidity", "value": 60}
    // ]
}
```

### 3. 状态条件监控

#### 3.1 StateConditionMonitor - 状态条件监控器

```java
private static class StateConditionMonitor {
    private final StateCondition condition;
    private final ScheduledExecutorService scheduler;
    private ScheduledFuture<?> timeoutTask;
    private boolean conditionCurrentlyMet = false;

    public void checkCondition(DevicePointState pointState) {
        boolean conditionMet = condition.matches(pointState.getCurrentValue(), pointState.getDataType());

        if (conditionMet && !conditionCurrentlyMet) {
            // 条件开始满足，启动超时任务
            startTimeoutTask(pointState);
        } else if (!conditionMet && conditionCurrentlyMet) {
            // 条件不再满足，取消超时任务
            cancelTimeoutTask();
        }
    }
}
```

#### 3.2 动态条件注册

```java
// 支持运行时动态注册状态条件
public void registerStateCondition(StateCondition condition) {
    StateConditionMonitor monitor = new StateConditionMonitor(condition, scheduler, this);
    stateConditionMonitors.put(condition.getConditionId(), monitor);
}

// 支持批量注册
public void registerStateConditions(List<StateCondition> conditions) {
    for (StateCondition condition : conditions) {
        registerStateCondition(condition);
    }
}
```

### 4. 事件模型

#### 4.1 StateChangeEvent - 状态变化事件

```java
public class StateChangeEvent {
    public enum EventType {
        VALUE_CHANGED,           // 值变化
        CONDITION_MET,           // 条件满足
        CONDITION_TIMEOUT,       // 条件超时
        CONDITION_RESET,         // 条件重置
        STATE_INITIALIZED,       // 状态初始化
        STATE_UPDATED           // 状态更新
    }

    // 工厂方法
    public static StateChangeEvent createValueChangeEvent(String deviceId, String pointId, Object oldValue, Object newValue, String dataType);
    public static StateChangeEvent createConditionMetEvent(String deviceId, String pointId, String conditionId, Long durationMinutes);
    public static StateChangeEvent createConditionTimeoutEvent(String deviceId, String pointId, String conditionId, Long durationMinutes);
}
```

## 使用示例

### 1. 占用传感器场景

```java
// 创建状态条件：占用状态 = "UNOCCUPIED" 持续 15分钟
StateCondition condition = new StateCondition(
    "unoccupied_15min_condition",
    "room_001",
    "occupancy_status",
    "EQUALS",
    "UNOCCUPIED",
    15
);

// 注册条件监控
deviceStateManager.registerStateCondition(condition);

// 当MQTT消息到达时，自动处理
// Topic: /device/room_001/occupancy_status
// Payload: "UNOCCUPIED"
// 系统会自动跟踪状态变化，15分钟后触发超时事件
```

### 2. 温度传感器场景

```java
// 创建状态条件：温度 > 30度 持续 10分钟
StateCondition tempCondition = new StateCondition(
    "high_temp_10min_condition",
    "room_001",
    "temperature",
    "GREATER_THAN",
    30.0,
    10
);

deviceStateManager.registerStateCondition(tempCondition);

// Topic: /sensor/room_001/temperature
// Payload: {"value": 32.5, "dataType": "DOUBLE"}
```

### 3. 湿度范围监控

```java
// 创建状态条件：湿度介于 40-60% 持续 30分钟
StateCondition humidityCondition = new StateCondition(
    "humidity_range_30min_condition",
    "room_001",
    "humidity",
    "BETWEEN",
    40.0,
    60.0,  // targetValue2
    30
);

deviceStateManager.registerStateCondition(humidityCondition);
```

## 架构优势

### 1. 通用性

- 支持任意设备类型和点位
- 支持各种数据类型和操作符
- 统一的状态管理机制

### 2. 扩展性

- 新增设备类型无需修改核心代码
- 新增操作符只需扩展ValueComparator
- 支持动态条件注册和注销

### 3. 灵活性

- 支持复杂的条件组合
- 支持多种消息格式
- 支持批量数据处理

### 4. 可维护性

- 清晰的职责分离
- 统一的错误处理
- 完整的日志记录

## 迁移策略

### 1. 保持兼容性

- 保留原有的DeviceStateManager作为兼容层
- 逐步迁移到GenericDeviceStateManager
- 提供适配器模式支持旧接口

### 2. 渐进式迁移

1. 部署新的通用组件
2. 配置新的状态条件
3. 验证功能正确性
4. 逐步停用旧组件

### 3. 数据迁移

- 将现有的占用状态数据转换为通用格式
- 更新规则定义以使用新的条件格式
- 保持数据一致性

## 总结

通过这次架构改进，我们实现了：

1. **从特化到通用**：将针对特定设备的硬编码逻辑抽象为通用的状态管理机制
2. **从固化到灵活**：支持动态配置各种条件和操作符
3. **从单一到多样**：支持多种设备类型、数据格式和消息协议
4. **从复杂到简单**：统一的接口和处理流程，降低维护成本

这个改进方案为规则引擎提供了更强的通用性和扩展性，能够适应更多的IoT应用场景。
