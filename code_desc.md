# 物联网规则引擎代码结构说明

## 项目概述
- **项目名称：** IoT Rule Engine (物联网规则引擎)
- **技术栈：** Java 8 + Spring Boot 2.7.18 + Easy Rules + SQLite + MyBatis
- **架构模式：** 分层架构 + 模块化设计

## 目录结构

```
rule-engine/
├── pom.xml                                    # Maven项目配置文件
├── src/
│   ├── main/
│   │   ├── java/com/inxaiot/ruleengine/
│   │   │   ├── app/                           # 应用启动和配置
│   │   │   │   ├── RuleEngineApplication.java # Spring Boot主启动类
│   │   │   │   └── RuleEngineConfig.java      # 应用配置类
│   │   │   ├── storage/                       # 数据持久化层
│   │   │   │   ├── entity/                    # 数据库实体类
│   │   │   │   ├── mapper/                    # MyBatis Mapper接口
│   │   │   │   └── service/                   # 数据访问服务
│   │   │   ├── core/                          # 规则引擎核心逻辑
│   │   │   │   ├── definition/                # 规则定义模型
│   │   │   │   │   ├── RuleDefinition.java    # 核心规则定义
│   │   │   │   │   ├── TimeCondition.java     # 时间条件定义
│   │   │   │   │   ├── TriggerConditions.java # 触发条件定义
│   │   │   │   │   ├── DeviceCondition.java   # 设备条件定义
│   │   │   │   │   └── ActionDefinition.java  # 动作定义
│   │   │   │   ├── adapter/                   # 规则适配器
│   │   │   │   ├── engine/                    # 规则引擎服务
│   │   │   │   └── action/                    # 动作执行器
│   │   │   ├── scheduler/                     # 时间条件处理
│   │   │   │   ├── model/                     # 时间相关模型
│   │   │   │   └── service/                   # 时间条件评估服务
│   │   │   ├── contextstate/                  # 状态管理
│   │   │   │   ├── model/                     # 状态对象模型
│   │   │   │   └── service/                   # 状态管理服务
│   │   │   ├── transport/                     # 数据传输层
│   │   │   │   ├── listener/                  # 消息监听器
│   │   │   │   └── publisher/                 # 消息发布器
│   │   │   └── api/                           # API接口层
│   │   │       └── controller/                # REST控制器
│   │   └── resources/
│   │       ├── application.yml                # 应用配置文件
│   │       └── mapper/                        # MyBatis XML映射文件
│   └── test/                                  # 测试代码
│       ├── java/com/inxaiot/ruleengine/
│       └── resources/
├── task.md                                    # 任务拆解文档
├── progress.md                                # 开发进度文档
└── code_desc.md                               # 代码结构说明文档
```

## 核心模块说明

### 1. app包 - 应用启动和配置
- **RuleEngineApplication.java**: Spring Boot主启动类，配置组件扫描和MyBatis映射
- **RuleEngineConfig.java**: 应用配置类，包含线程池、定时器、Jackson等配置
  - **ruleEvaluationExecutor**: 规则评估线程池（CPU密集型，核心数线程）
  - **actionExecutionExecutor**: 动作执行线程池（IO密集型，更多线程）
  - **deviceStateScheduler**: 设备状态管理定时器（2个线程）
  - **timeTriggerScheduler**: 时间触发器定时器（1个线程，预留）

### 2. core.definition包 - 核心数据模型
- **RuleDefinition.java**: 规则定义的核心数据结构，包含规则ID、名称、目标设备、优先级等
- **TimeCondition.java**: 时间条件定义，支持Cron表达式、工作日、季节、个性化日历
- **TriggerConditions.java**: 设备触发条件组合，支持AND/OR逻辑
- **DeviceCondition.java**: 单个设备条件，包含设备ID、点位、操作符、比较值等
- **ActionDefinition.java**: 动作定义，支持设备控制、消息发送、API调用等

### 3. storage包 - 数据持久化层
- **entity/**: 数据库实体类，对应数据库表结构
- **mapper/**: MyBatis Mapper接口，定义数据访问方法
- **service/**: 数据访问服务，封装业务友好的数据操作

### 4. scheduler包 - 时间条件处理
- **model/**: 时间相关的数据模型
- **service/**: 时间条件评估服务，处理复杂时间逻辑

### 5. contextstate包 - 状态管理
- **model/**: 状态对象模型，如设备占用状态
- **service/**: 状态管理服务，处理持续时间判断

### 6. core.adapter包 - 规则适配器
- 将RuleDefinition转换为Easy Rules的Rule对象
- 实现条件评估和动作执行的适配逻辑

### 7. core.engine包 - 规则引擎服务
- 封装Easy Rules引擎
- 提供规则加载、执行、管理功能
- **异步处理**: 使用规则评估线程池进行异步规则处理

### 8. core.action包 - 动作执行器
- 实现各种类型动作的执行逻辑
- 支持设备控制、消息发送、API调用等
- **异步执行**: 使用动作执行线程池进行异步动作执行

### 9. transport包 - 数据传输层
- **listener/**: 消息监听器，接收设备数据
- **publisher/**: 消息发布器，发送控制指令

### 10. api包 - API接口层
- **controller/**: REST控制器，提供规则管理API

## 技术特性

### 异步处理架构
- **分层异步**: 规则评估和动作执行使用独立线程池
- **事件驱动**: 事件源快速返回，异步处理后续逻辑
- **资源隔离**: CPU密集型和IO密集型任务分离
- **错误隔离**: 动作执行失败不影响规则引擎核心

### 数据模型设计
- 采用POJO设计，支持JSON序列化/反序列化
- 使用Jackson注解进行字段映射
- 支持复杂的嵌套结构和集合类型

### 时间条件支持
- Cron表达式：支持复杂的时间段定义
- 工作日配置：支持自定义工作日
- 季节配置：支持季节性规则
- 个性化日历：支持包含/排除特定日期

### 设备条件支持
- 多种操作符：等于、大于、小于、包含等
- 持续时间判断：支持"无人X分钟"等逻辑
- 数据类型转换：支持字符串、数值、布尔类型
- 组合逻辑：支持AND/OR条件组合

### 动作类型支持
- 设备控制：开关、设定值等
- 消息发送：支持多种消息类型
- API调用：支持HTTP请求
- 日志记录：支持事件日志

## 配置说明

### 应用配置 (application.yml)
- 数据库配置：SQLite连接参数
- 日志配置：日志级别和输出格式
- 规则引擎配置：执行参数、缓存大小等
- 监控配置：健康检查、指标暴露

### 依赖配置 (pom.xml)
- Spring Boot 2.7.18：基础框架
- Easy Rules 4.1.0：规则引擎核心
- MyBatis 2.3.2：数据访问框架
- SQLite 3.44.1.0：嵌入式数据库
- Quartz 2.3.2：Cron表达式解析

## 开发状态

### 已完成
- [x] 项目基础架构搭建
- [x] 核心数据模型定义
- [x] 应用配置和启动类

### 进行中
- [ ] 数据库设计和MyBatis配置
- [ ] 存储层服务实现

### 待开发
- [ ] 时间条件评估服务
- [ ] 设备状态管理服务
- [ ] 规则引擎核心服务
- [ ] API接口层
- [ ] 数据传输层

## 设计原则

1. **模块化设计**: 按功能划分包结构，职责清晰
2. **分层架构**: API层、业务层、数据层分离
3. **配置驱动**: 通过配置文件控制行为
4. **扩展性**: 支持新的条件类型和动作类型
5. **可测试性**: 接口与实现分离，便于单元测试
