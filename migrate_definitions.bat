@echo off
echo 正在迁移规则定义类...

REM 迁移 TriggerCondition.java
copy "src\main\java\com\inxaiot\re\core\definition\TriggerCondition.java" "src\main\java\com\inxaiot\ruleengine\rule\definition\TriggerCondition.java"

REM 迁移 DeviceCondition.java
copy "src\main\java\com\inxaiot\re\core\definition\DeviceCondition.java" "src\main\java\com\inxaiot\ruleengine\rule\definition\DeviceCondition.java"

REM 迁移 ActionDefinition.java
copy "src\main\java\com\inxaiot\re\core\definition\ActionDefinition.java" "src\main\java\com\inxaiot\ruleengine\rule\definition\ActionDefinition.java"

REM 迁移 TimeCondition.java
copy "src\main\java\com\inxaiot\re\core\definition\TimeCondition.java" "src\main\java\com\inxaiot\ruleengine\rule\definition\TimeCondition.java"

echo 定义类迁移完成！
