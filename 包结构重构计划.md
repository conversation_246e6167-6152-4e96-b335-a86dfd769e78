# 规则引擎包结构重构计划

## 📋 重构概述

### 重构目标

- 解决当前包结构混乱、职责不清的问题
- 建立清晰的功能模块化包结构
- 提高代码可维护性和可扩展性

### 重构策略：备份+重建

1. **备份现有代码**：将 `com.inxaiot.ruleengine` 重命名为 `com.inxaiot.re`,已完成
2. **重建包结构**：按新设计创建 `com.inxaiot.ruleengine` 包结构，主包已建，子包还没建
3. **逐步迁移**：从备份包逐个迁移类到新包结构
4. **测试验证**：每个模块迁移后进行功能测试
5. **清理备份**：迁移完成后删除备份包

## 🎯 新包结构设计

```
com.inxaiot.ruleengine/
├── rule/                           # 规则模块 - 规则定义、引擎、适配
│   ├── definition/                 # 规则定义模型
│   ├── engine/                     # 规则引擎核心
│   ├── adapter/                    # 规则适配器
│   ├── analyzer/                   # 规则分析器
│   └── storage/                    # 规则存储
├── device/                         # 设备模块 - 设备状态、事件处理
│   ├── state/                      # 设备状态管理
│   └── event/                      # 设备事件处理
├── action/                         # 动作模块 - 动作定义、执行
│   ├── definition/                 # 动作定义（可能与rule.definition合并）
│   └── executor/                   # 动作执行器
├── trigger/                        # 触发模块 - 时间触发、事件触发、Facts构建
│   ├── time/                       # 时间触发
│   ├── event/                      # 事件触发
│   └── facts/                      # Facts构建
├── transport/                      # 传输模块 - 外部接口
│   ├── mqtt/                       # MQTT监听
│   └── rest/                       # REST接口
└── common/                         # 公共模块 - 配置、工具、操作符
    ├── config/                     # 配置
    ├── operator/                   # 操作符定义
    └── context/                    # 系统上下文
```

## 📂 详细迁移映射表

### 当前包结构分析

```
com.inxaiot.re/
├── contextstate/
│   ├── model/
│   │   ├── DevicePointState.java
│   │   ├── StateChangeEvent.java
│   │   └── StateConditionMonitor.java
│   └── service/
│       ├── DeviceStateManager.java
│       └── RuleEngineTriggerService.java (接口)
├── core/
│   ├── action/
│   │   └── ActionExecutorService.java
│   ├── adapter/
│   │   └── RuleAdapterService.java
│   ├── analyzer/
│   │   └── RuleDependencyAnalyzer.java
│   ├── definition/
│   │   ├── ActionDefinition.java
│   │   ├── DeviceCondition.java
│   │   ├── RuleDefinition.java
│   │   ├── TimeCondition.java
│   │   └── TriggerCondition.java
│   ├── engine/
│   │   └── RuleEngineTriggerServiceImpl.java
│   ├── facts/
│   │   └── EnhancedFactsBuilder.java
│   └── model/
│       └── DevicePointRef.java
├── scheduler/
│   └── service/
│       └── TimeTriggerService.java
├── storage/
│   └── service/
│       └── RuleStorageService.java
├── transport/
│   └── listener/
│       └── MqttMessageListener.java
├── trigger/
│   └── time/
│       ├── GlobalCalendar.java
│       └── TimeConditionEvaluator.java
├── common/
│   └── operator/
│       ├── Operators.java
│       └── ValueComparator.java
└── config/
    ├── SystemContextService.java
    └── ThreadPoolConfig.java
```

### 迁移映射表

| 当前位置                                            | 新位置                                   | 新类名  | 迁移优先级 |
| ----------------------------------------------- | ------------------------------------- | ---- | ----- |
| **规则模块**                                        |                                       |      |       |
| `core.definition.RuleDefinition`                | `rule.definition.RuleDefinition`      | 保持不变 | P1    |
| `core.definition.TriggerCondition`              | `rule.definition.TriggerCondition`    | 保持不变 | P1    |
| `core.definition.DeviceCondition`               | `rule.definition.DeviceCondition`     | 保持不变 | P1    |
| `core.definition.ActionDefinition`              | `rule.definition.ActionDefinition`    | 保持不变 | P1    |
| `core.definition.TimeCondition`                 | `rule.definition.TimeCondition`       | 保持不变 | P1    |
| `contextstate.service.RuleEngineTriggerService` | `rule.engine.RuleEngineService`       | 重命名  | P1    |
| `core.engine.RuleEngineTriggerServiceImpl`      | `rule.engine.RuleEngineServiceImpl`   | 重命名  | P1    |
| `core.adapter.RuleAdapterService`               | `rule.adapter.RuleAdapterService`     | 保持不变 | P2    |
| `core.analyzer.RuleDependencyAnalyzer`          | `rule.analyzer.DependencyAnalyzer`    | 重命名  | P2    |
| `storage.service.RuleStorageService`            | `rule.storage.RuleStorageService`     | 保持不变 | P2    |
| **设备模块**                                        |                                       |      |       |
| `contextstate.service.DeviceStateManager`       | `device.state.StateManager`           | 重命名  | P1    |
| `contextstate.model.DevicePointState`           | `device.state.DevicePointState`       | 保持不变 | P1    |
| `contextstate.model.StateConditionMonitor`      | `device.state.StateConditionMonitor`  | 保持不变 | P2    |
| `core.model.DevicePointRef`                     | `device.state.DevicePointRef`         | 保持不变 | P2    |
| `contextstate.model.StateChangeEvent`           | `device.event.StateChangeEvent`       | 保持不变 | P3    |
| **动作模块**                                        |                                       |      |       |
| `core.action.ActionExecutorService`             | `action.executor.ActionExecutor`      | 重命名  | P1    |
| **触发模块**                                        |                                       |      |       |
| `scheduler.service.TimeTriggerService`          | `trigger.time.TimeScheduler`          | 重命名  | P2    |
| `trigger.time.TimeConditionEvaluator`           | `trigger.time.TimeConditionEvaluator` | 保持不变 | P2    |
| `trigger.time.GlobalCalendar`                   | `trigger.time.GlobalCalendar`         | 保持不变 | P3    |
| `core.facts.EnhancedFactsBuilder`               | `trigger.facts.FactsBuilder`          | 重命名  | P2    |
| **传输模块**                                        |                                       |      |       |
| `transport.listener.MqttMessageListener`        | `transport.mqtt.MqttListener`         | 重命名  | P3    |
| **公共模块**                                        |                                       |      |       |
| `common.operator.Operators`                     | `common.operator.Operators`           | 保持不变 | P3    |
| `common.operator.ValueComparator`               | `common.operator.ValueComparator`     | 保持不变 | P3    |
| `config.SystemContextService`                   | `common.context.SystemContextService` | 保持不变 | P3    |
| `config.RuleEngineConfig`                       | `common.config.RuleEngineConfig`      | 保持不变 | P3    |

## 🚀 分阶段实施计划

### 阶段0：准备工作

**目标**：备份现有代码，创建新包结构框架
**时间**：0.5天

1. **备份现有代码** 已完成
   
   ```bash
   # 在文件系统中重命名文件夹
   mv src/main/java/com/inxaiot/ruleengine src/main/java/com/inxaiot/re
   ```

2. **创建新包结构目录**
   
   ```
   mkdir -p src/main/java/com/inxaiot/ruleengine/rule/{definition,engine,adapter,analyzer,storage}
   mkdir -p src/main/java/com/inxaiot/ruleengine/device/{state,event}
   mkdir -p src/main/java/com/inxaiot/ruleengine/action/executor
   mkdir -p src/main/java/com/inxaiot/ruleengine/trigger/{time,event,facts}
   mkdir -p src/main/java/com/inxaiot/ruleengine/transport/{mqtt,rest}
   mkdir -p src/main/java/com/inxaiot/ruleengine/common/{config,operator,context}
   ```

3. **更新pom.xml和配置文件**
   
   - 暂时忽略编译错误
   - 更新Spring配置中的包扫描路径

### 阶段1：核心规则模块迁移

**目标**：迁移规则定义和引擎核心
**时间**：1天
**优先级**：P1

1. **迁移规则定义模型** (rule.definition)
   
   - RuleDefinition.java
   - TriggerCondition.java  
   - DeviceCondition.java
   - ActionDefinition.java
   - TimeCondition.java

2. **迁移规则引擎核心** (rule.engine)
   
   - RuleEngineService.java (重命名)
   - RuleEngineServiceImpl.java (重命名)

3. **更新import语句**
   
   - 更新所有引用这些类的import语句
   - 更新Spring配置中的bean定义

4. **功能测试**
   
   - 编译通过
   - 基本规则加载测试

### 阶段2：设备状态和动作模块迁移

**目标**：迁移设备状态管理和动作执行
**时间**：1天
**优先级**：P1

1. **迁移设备状态模块** (device.state)
   
   - StateManager.java (重命名自DeviceStateManager)
   - DevicePointState.java
   - DevicePointRef.java

2. **迁移动作执行模块** (action.executor)
   
   - ActionExecutor.java (重命名自ActionExecutorService)

3. **更新依赖关系**
   
   - 更新RuleEngineService中的依赖注入
   - 更新相关的import语句

4. **功能测试**
   
   - 设备状态管理测试
   - 动作执行测试

### 阶段3：规则适配和分析模块迁移

**目标**：迁移规则适配器和分析器
**时间**：0.5天  
**优先级**：P2

1. **迁移规则适配模块** (rule.adapter)
   
   - RuleAdapterService.java

2. **迁移规则分析模块** (rule.analyzer)  
   
   - DependencyAnalyzer.java (重命名)

3. **迁移规则存储模块** (rule.storage)
   
   - RuleStorageService.java

4. **功能测试**
   
   - 多条件规则测试
   - 规则依赖分析测试

### 阶段4：触发模块迁移

**目标**：迁移时间触发和Facts构建
**时间**：0.5天
**优先级**：P2

1. **迁移时间触发模块** (trigger.time)
   
   - TimeScheduler.java (重命名自TimeTriggerService)
   - TimeConditionEvaluator.java
   - GlobalCalendar.java

2. **迁移Facts构建模块** (trigger.facts)
   
   - FactsBuilder.java (重命名自EnhancedFactsBuilder)

3. **功能测试**
   
   - 时间驱动规则测试
   - 多条件Facts构建测试

### 阶段5：传输和公共模块迁移

**目标**：迁移MQTT监听和公共组件
**时间**：0.5天
**优先级**：P3

1. **迁移传输模块** (transport.mqtt)
   
   - MqttListener.java (重命名)

2. **迁移公共模块** (common.*)
   
   - Operators.java
   - ValueComparator.java  
   - SystemContextService.java
   - ThreadPoolConfig.java

3. **功能测试**
   
   - MQTT消息接收测试
   - 端到端集成测试

### 阶段6：清理和优化

**目标**：清理备份代码，优化新结构
**时间**：0.5天

1. **全面测试**
   
   - 运行所有单元测试
   - 运行集成测试
   - 性能测试

2. **清理备份代码**
   
   ```bash
   rm -rf src/main/java/com/inxaiot/re
   ```

3. **文档更新**
   
   - 更新README.md
   - 更新API文档
   - 更新架构文档

## ⚠️ 风险控制

### 回滚方案

如果迁移过程中出现问题：

```bash
# 删除新包结构
rm -rf src/main/java/com/inxaiot/ruleengine

# 恢复备份
mv src/main/java/com/inxaiot/re src/main/java/com/inxaiot/ruleengine
```

### 测试策略

- 每个阶段完成后必须通过编译
- 每个阶段完成后运行相关功能测试
- 保持现有测试用例不变，只更新import路径

### 依赖管理

- 使用IDE的重构工具辅助import更新
- 分模块逐步更新，避免大范围修改
- 保持Spring配置的向后兼容性

## 📊 预期收益

### 短期收益

- 包结构清晰，职责明确
- 新人更容易理解代码结构
- 减少类查找时间

### 长期收益

- 更好的可维护性
- 更容易进行模块化测试
- 支持未来的微服务拆分
- 更好的代码复用性

## 🎯 成功标准

1. **编译成功**：所有代码编译通过，无错误
2. **功能完整**：所有现有功能正常工作
3. **测试通过**：所有单元测试和集成测试通过
4. **性能保持**：性能指标不低于重构前
5. **文档更新**：相关文档已更新

---

**总预计时间**：3-4天
**建议实施时间**：在功能相对稳定期进行
**实施人员**：1-2名熟悉项目的开发人员
