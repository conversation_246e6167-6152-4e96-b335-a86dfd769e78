好的，我已经仔细回顾了我们所有的对话上下文和您提供的《需求背景.md》文档。基于这些信息，我为您准备了一份技术方案建议，重点关注边缘规则引擎的实现，并涵盖屏端和业务服务端的关联部分。

**整体架构回顾**

系统将采用三层架构：

1. **屏端 (Screen End):** 用户交互界面，用于定义和配置场景规则。
2. **业务服务端 (Business Server):** 规则的中央存储、管理、分解和分发中心。
3. **规则引擎 (Rule Engine):** 部署在边缘服务器，负责本地执行分解后的原子化规则，响应设备事件和时间条件。

---

## 技术方案建议

### 1. 屏端 (Screen End)

- **核心职责:** 提供直观的用户界面，供实施或业务人员为特定楼宇区域配置复杂的联动控制规则。
- **主要功能:**
  - **场景与模式定义:** 支持创建如“日常模式”、“情景模式”等。
  - **规则配置矩阵:**
    - **纵坐标 (时间点):** 用户可自定义业务相关的时间节点 (如白班节点、夜班节点)。
    - **横坐标 (区域):** 用户可定义楼宇功能区 (如护士站、会议室)。
    - **交叉点配置:** 在每个时间点与区域的交叉处，配置该时间段和区域下生效的设备规则。
  - **时间段定义:**
    - 支持选择日历概念 (如工作日、夏季) 并可编辑。
    - 支持选择具体的时:分起止时间。
    - 支持多个时间段的“或”逻辑组合。
    - **个性化日历:** 允许用户在标准日历（如工作日，已包含法定节假日概念）基础上，进行**日期例外设置**：
      - **排除日期:** 将某些原本符合条件的日期（如特定工作日）排除在此规则的生效范围之外。
      - **包含日期:** 将某些原本不符合条件的日期（如特定周末）包含在此规则的生效范围之内。
  - **设备规则配置 (按设备类型):**
    - **条件匹配逻辑:** 支持“全部满足 (AND)”或“任意满足 (OR)”一组子条件。
    - **条件类型:**
      - 定时开/关。
      - 感应开关 (如“无人15分钟关”)，涉及状态持续时间判断。
      - 智能开关 (如“照度>xxx关”、“温度<xxx开”)，涉及传感器阈值判断。
    - **动作配置:**
      - 设备控制指令 (开、关、设定值如空调温度、风速、模式等)。
  - **数据传输:** 屏端配置完成后，将结构化的规则数据 (建议使用JSON格式) 发送到业务服务端。此数据需完整包含时间点、区域、设备规则、所有时间参数（包括Cron表达式的要素、季节起止、以及个性化日历的`includeDates`和`excludeDates`列表）。

### 2. 业务服务端 (Business Server)

- **核心职责:** 作为规则的“大脑”，集中处理、存储、分解规则，并将其同步到相应的边缘规则引擎。
- **主要功能:**
  - **规则统一存储:** 接收来自所有屏端的规则配置，持久化存储 (建议使用关系型数据库如MySQL)。
  - **规则分解与转换:**
    - 将屏端的高层语义化配置（如“夏季工作日8-10点，护士站的灯A，无人15分钟关”）转换成边缘引擎能够直接执行的、更细粒度的“原子化规则定义”。
    - **`RuleDefinition` POJO生成:** 这是关键输出。每条原子化规则对应一个`RuleDefinition`对象，包含执行所需的所有信息（见边缘规则引擎部分详述）。
    - **时间条件处理:** 业务服务端将屏端的复杂时间描述（如“夏季”、“工作日”、自定义的例外日期、时分秒）转换为`RuleDefinition`中结构化的时间条件对象。
    - **多设备动作处理:** 未来如果一个屏端规则涉及对一个区域内多个设备或一类设备的操作，业务服务端负责将其分解成针对每个具体设备的原子化规则指令。
  - **全局配置管理与下发:**
    - 管理全局性的日历信息，如“法定节假日列表”、“工作日周期”、“季节标准起止日期”，并提供API供边缘引擎获取或由服务端主动推送更新。
  - **API接口:**
    - **对屏端:** 提供接收规则配置的接口。
    - **对边缘规则引擎:**
      - `POST /api/engine/rules`: 推送（新增/更新）规则定义到指定边缘引擎。
      - `DELETE /api/engine/rules/{ruleId}`: 删除边缘引擎的规则。
      - `GET /api/engine/rules`: （可选）边缘引擎启动时拉取规则。
      - `GET /api/engine/config/calendar`: 边缘引擎获取全局日历。
  - **管理与监控:** (可选) 提供后台管理界面，用于查看所有规则、边缘引擎状态、日志聚合等。

### 3. 规则引擎 (Rule Engine)

基于Java和Easy Rules，采用**单一工程、清晰包（Package）结构**的设计。

- **技术栈:** Java, Easy Rules, SQLite (本地存储), MyBatis (数据访问), (可选) Quartz Core (Cron表达式解析)。

- **工程结构 (包):**
  
  - `com.inxaiot.ruleengine.app`: 主应用启动、配置、服务组装。
  
  - `com.inxaiot.ruleengine.api`: 接收业务服务端指令的API接口（如基于Spring Boot的Controller）。
  
  - `com.inxaiot.ruleengine.core`: 规则引擎核心。
    
    - `definition`: `RuleDefinition.java` POJO类，包含所有从业务服务端接收到的原子化规则信息。
      
      Java
      
      ```
      package com.inxaiot.ruleengine.core.definition;
      
      import java.time.LocalDate;
      import java.util.List;
      import java.util.Map;
      
      public class RuleDefinition {
          private String ruleId;          // 规则唯一ID
          private String ruleName;        // 规则名称 (可选, 便于理解)
          private String targetDeviceId;  // 主要目标设备ID (如果规则针对单个设备)
          private String targetDeviceType; // (可选) 用于筛选或特定逻辑
          private String bizId            //规则所属外部业务唯一标识符
          private int priority = 1;       // 规则优先级 (Easy Rules支持)
          private boolean enabled = true; // 规则是否启用
      
          private List<TimeCondition> timeConditions;         // 时间条件
          private TriggerConditions triggerConditions;   // 设备触发条件
          private List<ActionDefinition> actions;        // 动作列表
      
          // Getters and Setters
          // ...
      }
      
      // --- 时间条件 ---
      class TimeCondition {
          private List<String> timeCronExpressions; // 例如: ["0 0 8-10 ? *", "0 0 14-16 ? *"]
          private List<String> workDays;            // 例如：Mon,Tue、Wed
          private String seanson                    // 如 summer、winter、all，由全局日历表定义起止时间
          private List<LocalDate> includeDates; // 个性化日历: 强制生效的日期列表
          private List<LocalDate> excludeDates; // 个性化日历: 强制不生效的日期列表
      
          // Getters and Setters
          // ...
      }
      
      // --- 设备触发条件 ---
      class TriggerConditions {
          public enum MatchLogic { ALL, ANY } // AND, OR
          private MatchLogic logic = MatchLogic.ALL;
          private List<DeviceCondition> conditions;
      
          // Getters and Setters
          // ...
      }
      
      class DeviceCondition {
          private String sourceDeviceId;  // 条件数据来源设备ID (可能与targetDeviceId不同)
          private String pointId;         // 监控的设备点位ID (如 "illuminance", "occupancy_status")
          private String operator;        // 操作符 (如 "EQUALS", "GREATER_THAN", "LESS_THAN", "NOT_EQUALS", "UNOCCUPIED_FOR_MINUTES")
          private Object value;           // 比较值 (如 "UNOCCUPIED", 500, true)
          private long durationMinutes;   // 持续时间 (用于 "UNOCCUPIED_FOR_MINUTES" 等操作符)
      
          // Getters and Setters
          // ...
      }
      
      // --- 动作定义 ---
      class ActionDefinition {
          private String actionType;      // 动作类型 (如 "DEVICE_CONTROL", "SEND_MESSAGE", "CALL_API")
          private String targetDeviceId;  // 动作执行的目标设备ID (可能与规则的targetDeviceId相同或不同)
          private String pointIdToActOn;  // (用于DEVICE_CONTROL) 执行动作的点位
          private Object actionValue;     // (用于DEVICE_CONTROL) 设定的值
          private Map<String, Object> params; // (用于其他动作类型) 其他参数，如消息内容、API地址、空调详细设置等
      
          // Getters and Setters
          // ...
      }
      
      // --- 全局日志配置 --- 
      }
      ```
    
    - `mapper.RuleAdapter.java`: 将`RuleDefinition`动态转换为Easy Rules的`org.jeasy.rules.api.Rule`对象。
      
      - `@Condition`方法会结合设备事件（来自`Facts`）和时间条件（调用`scheduler.TimeConditionEvaluator`）进行判断。
    
    - `engine.RuleEngineService.java`: 封装`DefaultRulesEngine`，提供处理事实、触发规则的方法。
    
    - `action.ActionExecutorService.java`: 执行规则定义的动作，通过`transport`模块与设备交互。
  
  - `com.inxaiot.ruleengine.scheduler`: 时间处理。
    
    - `TimeConditionEvaluator.java`: **按需评估服务。** 不进行周期性轮询激活规则，而是在`RuleAdapter`的`@Condition`方法中被调用，传入特定规则的`TimeCondition`进行即时判断。
      
      Java
      
      ```
      package com.inxaiot.ruleengine.scheduler.service;
      
      import com.inxaiot.ruleengine.core.definition.TimeConditions;
      import com.inxaiot.ruleengine.storage.GlobalCalendarService; // 用于获取全局日历
      import org.quartz.CronExpression; // 引入Quartz Core库进行Cron表达式解析
      import org.slf4j.Logger;
      import org.slf4j.LoggerFactory;
      import org.springframework.beans.factory.annotation.Autowired;
      import org.springframework.stereotype.Service;
      
      import java.text.ParseException;
      import java.time.DayOfWeek;
      import java.time.LocalDate;
      import java.time.LocalDateTime;
      import java.time.ZoneId;
      import java.util.Date;
      import java.util.List;
      import java.util.Set;
      
      @Service
      public class TimeConditionEvaluator {
          private static final Logger logger = LoggerFactory.getLogger(TimeConditionEvaluator.class);
          private final GlobalConfigStorageService globalConfigStorageService;
          private GlobalCalendar globalCalendar;
      
          @Autowired
          public TimeConditionEvaluator(GlobalConfigStorageService globalConfigStorageService) {
              this.globalConfigStorageService = globalConfigStorageService;
              refreshGlobalCalendar(); // 初始化加载
          }
      
          public void refreshGlobalCalendar() {
              this.globalCalendar= globalConfigStorageService.loadGlobalCalendar(); // 从存储层加载
              logger.info("Global calendar refreshed, count: {}", globalCalendar.toJson();
          }
      
          public boolean isTimeConditionMet(List<TimeCondition> conditions, LocalDateTime evaluationTime) {
              if (conditions == null || conditions.isEmpty() {
                  return true; // 没有时间条件，视为满足
              }
              LocalDate evaluationDate = evaluationTime.toLocalDate();
      
              for(TimeCondition condition:conditions)
      
              // 1. 检查强制包含的日期 (优先级最高)
              if (condition.getIncludeDates() != null && condition.getIncludeDates().contains(evaluationDate)) {
                  return checkCronExpressions(condition.getTimeCronExpressions(), evaluationTime); // 仍需满足当天的时间段
              }
      
              // 2. 检查强制排除的日期 (优先级次高)
              if (condition.getExcludeDates() != null && condition.getExcludeDates().contains(evaluationDate)) {
                  return false;
              }
      
              // 3. 检查季节性/特定日期范围,需要拿全局日历globalCalendar判断
              // TODO
      
              // 4. 检查工作日
              // TODO
      
              // 5. 检查Cron表达式定义的时间段
              if (!checkCronExpressions(conditions.getCronExpressions(), evaluationTime)) {
                  return false;
              }
              return true; // 所有时间条件均满足
          }
      
          private boolean checkCronExpressions(List<String> cronExpressions, LocalDateTime evaluationTime) {
              if (cronExpressions == null || cronExpressions.isEmpty()) {
                  return true; // 没有指定具体时间段，则视为全天满足（如果日期已满足）
              }
              Date dateToTest = Date.from(evaluationTime.atZone(ZoneId.systemDefault()).toInstant());
              for (String cronStr : cronExpressions) {
                  try {
                      CronExpression cron = new CronExpression(cronStr);
                      if (cron.isSatisfiedBy(dateToTest)) {
                          return true; // 满足任意一个Cron表达式即可
                      }
                  } catch (ParseException e) {
                      logger.error("Failed to parse cron expression: {}", cronStr, e);
                      // 根据策略，可以选择忽略错误表达式或直接返回false
                  }
              }
              return false; // 所有Cron表达式都不满足
          }
      }
      ```
    
    - `GlobalCalendarService.java`: 从`storage`模块加载或通过API接收并缓存全局日历。

- `com.inxaiot.ruleengine.contextstate`: 状态管理。
  
  - `DeviceStateManager.java`: 处理如“无人15分钟”这类需要跨事件维护状态的逻辑。
    - 内部使用`Map<String_deviceId, OccupancyState>`，`OccupancyState`包含`isOccupied`、`lastBecameUnoccupiedTimestamp`、`ScheduledFuture<?> timeoutTask` (用于取消)。
    - 使用`java.util.concurrent.ScheduledExecutorService`来处理15分钟延迟。
    - 当“无人”事件到达，记录时间戳，启动一个15分钟的`ScheduledFuture`任务。
    - 当“有人”事件到达，更新状态，并取消之前对应的`ScheduledFuture`任务。
    - 当延迟任务执行时，再次确认设备仍为无人状态，然后构造一个特定的`Fact` (如`UnoccupiedTimeoutFact(deviceId)`)，调用`RuleEngineService`。
  - `SystemContextService.java`: 存储从业务服务端下发的其他上下文信息 (如楼宇模式)。

- `com.inxaiot.ruleengine.transport`: 数据接入与输出 (如MQTT Listener/Publisher)。
  
  - 负责接收设备数据，转换为内部`Fact`对象。
  - 调用`DeviceStateManager`更新设备状态（如果相关）。
  - 将`Fact`和必要的上下文信息（从`SystemContextService`获取）传递给`RuleEngineService`。
  - 负责执行`ActionExecutorService`发出的设备控制指令。

- `com.inxaiot.ruleengine.storage`: 本地存储。
  
  - `RuleRepository.java` (接口) 和 `SqliteRuleRepositoryImpl.java` (使用SQLite和MyBatis实现)。
    - 存储`RuleDefinition` (可序列化为JSON存入TEXT字段，或拆分成结构化表)。
    - 存储全局配置如全局日历列表。
    - 存储设备基础信息。


  
  - **测试策略:**

- **单元测试:** 对各个服务（如`TimeConditionEvaluator`, `RuleAdapter`, `DeviceStateManager`）和核心逻辑进行单元测试。Easy Rules的规则也可以进行单元测试。

- **集成测试:** 测试模块间的交互，例如：
  
  - API接收规则 -> 存储 -> 加载 -> 转换为Easy Rules对象。
  - 模拟设备事件 -> 状态更新 -> 规则匹配 -> 动作执行。

- **模拟屏端配置:** 创建模拟的屏端配置文件，通过业务服务端的（模拟）分解逻辑，生成`RuleDefinition`，再由边缘引擎API推送到边缘引擎进行测试。

**流程图/时序图示意 (文字描述):**

1. **高层组件交互图:**
   
   代码段

```mermaid
graph LR
    Screen[屏端 UI] -- JSON配置 --> BusinessServer[业务服务端]
    BusinessServer -- 原子化RuleDefinition推送 --> RuleAPI[边缘引擎API模块]
    RuleAPI-- 存储/更新 --> Storage[存储模块 SQLite]
    Storage -- 加载规则定义 --> Core[核心规则模块]
    Transport[数据传输模块 MQTT] -- 设备数据 --> Core
    Transport -- 设备数据 --> ContextState[上下文状态模块]
    ContextState -- 状态事实 --> Core
    Core -- 时间条件判断 --> Scheduler[时间调度模块]
    Core -- 执行规则 EasyRules --> CoreActions[核心动作执行]
    CoreActions -- 设备指令 --> Transport
```

2. **边缘引擎处理设备事件时序图:**
   
   代码段

```mermaid
sequenceDiagram
   participant Device
   participant Transport as 数据传输模块
   participant ContextState as 上下文状态模块
   participant CoreEngine as 核心规则引擎服务
   participant EasyRulesLib as Easy Rules库
   participant Scheduler as 时间条件评估服务
   participant CoreActions as 核心动作执行

   Device->>Transport: 上报传感器数据 (如: 无人)
   Transport->>ContextState: 更新设备状态 (如: 标记无人, 启动15分钟定时器)
   Transport->>CoreEngine: processFacts(原始Facts)
   CoreEngine->>EasyRulesLib: 获取相关规则实例 (RuleAdapter转换)
   loop 对每个规则
       EasyRulesLib->>RuleAdapterCondition: @Condition check
       RuleAdapterCondition->>Scheduler: isTimeConditionMet(rule.timeConditions)?
       Scheduler-->>RuleAdapterCondition: true/false
       RuleAdapterCondition-->>EasyRulesLib: (设备条件 && 时间条件) 结果
   end
   EasyRulesLib-->>CoreEngine: 触发的规则列表
   CoreEngine->>CoreActions: 执行动作
   CoreActions->>Transport: 发送控制指令
   Transport->>Device: 控制指令
```

3. **“无人15分钟”场景时序图:**
   
   代码段

```mermaid
sequenceDiagram
   participant Device
   participant Transport as 数据传输模块
   participant ContextState as 上下文状态模块
   participant JavaScheduler as ScheduledExecutorService
   participant CoreEngine as 核心规则引擎服务

   Device->>Transport: 上报传感器数据 (无人)
   Transport->>ContextState: updateStateToUnoccupied(deviceId)
   ContextState->>JavaScheduler: schedule(task_checkTimeout_deviceId, 15min)
   Note over ContextState: 存储 lastUnoccupiedTimestamp 和 ScheduledFuture

   opt 15分钟内有人信号到达
       Device->>Transport: 上报传感器数据 (有人)
       Transport->>ContextState: updateStateToOccupied(deviceId)
       ContextState->>JavaScheduler: cancel(task_checkTimeout_deviceId)
   end

   opt 15分钟后 (定时任务执行)
       JavaScheduler->>ContextState: task_checkTimeout_deviceId.run()
       ContextState->>ContextState: if stillUnoccupiedAndTimeout(deviceId)
       ContextState->>CoreEngine: processFacts(UnoccupiedTimeoutFact(deviceId))
       Note over CoreEngine: 后续流程类似设备事件处理
   end
```

---

**不确定或需进一步明确的点 (提问):**

1. **多设备动作的分解粒度:** 文档中提到“未来不排除可能对多个设备执行（由业务服务端拆解成具体设备？比如空间范围内的设备或者某一类设备）”。当前方案假设业务服务端会将其彻底分解为针对**单一目标设备**的`RuleDefinition`。如果边缘引擎需要解释“区域内所有照明设备”这样的目标，那么边缘引擎的`core.action.ActionExecutorService`和`storage`（用于查询区域内的设备）会需要更复杂的逻辑。**请问，初期是否可以假设业务服务端会将规则目标明确到具体的`targetDeviceId`？**

此方案旨在平衡您对功能、性能、资源、开发时间和团队经验的各项要求。通过将复杂的时间逻辑和状态管理封装在Java辅助组件中，可以让Easy Rules专注于其核心的条件判断和动作触发，从而降低整体实现难度。
