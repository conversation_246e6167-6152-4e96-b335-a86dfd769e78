package com.inxaiot.ruleengine;

import com.inxaiot.ruleengine.core.definition.*;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.device.state.DevicePointState;
import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.TimeRange;

import java.time.LocalDate;
import java.util.*;

/**
 * 测试数据工厂类
 * 提供各种测试用的数据对象
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class TestDataFactory {

    /**
     * 创建简单的事件驱动规则
     */
    public static RuleDefinition createSimpleEventDrivenRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId("test_rule_001");
        rule.setRuleName("测试规则-温度控制");
        rule.setTargetDeviceId("hvac_001");
        rule.setBizId("test_biz");
        rule.setPriority(1);
        rule.setEnabled(true);
        rule.setTriggerType(RuleDefinition.TriggerType.EVENT_DRIVEN);

        // 设置时间条件
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        timeCondition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        timeCondition.setLogic(TimeCondition.TimeLogic.AND);
        rule.setTimeConditions(Arrays.asList(timeCondition));

        // 设置触发条件
        TriggerCondition triggerCondition = new TriggerCondition();
        triggerCondition.setLogic(TriggerCondition.MatchLogic.ALL);

        DeviceCondition deviceCondition = new DeviceCondition();
        deviceCondition.setSourceDeviceId("temp_sensor_001");
        deviceCondition.setPointId("temperature");
        deviceCondition.setOperator(Operators.GREATER_THAN);
        deviceCondition.setValue(28.0);
        deviceCondition.setDurationMinutes(5);

        triggerCondition.setConditions(Arrays.asList(deviceCondition));
        rule.setTriggerCondition(triggerCondition);

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        action.setTargetDeviceId("hvac_001");
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        params.put("temperature", 24);
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }

    /**
     * 创建时间驱动规则
     */
    public static RuleDefinition createTimeDrivenRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId("test_rule_002");
        rule.setRuleName("测试规则-定时开灯");
        rule.setTargetDeviceId("light_001");
        rule.setBizId("test_biz");
        rule.setPriority(2);
        rule.setEnabled(true);
        rule.setTriggerType(RuleDefinition.TriggerType.TIME_DRIVEN);

        // 设置时间条件
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        timeCondition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        timeCondition.setLogic(TimeCondition.TimeLogic.AND);
        rule.setTimeConditions(Arrays.asList(timeCondition));

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        action.setTargetDeviceId("light_001");
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }

    /**
     * 创建多条件规则
     */
    public static RuleDefinition createMultiConditionRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId("test_rule_003");
        rule.setRuleName("测试规则-多条件");
        rule.setTargetDeviceId("hvac_002");
        rule.setBizId("test_biz");
        rule.setPriority(1);
        rule.setEnabled(true);
        rule.setTriggerType(RuleDefinition.TriggerType.EVENT_DRIVEN);

        // 设置触发条件
        TriggerCondition triggerCondition = new TriggerCondition();
        triggerCondition.setLogic(TriggerCondition.MatchLogic.ALL);

        // 温度条件
        DeviceCondition tempCondition = new DeviceCondition();
        tempCondition.setSourceDeviceId("temp_sensor_001");
        tempCondition.setPointId("temperature");
        tempCondition.setOperator(Operators.GREATER_THAN);
        tempCondition.setValue(30.0);
        tempCondition.setDurationMinutes(10);

        // 占用条件
        DeviceCondition occupancyCondition = new DeviceCondition();
        occupancyCondition.setSourceDeviceId("occupancy_sensor_001");
        occupancyCondition.setPointId("occupancy_status");
        occupancyCondition.setOperator(Operators.EQUALS);
        occupancyCondition.setValue("OCCUPIED");
        occupancyCondition.setDurationMinutes(0);

        triggerCondition.setConditions(Arrays.asList(tempCondition, occupancyCondition));
        rule.setTriggerCondition(triggerCondition);

        // 设置动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        action.setTargetDeviceId("hvac_002");
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        params.put("temperature", 22);
        action.setParams(params);
        action.setEnabled(true);

        rule.setActions(Arrays.asList(action));

        return rule;
    }

    /**
     * 创建设备点位状态
     */
    public static DevicePointState createDevicePointState(String deviceId, String pointId, Object value) {
        DevicePointState state = new DevicePointState(deviceId, pointId);
        state.updateValue(value, "DOUBLE");
        return state;
    }

    /**
     * 创建状态条件
     */
    public static StateCondition createStateCondition(String deviceId, String pointId, String operator, Object value, long durationMinutes) {
        StateCondition condition = new StateCondition();
        condition.setConditionId(UUID.randomUUID().toString());
        condition.setDeviceId(deviceId);
        condition.setPointId(pointId);
        condition.setOperator(operator);
        condition.setValue(value);
        condition.setDurationMinutes(durationMinutes);
        condition.setEnabled(true);
        return condition;
    }

    /**
     * 创建状态变化事件
     */
    public static StateChangeEvent createStateChangeEvent(String deviceId, String pointId, Object oldValue, Object newValue) {
        return StateChangeEvent.createValueChangeEvent(deviceId, pointId, oldValue, newValue, "DOUBLE");
    }

    /**
     * 创建全局日历
     */
    public static GlobalCalendar createGlobalCalendar() {
        GlobalCalendar calendar = new GlobalCalendar();
        calendar.setHolidays(Arrays.asList(
            LocalDate.of(2024, 1, 1),
            LocalDate.of(2024, 5, 1),
            LocalDate.of(2024, 10, 1)
        ));
        
        TimeRange summerTime = new TimeRange();
        summerTime.setStartDate(LocalDate.of(2024, 6, 1));
        summerTime.setEndDate(LocalDate.of(2024, 8, 31));
        calendar.setSummerTime(summerTime);
        
        TimeRange winterTime = new TimeRange();
        winterTime.setStartDate(LocalDate.of(2024, 12, 1));
        winterTime.setEndDate(LocalDate.of(2025, 2, 28));
        calendar.setWinterTime(winterTime);
        
        return calendar;
    }
}
