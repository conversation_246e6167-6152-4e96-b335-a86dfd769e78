package com.inxaiot.ruleengine.core;

import com.inxaiot.ruleengine.device.state.DevicePointState;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.core.analyzer.DependencyAnalyzer;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.core.analyzer.FactsBuilder;
import com.inxaiot.ruleengine.device.state.DevicePointRef;
import org.jeasy.rules.api.Facts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 多条件规则测试
 * 验证增强的Facts构建器能够正确聚合多个设备状态
 */
@ExtendWith(MockitoExtension.class)
public class MultiConditionRuleTest {
    
    @Mock
    private StateManager stateManager;

    private DependencyAnalyzer dependencyAnalyzer;
    private FactsBuilder factsBuilder;
    
    @BeforeEach
    void setUp() {
        dependencyAnalyzer = new DependencyAnalyzer();
        factsBuilder = new FactsBuilder();

        // 使用反射设置私有字段（在实际应用中通过Spring注入）
        try {
            java.lang.reflect.Field stateManagerField = FactsBuilder.class.getDeclaredField("stateManager");
            stateManagerField.setAccessible(true);
            stateManagerField.set(factsBuilder, stateManager);

            java.lang.reflect.Field dependencyAnalyzerField = FactsBuilder.class.getDeclaredField("dependencyAnalyzer");
            dependencyAnalyzerField.setAccessible(true);
            dependencyAnalyzerField.set(factsBuilder, dependencyAnalyzer);
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup test", e);
        }
    }
    
    @Test
    void testRuleDependencyAnalysis() {
        // 创建一个多条件规则：温度 > 100 AND CO2 > 25%
        RuleDefinition rule = createMultiConditionRule();
        
        // 分析规则依赖
        Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);
        
        // 验证依赖分析结果
        assertEquals(2, dependencies.size(), "应该识别出2个设备点位依赖");
        
        boolean hasTemperatureDep = dependencies.stream()
                .anyMatch(ref -> "sensor001".equals(ref.getDeviceId()) && "temperature".equals(ref.getPointId()));
        boolean hasCO2Dep = dependencies.stream()
                .anyMatch(ref -> "sensor002".equals(ref.getDeviceId()) && "co2".equals(ref.getPointId()));
        
        assertTrue(hasTemperatureDep, "应该包含温度传感器依赖");
        assertTrue(hasCO2Dep, "应该包含CO2传感器依赖");
    }
    
    @Test
    void testEnhancedFactsBuilding() {
        // 创建多条件规则
        RuleDefinition rule = createMultiConditionRule();
        
        // 模拟设备状态
        DevicePointState tempState = createDeviceState("sensor001", "temperature", 105.0, "DOUBLE");
        DevicePointState co2State = createDeviceState("sensor002", "co2", 30.0, "DOUBLE");
        
        // 配置mock返回
        when(stateManager.getDevicePointState("sensor001", "temperature")).thenReturn(tempState);
        when(stateManager.getDevicePointState("sensor002", "co2")).thenReturn(co2State);
        
        // 构建Facts（模拟温度传感器触发）
        Facts facts = factsBuilder.buildCompleteFactsForRule(rule, "sensor001", "temperature", 105.0);
        
        // 验证Facts内容
        assertNotNull(facts, "Facts不应为null");
        
        // 验证触发信息
        assertEquals("sensor001", facts.get("triggeringDeviceId"));
        assertEquals("temperature", facts.get("triggeringPointId"));
        assertEquals(105.0, facts.get("triggeringValue"));
        
        // 验证聚合的设备状态
        assertEquals(105.0, facts.get("sensor001.temperature"), "应该包含温度传感器数据");
        assertEquals(30.0, facts.get("sensor002.co2"), "应该包含CO2传感器数据");
        
        // 验证时间戳信息
        assertNotNull(facts.get("sensor001.temperature_timestamp"));
        assertNotNull(facts.get("sensor002.co2_timestamp"));
        
        // 验证统计信息
        assertEquals(2, (Integer) facts.get("_stats_addedStates"), "应该添加了2个设备状态");
        assertEquals(0, (Integer) facts.get("_stats_expiredStates"), "应该没有过期状态");
        assertEquals(0, (Integer) facts.get("_stats_missingStates"), "应该没有缺失状态");
    }
    
    @Test
    void testFactsBuildingWithMissingState() {
        // 创建多条件规则
        RuleDefinition rule = createMultiConditionRule();
        
        // 只模拟温度传感器状态，CO2传感器状态缺失
        DevicePointState tempState = createDeviceState("sensor001", "temperature", 105.0, "DOUBLE");
        
        when(stateManager.getDevicePointState("sensor001", "temperature")).thenReturn(tempState);
        when(stateManager.getDevicePointState("sensor002", "co2")).thenReturn(null);
        
        // 构建Facts
        Facts facts = factsBuilder.buildCompleteFactsForRule(rule, "sensor001", "temperature", 105.0);
        
        // 验证结果
        assertEquals(105.0, facts.get("sensor001.temperature"), "应该包含温度传感器数据");
        assertNull(facts.get("sensor002.co2"), "CO2数据应该为null");
        assertTrue((Boolean) facts.get("sensor002.co2_missing"), "应该标记CO2状态缺失");
        
        // 验证统计信息
        assertEquals(1, (Integer) facts.get("_stats_addedStates"), "应该添加了1个设备状态");
        assertEquals(1, (Integer) facts.get("_stats_missingStates"), "应该有1个缺失状态");
    }
    
    /**
     * 创建多条件规则：温度 > 100 AND CO2 > 25%
     */
    private RuleDefinition createMultiConditionRule() {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId("fire_sprinkler_rule");
        rule.setRuleName("火灾预警喷淋系统");
        rule.setTriggerType(RuleDefinition.TriggerType.EVENT_DRIVEN);
        rule.setEnabled(true);

        // 创建设备条件
        // 温度条件：sensor001.temperature > 100
        DeviceCondition tempCondition = new DeviceCondition();
        tempCondition.setSourceDeviceId("sensor001");
        tempCondition.setPointId("temperature");
        tempCondition.setOperator("GREATER_THAN");
        tempCondition.setValue(100.0);
        tempCondition.setDataType("DOUBLE");

        // CO2条件：sensor002.co2 > 25
        DeviceCondition co2Condition = new DeviceCondition();
        co2Condition.setSourceDeviceId("sensor002");
        co2Condition.setPointId("co2");
        co2Condition.setOperator("GREATER_THAN");
        co2Condition.setValue(25.0);
        co2Condition.setDataType("DOUBLE");

        // 创建触发条件容器
        TriggerCondition triggerCondition = new TriggerCondition();
        triggerCondition.setLogic(TriggerCondition.MatchLogic.ALL); // AND逻辑
        triggerCondition.setConditions(Arrays.asList(tempCondition, co2Condition));

        rule.setTriggerCondition(triggerCondition);

        return rule;
    }
    
    /**
     * 创建设备状态
     */
    private DevicePointState createDeviceState(String deviceId, String pointId, Object value, String dataType) {
        DevicePointState state = new DevicePointState(deviceId, pointId);
        state.updateValue(value, dataType);
        return state;
    }
}
