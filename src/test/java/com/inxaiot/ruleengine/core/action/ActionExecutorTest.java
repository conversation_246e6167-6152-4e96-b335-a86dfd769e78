package com.inxaiot.ruleengine.core.action;

import com.inxaiot.ruleengine.RuleEngineTestConfig;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import org.jeasy.rules.api.Facts;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ActionExecutor 单元测试
 * 测试动作执行器的核心功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Import(RuleEngineTestConfig.class)
public class ActionExecutorTest {

    @Mock
    private ThreadPoolTaskExecutor actionExecutor;

    private ActionExecutor actionExecutorService;

    @BeforeEach
    void setUp() {
        actionExecutorService = new ActionExecutor();
        // 使用反射设置私有字段
        try {
            java.lang.reflect.Field field = ActionExecutor.class.getDeclaredField("actionExecutor");
            field.setAccessible(true);
            field.set(actionExecutorService, actionExecutor);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set actionExecutor", e);
        }
    }

    /**
     * 测试执行设备控制动作 - 正常流程
     */
    @Test
    void testExecuteDeviceControlAction_Success() {
        // Given
        String targetDeviceId = "hvac_001";
        ActionDefinition actionDef = createDeviceControlAction();
        Facts facts = createTestFacts();

        // 模拟异步执行器直接执行任务
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(actionExecutor).execute(any(Runnable.class));

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        verify(actionExecutor).execute(any(Runnable.class));
    }

    /**
     * 测试执行禁用的动作
     */
    @Test
    void testExecuteDisabledAction() {
        // Given
        String targetDeviceId = "hvac_001";
        ActionDefinition actionDef = createDeviceControlAction();
        actionDef.setEnabled(false); // 禁用动作
        Facts facts = createTestFacts();

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        // 禁用的动作不应该被执行
        verify(actionExecutor, never()).execute(any(Runnable.class));
    }

    /**
     * 测试执行空动作
     */
    @Test
    void testExecuteNullAction() {
        // Given
        String targetDeviceId = "hvac_001";
        ActionDefinition actionDef = null;
        Facts facts = createTestFacts();

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        // 空动作不应该被执行
        verify(actionExecutor, never()).execute(any(Runnable.class));
    }

    /**
     * 测试重复操作检测机制
     */
    @Test
    void testDuplicateOperationPrevention() {
        // Given
        String targetDeviceId = "hvac_001";
        ActionDefinition actionDef = createDeviceControlAction();
        Facts facts = createTestFacts();

        // 模拟异步执行器直接执行任务
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(actionExecutor).execute(any(Runnable.class));

        // When - 连续执行两次相同的动作
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then - 应该执行两次（第二次会被去重机制阻止，但这里我们只验证提交了两次任务）
        verify(actionExecutor, times(2)).execute(any(Runnable.class));
    }

    /**
     * 测试发送消息动作
     */
    @Test
    void testExecuteSendMessageAction() {
        // Given
        String targetDeviceId = "notification_service";
        ActionDefinition actionDef = createSendMessageAction();
        Facts facts = createTestFacts();

        // 模拟异步执行器直接执行任务
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(actionExecutor).execute(any(Runnable.class));

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        verify(actionExecutor).execute(any(Runnable.class));
    }

    /**
     * 测试API调用动作
     */
    @Test
    void testExecuteCallApiAction() {
        // Given
        String targetDeviceId = "api_service";
        ActionDefinition actionDef = createCallApiAction();
        Facts facts = createTestFacts();

        // 模拟异步执行器直接执行任务
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(actionExecutor).execute(any(Runnable.class));

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        verify(actionExecutor).execute(any(Runnable.class));
    }

    /**
     * 测试日志记录动作
     */
    @Test
    void testExecuteLogEventAction() {
        // Given
        String targetDeviceId = "log_service";
        ActionDefinition actionDef = createLogEventAction();
        Facts facts = createTestFacts();

        // 模拟异步执行器直接执行任务
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(actionExecutor).execute(any(Runnable.class));

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        verify(actionExecutor).execute(any(Runnable.class));
    }

    /**
     * 测试不支持的动作类型
     */
    @Test
    void testExecuteUnsupportedActionType() {
        // Given
        String targetDeviceId = "unknown_device";
        ActionDefinition actionDef = new ActionDefinition();
        actionDef.setActionType("UNSUPPORTED_ACTION");
        actionDef.setEnabled(true);
        actionDef.setParams(new HashMap<>());
        Facts facts = createTestFacts();

        // 模拟异步执行器直接执行任务
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run();
            return null;
        }).when(actionExecutor).execute(any(Runnable.class));

        // When
        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);

        // Then
        verify(actionExecutor).execute(any(Runnable.class));
    }

    /**
     * 测试动作执行异常处理
     */
    @Test
    void testExecuteActionWithException() {
        // Given
        String targetDeviceId = "hvac_001";
        ActionDefinition actionDef = createDeviceControlAction();
        Facts facts = createTestFacts();

        // 模拟异步执行器抛出异常
        doThrow(new RuntimeException("Executor rejected task"))
            .when(actionExecutor).execute(any(Runnable.class));

        // When & Then - 不应该抛出异常，应该被内部处理
        assertDoesNotThrow(() -> {
            actionExecutorService.executeAction(targetDeviceId, actionDef, facts);
        });

        verify(actionExecutor).execute(any(Runnable.class));
    }

    // 辅助方法：创建设备控制动作
    private ActionDefinition createDeviceControlAction() {
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.DEVICE_CONTROL);
        action.setTargetDeviceId("hvac_001");
        action.setEnabled(true);
        
        Map<String, Object> params = new HashMap<>();
        params.put("command", "turn_on");
        params.put("temperature", 24);
        action.setParams(params);
        
        return action;
    }

    // 辅助方法：创建发送消息动作
    private ActionDefinition createSendMessageAction() {
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.SEND_MESSAGE);
        action.setTargetDeviceId("notification_service");
        action.setEnabled(true);
        
        Map<String, Object> params = new HashMap<>();
        params.put("message", "Temperature alert");
        params.put("recipient", "<EMAIL>");
        action.setParams(params);
        
        return action;
    }

    // 辅助方法：创建API调用动作
    private ActionDefinition createCallApiAction() {
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.CALL_API);
        action.setTargetDeviceId("api_service");
        action.setEnabled(true);
        
        Map<String, Object> params = new HashMap<>();
        params.put("url", "http://api.example.com/alert");
        params.put("method", "POST");
        action.setParams(params);
        
        return action;
    }

    // 辅助方法：创建日志记录动作
    private ActionDefinition createLogEventAction() {
        ActionDefinition action = new ActionDefinition();
        action.setActionType(ActionDefinition.ActionTypes.LOG_EVENT);
        action.setTargetDeviceId("log_service");
        action.setEnabled(true);
        
        Map<String, Object> params = new HashMap<>();
        params.put("level", "INFO");
        params.put("message", "Rule triggered");
        action.setParams(params);
        
        return action;
    }

    // 辅助方法：创建测试Facts
    private Facts createTestFacts() {
        Facts facts = new Facts();
        facts.put("triggeredRuleId", "test_rule_001");
        facts.put("deviceId", "temp_sensor_001");
        facts.put("pointId", "temperature");
        facts.put("value", 30.0);
        return facts;
    }
}
