package com.inxaiot.ruleengine.core.adapter;

import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.operator.Operators;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DeviceCondition 单元测试
 * 重点测试新的通用状态持续时间检测机制
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
class DeviceConditionTest {
    
    @Test
    void testStatesKeepMinutesOperator() {
        // 测试新的通用状态持续时间操作符
        DeviceCondition condition = new DeviceCondition();
        condition.setOperator(Operators.Duration.STATES_KEEP_MINUTES);
        condition.setValue("OCCUPIED");
        condition.setDurationMinutes(30);
        
        assertTrue(condition.isDurationOperator());
        assertFalse(condition.isRangeOperator());
        assertEquals("STATES_KEEP_MINUTES", condition.getOperator());
        assertEquals("OCCUPIED", condition.getValue());
        assertEquals(30, condition.getDurationMinutes());
    }
    
    @Test
    void testBetweenOperators() {
        // 测试BETWEEN操作符
        DeviceCondition betweenCondition = new DeviceCondition();
        betweenCondition.setOperator(Operators.Range.BETWEEN);
        betweenCondition.setValue(20.0);
        betweenCondition.setUpperValue(30.0);
        
        assertTrue(betweenCondition.isRangeOperator());
        assertFalse(betweenCondition.isDurationOperator());
        assertEquals("BETWEEN", betweenCondition.getOperator());
        assertEquals(20.0, betweenCondition.getValue());
        assertEquals(30.0, betweenCondition.getUpperValue());
        
        // 测试NOT_BETWEEN操作符
        DeviceCondition notBetweenCondition = new DeviceCondition();
        notBetweenCondition.setOperator(Operators.Range.NOT_BETWEEN);
        notBetweenCondition.setValue(10.0);
        notBetweenCondition.setUpperValue(40.0);
        
        assertTrue(notBetweenCondition.isRangeOperator());
        assertFalse(notBetweenCondition.isDurationOperator());
        assertEquals("NOT_BETWEEN", notBetweenCondition.getOperator());
    }
    
    @Test
    void testOperatorConstants() {
        // 验证所有操作符常量
        assertEquals("EQUALS", Operators.Basic.EQUALS);
        assertEquals("NOT_EQUALS", Operators.Basic.NOT_EQUALS);
        assertEquals("GREATER_THAN", Operators.Basic.GREATER_THAN);
        assertEquals("GREATER_THAN_OR_EQUAL", Operators.Basic.GREATER_THAN_OR_EQUAL);
        assertEquals("LESS_THAN", Operators.Basic.LESS_THAN);
        assertEquals("LESS_THAN_OR_EQUAL", Operators.Basic.LESS_THAN_OR_EQUAL);
        assertEquals("CONTAINS", Operators.Text.CONTAINS);
        assertEquals("NOT_CONTAINS", Operators.Text.NOT_CONTAINS);
        assertEquals("BETWEEN", Operators.Range.BETWEEN);
        assertEquals("NOT_BETWEEN", Operators.Range.NOT_BETWEEN);
        assertEquals("STATES_KEEP_MINUTES", Operators.Duration.STATES_KEEP_MINUTES);
    }
    
    @Test
    void testTemperatureScenario() {
        // 测试温度持续时间场景：温度持续高于25度超过60分钟
        DeviceCondition tempCondition = new DeviceCondition();
        tempCondition.setSourceDeviceId("temp_sensor_001");
        tempCondition.setPointId("temperature");
        tempCondition.setOperator(Operators.Duration.STATES_KEEP_MINUTES);
        tempCondition.setValue(25.0);
        tempCondition.setDurationMinutes(60);
        tempCondition.setDataType("DOUBLE");
        
        assertEquals("temp_sensor_001", tempCondition.getSourceDeviceId());
        assertEquals("temperature", tempCondition.getPointId());
        assertEquals("STATES_KEEP_MINUTES", tempCondition.getOperator());
        assertEquals(25.0, tempCondition.getValue());
        assertEquals(60, tempCondition.getDurationMinutes());
        assertEquals("DOUBLE", tempCondition.getDataType());
        assertTrue(tempCondition.isDurationOperator());
    }
    
    @Test
    void testHumidityRangeScenario() {
        // 测试湿度范围场景：湿度在40%-60%之间
        DeviceCondition humidityCondition = new DeviceCondition();
        humidityCondition.setSourceDeviceId("humidity_sensor_001");
        humidityCondition.setPointId("humidity");
        humidityCondition.setOperator(Operators.Range.BETWEEN);
        humidityCondition.setValue(40.0);
        humidityCondition.setUpperValue(60.0);
        humidityCondition.setDataType("DOUBLE");
        
        assertEquals("humidity_sensor_001", humidityCondition.getSourceDeviceId());
        assertEquals("humidity", humidityCondition.getPointId());
        assertEquals("BETWEEN", humidityCondition.getOperator());
        assertEquals(40.0, humidityCondition.getValue());
        assertEquals(60.0, humidityCondition.getUpperValue());
        assertEquals("DOUBLE", humidityCondition.getDataType());
        assertTrue(humidityCondition.isRangeOperator());
    }
    
    @Test
    void testOccupancyScenario() {
        // 测试人体感应场景：有人状态持续30分钟
        DeviceCondition occupancyCondition = new DeviceCondition();
        occupancyCondition.setSourceDeviceId("pir_sensor_001");
        occupancyCondition.setPointId("occupancy_status");
        occupancyCondition.setOperator(Operators.Duration.STATES_KEEP_MINUTES);
        occupancyCondition.setValue("OCCUPIED");
        occupancyCondition.setDurationMinutes(30);
        occupancyCondition.setDataType("STRING");
        
        assertEquals("pir_sensor_001", occupancyCondition.getSourceDeviceId());
        assertEquals("occupancy_status", occupancyCondition.getPointId());
        assertEquals("STATES_KEEP_MINUTES", occupancyCondition.getOperator());
        assertEquals("OCCUPIED", occupancyCondition.getValue());
        assertEquals(30, occupancyCondition.getDurationMinutes());
        assertEquals("STRING", occupancyCondition.getDataType());
        assertTrue(occupancyCondition.isDurationOperator());
    }
    
    @Test
    void testSwitchStateScenario() {
        // 测试开关状态场景：开关保持ON状态15分钟
        DeviceCondition switchCondition = new DeviceCondition();
        switchCondition.setSourceDeviceId("switch_001");
        switchCondition.setPointId("switch_state");
        switchCondition.setOperator(Operators.Duration.STATES_KEEP_MINUTES);
        switchCondition.setValue("ON");
        switchCondition.setDurationMinutes(15);
        switchCondition.setDataType("STRING");
        
        assertEquals("switch_001", switchCondition.getSourceDeviceId());
        assertEquals("switch_state", switchCondition.getPointId());
        assertEquals("STATES_KEEP_MINUTES", switchCondition.getOperator());
        assertEquals("ON", switchCondition.getValue());
        assertEquals(15, switchCondition.getDurationMinutes());
        assertTrue(switchCondition.isDurationOperator());
    }
    
    @Test
    void testPowerConsumptionScenario() {
        // 测试功耗场景：功耗不在100W-500W之间
        DeviceCondition powerCondition = new DeviceCondition();
        powerCondition.setSourceDeviceId("power_meter_001");
        powerCondition.setPointId("power_consumption");
        powerCondition.setOperator(Operators.Range.NOT_BETWEEN);
        powerCondition.setValue(100.0);
        powerCondition.setUpperValue(500.0);
        powerCondition.setDataType("DOUBLE");
        
        assertEquals("power_meter_001", powerCondition.getSourceDeviceId());
        assertEquals("power_consumption", powerCondition.getPointId());
        assertEquals("NOT_BETWEEN", powerCondition.getOperator());
        assertEquals(100.0, powerCondition.getValue());
        assertEquals(500.0, powerCondition.getUpperValue());
        assertTrue(powerCondition.isRangeOperator());
    }
    
    @Test
    void testConditionValidation() {
        // 测试条件有效性验证
        DeviceCondition validCondition = new DeviceCondition();
        validCondition.setSourceDeviceId("device_001");
        validCondition.setPointId("point_001");
        validCondition.setOperator(Operators.Basic.EQUALS);
        validCondition.setValue("test_value");
        validCondition.setEnabled(true);
        
        assertTrue(validCondition.isValid());
        
        // 测试无效条件
        DeviceCondition invalidCondition = new DeviceCondition();
        // 缺少必要字段
        assertFalse(invalidCondition.isValid());
    }
    
    @Test
    void testLegacyOperatorRemoval() {
        // 验证旧的业务特定操作符已被移除
        // 这些常量应该不再存在
        try {
            // 尝试访问旧的操作符常量，应该会编译错误
            // String oldOp1 = DeviceCondition.Operators.UNOCCUPIED_FOR_MINUTES; // 应该不存在
            // String oldOp2 = DeviceCondition.Operators.OCCUPIED_FOR_MINUTES;   // 应该不存在
            
            // 如果代码能到这里，说明重构成功
            assertTrue(true, "Legacy operators have been successfully removed");
        } catch (Exception e) {
            fail("Unexpected error: " + e.getMessage());
        }
    }
    
    @Test
    void testOperatorCategorization() {
        // 测试操作符分类
        DeviceCondition durationCondition = new DeviceCondition();
        durationCondition.setOperator(Operators.Duration.STATES_KEEP_MINUTES);
        assertTrue(durationCondition.isDurationOperator());
        assertFalse(durationCondition.isRangeOperator());
        
        DeviceCondition rangeCondition = new DeviceCondition();
        rangeCondition.setOperator(Operators.Range.BETWEEN);
        assertTrue(rangeCondition.isRangeOperator());
        assertFalse(rangeCondition.isDurationOperator());
        
        DeviceCondition normalCondition = new DeviceCondition();
        normalCondition.setOperator(Operators.Basic.EQUALS);
        assertFalse(normalCondition.isDurationOperator());
        assertFalse(normalCondition.isRangeOperator());
    }
}
