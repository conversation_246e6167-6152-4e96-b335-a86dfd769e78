package com.inxaiot.ruleengine.trigger.time;

import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.GlobalCalendarService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * TimeConditionEvaluator 单元测试
 * 测试时间条件评估器的核心功能
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
public class TimeConditionEvaluatorTest {

    @Mock
    private GlobalCalendarService globalCalendarService;

    private TimeConditionEvaluator timeConditionEvaluator;

    @BeforeEach
    void setUp() {
        timeConditionEvaluator = new TimeConditionEvaluator(globalCalendarService);
        
        // 模拟全局日历数据
        GlobalCalendar calendar = TestDataFactory.createGlobalCalendar();
        when(globalCalendarService.loadGlobalCalendar()).thenReturn(calendar);
    }

    /**
     * 测试Cron表达式评估 - 工作日上午8点
     */
    @Test
    void testCronExpressionEvaluation_WorkdayMorning() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月19日（周四）上午8点
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试Cron表达式评估 - 周末时间不匹配
     */
    @Test
    void testCronExpressionEvaluation_WeekendNotMatch() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月21日（周六）上午8点
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试工作日条件评估 - 工作日
     */
    @Test
    void testWorkDayEvaluation_Workday() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月19日（周四）
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试工作日条件评估 - 非工作日
     */
    @Test
    void testWorkDayEvaluation_NonWorkday() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月21日（周六）
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试季节条件评估 - 夏季
     */
    @Test
    void testSeasonEvaluation_Summer() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setSeason("SUMMER");
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年7月15日（夏季）
        LocalDateTime testTime = LocalDateTime.of(2024, 7, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试季节条件评估 - 非夏季
     */
    @Test
    void testSeasonEvaluation_NotSummer() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setSeason("SUMMER");
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月15日（冬季）
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 15, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试包含日期条件 - 匹配
     */
    @Test
    void testIncludeDateEvaluation_Match() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setIncludeDates(Arrays.asList(LocalDate.of(2024, 12, 19)));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月19日
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试排除日期条件 - 被排除
     */
    @Test
    void testExcludeDateEvaluation_Excluded() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setExcludeDates(Arrays.asList(LocalDate.of(2024, 12, 19)));
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月19日（周四）上午8点 - 应该匹配Cron但被排除日期阻止
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试节假日条件 - 节假日
     */
    @Test
    void testHolidayEvaluation_Holiday() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年1月1日（元旦，节假日）
        LocalDateTime testTime = LocalDateTime.of(2024, 1, 1, 8, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        // 节假日应该被排除，即使是工作日时间
        assertFalse(result);
    }

    /**
     * 测试多条件AND逻辑 - 所有条件满足
     */
    @Test
    void testMultipleConditions_AndLogic_AllMet() {
        // Given
        TimeCondition condition1 = new TimeCondition();
        condition1.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        condition1.setLogic(TimeCondition.TimeLogic.AND);

        TimeCondition condition2 = new TimeCondition();
        condition2.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition2.setLogic(TimeCondition.TimeLogic.AND);

        List<TimeCondition> conditions = Arrays.asList(condition1, condition2);

        // 2024年12月19日（周四）上午10点
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试多条件AND逻辑 - 部分条件不满足
     */
    @Test
    void testMultipleConditions_AndLogic_PartialMet() {
        // Given
        TimeCondition condition1 = new TimeCondition();
        condition1.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        condition1.setLogic(TimeCondition.TimeLogic.AND);

        TimeCondition condition2 = new TimeCondition();
        condition2.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"));
        condition2.setLogic(TimeCondition.TimeLogic.AND);

        List<TimeCondition> conditions = Arrays.asList(condition1, condition2);

        // 2024年12月21日（周六）上午10点 - Cron匹配但工作日不匹配
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertFalse(result);
    }

    /**
     * 测试多条件OR逻辑 - 任一条件满足
     */
    @Test
    void testMultipleConditions_OrLogic_AnyMet() {
        // Given
        TimeCondition condition1 = new TimeCondition();
        condition1.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        condition1.setLogic(TimeCondition.TimeLogic.OR);

        TimeCondition condition2 = new TimeCondition();
        condition2.setIncludeDates(Arrays.asList(LocalDate.of(2024, 12, 21)));
        condition2.setLogic(TimeCondition.TimeLogic.OR);

        List<TimeCondition> conditions = Arrays.asList(condition1, condition2);

        // 2024年12月21日（周六）上午10点 - Cron不匹配但包含日期匹配
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result);
    }

    /**
     * 测试空条件列表
     */
    @Test
    void testEmptyConditions() {
        // Given
        List<TimeCondition> conditions = Arrays.asList();
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result); // 空条件应该返回true
    }

    /**
     * 测试null条件列表
     */
    @Test
    void testNullConditions() {
        // Given
        List<TimeCondition> conditions = null;
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 19, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(conditions, testTime);

        // Then
        assertTrue(result); // null条件应该返回true
    }

    /**
     * 测试时间条件优先级 - 包含日期优先级最高
     */
    @Test
    void testTimeConditionPriority_IncludeDateHighest() {
        // Given
        TimeCondition condition = new TimeCondition();
        condition.setIncludeDates(Arrays.asList(LocalDate.of(2024, 12, 21))); // 包含周六
        condition.setWorkDays(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY")); // 只包含工作日
        condition.setLogic(TimeCondition.TimeLogic.AND);

        // 2024年12月21日（周六）- 包含日期匹配但工作日不匹配
        LocalDateTime testTime = LocalDateTime.of(2024, 12, 21, 10, 0, 0);

        // When
        boolean result = timeConditionEvaluator.isTimeConditionMet(Arrays.asList(condition), testTime);

        // Then
        assertTrue(result); // 包含日期优先级最高，应该返回true
    }
}
