package com.inxaiot.ruleengine.trigger.time;

import com.inxaiot.ruleengine.TestConfiguration;
import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.core.engine.RuleEngineService;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.RuleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TimeSchedulerService 单元测试
 * 测试时间调度服务的核心功�? *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Import(RuleEngineTestConfig.class)
public class TimeSchedulerServiceTest {

    @Mock
    private ScheduledExecutorService scheduler;

    @Mock
    private RuleService ruleService;

    @Mock
    private TimeConditionEvaluator timeConditionEvaluator;

    @Mock
    private RuleEngineService ruleEngineService;

    private TimeSchedulerService timeSchedulerService;

    @BeforeEach
    void setUp() {
        timeSchedulerService = new TimeSchedulerService();

        // 使用反射设置私有字段
        try {
            setPrivateField("scheduler", scheduler);
            setPrivateField("ruleService", ruleService);
            setPrivateField("timeConditionEvaluator", timeConditionEvaluator);
            setPrivateField("ruleEngineService", ruleEngineService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set private fields", e);
        }
    }

    /**
     * 测试POINT模式规则触发 - 时间条件满足
     */
    @Test
    void testPointModeRule_TimeConditionMet() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("point_rule_001");
        
        // 设置为POINT模式的时间条件（精确时间点）
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(true);

        // When - 调用内部的检查方�?        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService).triggerRuleActivation(rule.getRuleId());
    }

    /**
     * 测试POINT模式规则 - 时间条件不满�?     */
    @Test
    void testPointModeRule_TimeConditionNotMet() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("point_rule_002");
        
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(false);

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService, never()).triggerRuleActivation(any());
    }

    /**
     * 测试RANGE模式规则 - 首次检查且条件满足
     */
    @Test
    void testRangeModeRule_FirstCheckActive() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("range_rule_001");
        
        // 设置为RANGE模式的时间条件（时间段）
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(true);

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService).triggerRuleActivation(rule.getRuleId());
    }

    /**
     * 测试RANGE模式规则 - 状态从不活跃变为活�?     */
    @Test
    void testRangeModeRule_BecomeActive() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("range_rule_002");
        
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));

        // 第一次检查：条件不满�?        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(false);
        invokePrivateMethod("checkForTimeTriggers");

        // 第二次检查：条件满足
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(true);

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService).triggerRuleActivation(rule.getRuleId());
    }

    /**
     * 测试RANGE模式规则 - 状态从活跃变为不活�?     */
    @Test
    void testRangeModeRule_BecomeInactive() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("range_rule_003");
        
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));

        // 第一次检查：条件满足
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(true);
        invokePrivateMethod("checkForTimeTriggers");

        // 第二次检查：条件不满�?        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class)))
            .thenReturn(false);

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService).triggerRuleActivation(rule.getRuleId()); // 第一次激�?        verify(ruleEngineService).triggerRuleDeactivation(rule.getRuleId()); // 第二次失�?    }

    /**
     * 测试无时间驱动规则的情况
     */
    @Test
    void testNoTimeDrivenRules() throws Exception {
        // Given
        RuleDefinition eventRule = TestDataFactory.createSimpleEventDrivenRule();
        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(eventRule));

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
    }

    /**
     * 测试规则没有时间条件的情�?     */
    @Test
    void testRuleWithoutTimeConditions() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setTimeConditions(null); // 没有时间条件

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(timeConditionEvaluator, never()).isTimeConditionMet(any(), any());
    }

    /**
     * 测试Cron表达式模式推�?- POINT模式
     */
    @Test
    void testInferTriggerMode_Point() throws Exception {
        // Given
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI")); // 精确时间�?
        // When
        Object mode = invokePrivateMethod("inferTriggerMode", Arrays.asList(timeCondition));

        // Then
        assertEquals("POINT", mode.toString());
    }

    /**
     * 测试Cron表达式模式推�?- RANGE模式
     */
    @Test
    void testInferTriggerMode_Range() throws Exception {
        // Given
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8-18 ? * MON-FRI")); // 时间�?
        // When
        Object mode = invokePrivateMethod("inferTriggerMode", Arrays.asList(timeCondition));

        // Then
        assertEquals("RANGE", mode.toString());
    }

    /**
     * 测试清除规则状�?     */
    @Test
    void testEvictStateForRule() {
        // Given
        String ruleId = "test_rule_001";

        // When
        timeSchedulerService.evictStateForRule(ruleId);

        // Then
        // 验证状态被清除（通过获取状态数量验证）
        int stateCount = timeSchedulerService.getStateCount();
        assertEquals(0, stateCount);
    }

    /**
     * 测试异常处理
     */
    @Test
    void testExceptionHandling() throws Exception {
        // Given
        when(ruleService.findAllEnabledRules())
            .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then - 不应该抛出异�?        assertDoesNotThrow(() -> {
            try {
                invokePrivateMethod("checkForTimeTriggers");
            } catch (Exception e) {
                // 忽略反射异常
            }
        });
    }

    // 辅助方法：设置私有字�?    private void setPrivateField(String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = TimeSchedulerService.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(timeSchedulerService, value);
    }

    // 辅助方法：调用私有方�?    private Object invokePrivateMethod(String methodName, Object... args) throws Exception {
        Class<?>[] paramTypes = new Class[args.length];
        for (int i = 0; i < args.length; i++) {
            paramTypes[i] = args[i].getClass();
            if (paramTypes[i] == Arrays.asList().getClass()) {
                paramTypes[i] = List.class;
            }
        }
        
        java.lang.reflect.Method method = TimeSchedulerService.class.getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(timeSchedulerService, args);
    }
}
