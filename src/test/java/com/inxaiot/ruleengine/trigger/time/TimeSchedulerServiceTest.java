package com.inxaiot.ruleengine.trigger.time;

import com.inxaiot.ruleengine.RuleEngineTestConfig;
import com.inxaiot.ruleengine.TestDataFactory;
import com.inxaiot.ruleengine.core.engine.RuleEngineService;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.RuleService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.concurrent.ScheduledExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * TimeSchedulerService unit test
 * Test core functionality of time scheduler service
 */
@ExtendWith(MockitoExtension.class)
@SpringJUnitConfig
@Import(RuleEngineTestConfig.class)
public class TimeSchedulerServiceTest {

    @Mock
    private ScheduledExecutorService scheduler;

    @Mock
    private RuleService ruleService;

    @Mock
    private TimeConditionEvaluator timeConditionEvaluator;

    @Mock
    private RuleEngineService ruleEngineService;

    private TimeSchedulerService timeSchedulerService;

    @BeforeEach
    void setUp() {
        timeSchedulerService = new TimeSchedulerService();
        
        // Use reflection to set private fields
        try {
            setPrivateField("scheduler", scheduler);
            setPrivateField("ruleService", ruleService);
            setPrivateField("timeConditionEvaluator", timeConditionEvaluator);
            setPrivateField("ruleEngineService", ruleEngineService);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set private fields", e);
        }
    }

    /**
     * Test POINT mode rule trigger - time condition met
     */
    @Test
    void testPointModeRule_TimeConditionMet() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("point_rule_001");
        
        // Set POINT mode time condition (exact time point)
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));
        when(timeConditionEvaluator.isTimeConditionMet(any(java.util.List.class), any(LocalDateTime.class)))
            .thenReturn(true);

        // When - call internal check method
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService).triggerRuleActivation(rule.getRuleId());
    }

    /**
     * Test POINT mode rule - time condition not met
     */
    @Test
    void testPointModeRule_TimeConditionNotMet() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setRuleId("point_rule_002");
        
        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList("0 0 8 ? * MON-FRI"));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));
        when(timeConditionEvaluator.isTimeConditionMet(any(java.util.List.class), any(LocalDateTime.class)))
            .thenReturn(false);

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService, never()).triggerRuleActivation(any());
    }

    /**
     * Test no time-driven rules scenario
     */
    @Test
    void testNoTimeDrivenRules() throws Exception {
        // Given
        RuleDefinition eventRule = TestDataFactory.createSimpleEventDrivenRule();
        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(eventRule));

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
    }

    /**
     * Test rule without time conditions
     */
    @Test
    void testRuleWithoutTimeConditions() throws Exception {
        // Given
        RuleDefinition rule = TestDataFactory.createTimeDrivenRule();
        rule.setTimeConditions(null); // no time conditions

        when(ruleService.findAllEnabledRules()).thenReturn(Arrays.asList(rule));

        // When
        invokePrivateMethod("checkForTimeTriggers");

        // Then
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(timeConditionEvaluator, never()).isTimeConditionMet(any(java.util.List.class), any());
    }

    /**
     * Test clearing rule state
     */
    @Test
    void testEvictStateForRule() {
        // Given
        String ruleId = "test_rule_001";

        // When
        timeSchedulerService.evictStateForRule(ruleId);

        // Then
        // Verify state is cleared (verify by checking state count)
        int stateCount = timeSchedulerService.getStateCount();
        assertEquals(0, stateCount);
    }

    /**
     * Test exception handling
     */
    @Test
    void testExceptionHandling() throws Exception {
        // Given
        when(ruleService.findAllEnabledRules())
            .thenThrow(new RuntimeException("Database connection failed"));

        // When & Then - should not throw exception
        assertDoesNotThrow(() -> {
            try {
                invokePrivateMethod("checkForTimeTriggers");
            } catch (Exception e) {
                // Ignore reflection exceptions
            }
        });
    }

    // Helper method: set private field
    private void setPrivateField(String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = TimeSchedulerService.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(timeSchedulerService, value);
    }

    // Helper method: invoke private method
    private Object invokePrivateMethod(String methodName, Object... args) throws Exception {
        Class<?>[] paramTypes = new Class[args.length];
        for (int i = 0; i < args.length; i++) {
            paramTypes[i] = args[i].getClass();
        }
        
        java.lang.reflect.Method method = TimeSchedulerService.class.getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(timeSchedulerService, args);
    }
}
