package com.inxaiot.ruleengine.contextstate.service;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SystemContextService 单元测试
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
class SystemContextServiceTest {

    private SystemContextServiceImpl systemContextService;

    @BeforeEach
    void setUp() {
        systemContextService = new SystemContextServiceImpl();
        // 手动初始化，模拟Spring的@PostConstruct
        systemContextService.init();
    }
    
    @Test
    void testGetBasicInfo() {
        // 在单元测试中，@Value注解不会生效，所以这些值可能为null
        // 但getAllContext()方法会提供默认值
        Map<String, Object> context = systemContextService.getAllContext();

        assertNotNull(context.get("engineId"));
        assertNotNull(context.get("regionId"));
        assertNotNull(context.get("buildingId"));
        assertNotNull(context.get("engineVersion"));
        assertNotNull(context.get("timezone"));
        assertNotNull(context.get("locale"));
    }
    
    @Test
    void testTechnicalConfig() {
        // 测试技术配置
        String testKey = "test.config";
        String testValue = "test.value";
        
        // 设置配置
        systemContextService.setTechnicalConfig(testKey, testValue);
        
        // 获取配置
        Object retrievedValue = systemContextService.getTechnicalConfig(testKey);
        assertEquals(testValue, retrievedValue);
    }
    
    @Test
    void testGetAllContext() {
        // 测试获取所有上下文
        Map<String, Object> context = systemContextService.getAllContext();
        
        assertNotNull(context);
        assertTrue(context.containsKey("engineId"));
        assertTrue(context.containsKey("regionId"));
        assertTrue(context.containsKey("buildingId"));
        assertTrue(context.containsKey("engineVersion"));
        assertTrue(context.containsKey("timezone"));
        assertTrue(context.containsKey("locale"));
        assertTrue(context.containsKey("debugMode"));
        assertTrue(context.containsKey("currentTime"));
        
        // 验证currentTime是当前时间附近
        Long currentTime = (Long) context.get("currentTime");
        assertNotNull(currentTime);
        assertTrue(Math.abs(System.currentTimeMillis() - currentTime) < 1000); // 1秒内
    }
    
    @Test
    void testUpdateBasicConfig() {
        // 测试更新基础配置
        Map<String, Object> config = new HashMap<>();
        config.put("engineId", "updated-engine");
        config.put("regionId", "updated-region");
        config.put("debugMode", "false");
        config.put("customConfig", "customValue");
        
        systemContextService.updateBasicConfig(config);
        
        // 验证更新结果
        assertEquals("updated-engine", systemContextService.getEngineId());
        assertEquals("updated-region", systemContextService.getRegionId());
        assertFalse(systemContextService.isDebugMode());
        assertEquals("customValue", systemContextService.getTechnicalConfig("customConfig"));
    }
    
    @Test
    void testResetToDefaults() {
        // 先设置一些配置
        systemContextService.setTechnicalConfig("test.key", "test.value");
        
        // 重置
        systemContextService.resetToDefaults();
        
        // 验证配置被清空
        assertNull(systemContextService.getTechnicalConfig("test.key"));
        assertFalse(systemContextService.isDebugMode());
    }
    
    @Test
    void testNullAndEmptyHandling() {
        // 测试空值处理
        systemContextService.updateBasicConfig(null);
        systemContextService.updateBasicConfig(new HashMap<>());
        
        // 测试空键值处理
        systemContextService.setTechnicalConfig(null, "value");
        systemContextService.setTechnicalConfig("", "value");
        systemContextService.setTechnicalConfig("  ", "value");
        
        // 应该不会抛出异常
        assertNotNull(systemContextService.getAllContext());
    }
    
    @Test
    void testContextConsistency() {
        // 测试上下文一致性
        Map<String, Object> context1 = systemContextService.getAllContext();
        
        // 等待一小段时间
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        Map<String, Object> context2 = systemContextService.getAllContext();
        
        // 除了currentTime，其他值应该相同
        context1.remove("currentTime");
        context2.remove("currentTime");
        
        assertEquals(context1, context2);
    }
}
