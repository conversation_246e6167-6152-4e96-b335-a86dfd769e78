package com.inxaiot.ruleengine.scheduler.service;



import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.GlobalCalendarService;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.TimeConditionEvaluator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * TimeConditionEvaluator 单元测试
 * 重点测试AND/OR逻辑的正确性
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
class TimeConditionEvaluatorTest {
    
    @Mock
    private GlobalCalendarService globalConfigStorageService;
    
    private TimeConditionEvaluator evaluator;
    private GlobalCalendar globalCalendar;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试用的全局日历
        globalCalendar = new GlobalCalendar();
        globalCalendar.addHoliday(LocalDate.of(2024, 1, 1)); // 元旦
        globalCalendar.setSummerTime(LocalDate.of(2024, 3, 1), LocalDate.of(2024, 10, 31));
        globalCalendar.setWinterTime(LocalDate.of(2024, 11, 1), LocalDate.of(2025, 2, 28));
        
        when(globalConfigStorageService.loadGlobalCalendar()).thenReturn(globalCalendar);
        
        evaluator = new TimeConditionEvaluator(globalConfigStorageService);
    }
    
    @Test
    void testEmptyConditions() {
        // 测试空条件列表
        LocalDateTime testTime = LocalDateTime.of(2024, 6, 15, 9, 0);
        
        assertTrue(evaluator.isTimeConditionMet((List<TimeCondition>) null, testTime));
        assertTrue(evaluator.isTimeConditionMet(Collections.<TimeCondition>emptyList(), testTime));
    }
    
    @Test
    void testSingleAndCondition() {
        // 测试单个AND条件
        TimeCondition condition = createWorkDayCondition(TimeCondition.TimeLogic.AND);
        List<TimeCondition> conditions = Arrays.asList(condition);
        
        // 工作日应该满足
        LocalDateTime workDayTime = LocalDateTime.of(2024, 6, 17, 9, 0); // 周一
        assertTrue(evaluator.isTimeConditionMet(conditions, workDayTime));
        
        // 周末不应该满足
        LocalDateTime weekendTime = LocalDateTime.of(2024, 6, 15, 9, 0); // 周六
        assertFalse(evaluator.isTimeConditionMet(conditions, weekendTime));
    }
    
    @Test
    void testSingleOrCondition() {
        // 测试单个OR条件
        TimeCondition condition = createWorkDayCondition(TimeCondition.TimeLogic.OR);
        List<TimeCondition> conditions = Arrays.asList(condition);
        
        // 工作日应该满足
        LocalDateTime workDayTime = LocalDateTime.of(2024, 6, 17, 9, 0); // 周一
        assertTrue(evaluator.isTimeConditionMet(conditions, workDayTime));
        
        // 周末不应该满足
        LocalDateTime weekendTime = LocalDateTime.of(2024, 6, 15, 9, 0); // 周六
        assertFalse(evaluator.isTimeConditionMet(conditions, weekendTime));
    }
    
    @Test
    void testMultipleAndConditions() {
        // 测试多个AND条件：工作日 AND 夏季
        TimeCondition workDayCondition = createWorkDayCondition(TimeCondition.TimeLogic.AND);
        TimeCondition summerCondition = createSeasonCondition("SUMMER", TimeCondition.TimeLogic.AND);
        List<TimeCondition> conditions = Arrays.asList(workDayCondition, summerCondition);
        
        // 夏季工作日应该满足
        LocalDateTime summerWorkDay = LocalDateTime.of(2024, 6, 17, 9, 0); // 夏季周一
        assertTrue(evaluator.isTimeConditionMet(conditions, summerWorkDay));
        
        // 夏季周末不应该满足（工作日条件不满足）
        LocalDateTime summerWeekend = LocalDateTime.of(2024, 6, 15, 9, 0); // 夏季周六
        assertFalse(evaluator.isTimeConditionMet(conditions, summerWeekend));
        
        // 冬季工作日不应该满足（季节条件不满足）
        LocalDateTime winterWorkDay = LocalDateTime.of(2024, 12, 16, 9, 0); // 冬季周一
        assertFalse(evaluator.isTimeConditionMet(conditions, winterWorkDay));
    }
    
    @Test
    void testMultipleOrConditions() {
        // 测试多个OR条件：工作日 OR 夏季
        TimeCondition workDayCondition = createWorkDayCondition(TimeCondition.TimeLogic.OR);
        TimeCondition summerCondition = createSeasonCondition("SUMMER", TimeCondition.TimeLogic.OR);
        List<TimeCondition> conditions = Arrays.asList(workDayCondition, summerCondition);
        
        // 夏季工作日应该满足（两个条件都满足）
        LocalDateTime summerWorkDay = LocalDateTime.of(2024, 6, 17, 9, 0); // 夏季周一
        assertTrue(evaluator.isTimeConditionMet(conditions, summerWorkDay));
        
        // 夏季周末应该满足（季节条件满足）
        LocalDateTime summerWeekend = LocalDateTime.of(2024, 6, 15, 9, 0); // 夏季周六
        assertTrue(evaluator.isTimeConditionMet(conditions, summerWeekend));
        
        // 冬季工作日应该满足（工作日条件满足）
        LocalDateTime winterWorkDay = LocalDateTime.of(2024, 12, 16, 9, 0); // 冬季周一
        assertTrue(evaluator.isTimeConditionMet(conditions, winterWorkDay));
        
        // 冬季周末不应该满足（两个条件都不满足）
        LocalDateTime winterWeekend = LocalDateTime.of(2024, 12, 14, 9, 0); // 冬季周六
        assertFalse(evaluator.isTimeConditionMet(conditions, winterWeekend));
    }
    
    @Test
    void testMixedAndOrConditions() {
        // 测试混合AND/OR条件：(工作日 AND 上午) OR 夏季
        TimeCondition workDayMorningCondition = createWorkDayMorningCondition(TimeCondition.TimeLogic.AND);
        TimeCondition summerCondition = createSeasonCondition("SUMMER", TimeCondition.TimeLogic.OR);
        List<TimeCondition> conditions = Arrays.asList(workDayMorningCondition, summerCondition);
        
        // 夏季任何时间都应该满足（OR条件满足）
        LocalDateTime summerAfternoon = LocalDateTime.of(2024, 6, 15, 15, 0); // 夏季周六下午
        assertTrue(evaluator.isTimeConditionMet(conditions, summerAfternoon));
        
        // 冬季工作日上午应该满足（AND条件满足）
        LocalDateTime winterMorning = LocalDateTime.of(2024, 12, 16, 9, 0); // 冬季周一上午
        assertTrue(evaluator.isTimeConditionMet(conditions, winterMorning));
        
        // 冬季工作日下午不应该满足（AND条件不完全满足，OR条件也不满足）
        LocalDateTime winterAfternoon = LocalDateTime.of(2024, 12, 16, 15, 0); // 冬季周一下午
        assertFalse(evaluator.isTimeConditionMet(conditions, winterAfternoon));
        
        // 冬季周末不应该满足（两种条件都不满足）
        LocalDateTime winterWeekend = LocalDateTime.of(2024, 12, 14, 9, 0); // 冬季周六
        assertFalse(evaluator.isTimeConditionMet(conditions, winterWeekend));
    }
    
    @Test
    void testDisabledConditions() {
        // 测试禁用的条件
        TimeCondition enabledCondition = createWorkDayCondition(TimeCondition.TimeLogic.AND);
        TimeCondition disabledCondition = createSeasonCondition("SUMMER", TimeCondition.TimeLogic.AND);
        disabledCondition.setEnabled(false);
        
        List<TimeCondition> conditions = Arrays.asList(enabledCondition, disabledCondition);
        
        // 冬季工作日应该满足（禁用的夏季条件被忽略）
        LocalDateTime winterWorkDay = LocalDateTime.of(2024, 12, 16, 9, 0);
        assertTrue(evaluator.isTimeConditionMet(conditions, winterWorkDay));
    }
    
    // 辅助方法：创建工作日条件
    private TimeCondition createWorkDayCondition(TimeCondition.TimeLogic logic) {
        TimeCondition condition = new TimeCondition();
        condition.setWorkDays(Arrays.asList("MON", "TUE", "WED", "THU", "FRI"));
        condition.setTimeCronExpressions(Arrays.asList("0 0 9 * * ?")); // 每天9点 (Quartz格式：秒 分 时 日 月 星期)
        condition.setLogic(logic);
        condition.setEnabled(true);
        return condition;
    }

    // 辅助方法：创建季节条件
    private TimeCondition createSeasonCondition(String season, TimeCondition.TimeLogic logic) {
        TimeCondition condition = new TimeCondition();
        condition.setSeason(season);
        condition.setTimeCronExpressions(Arrays.asList("0 0 * * * ?")); // 每小时 (Quartz格式)
        condition.setLogic(logic);
        condition.setEnabled(true);
        return condition;
    }

    // 辅助方法：创建工作日上午条件
    private TimeCondition createWorkDayMorningCondition(TimeCondition.TimeLogic logic) {
        TimeCondition condition = new TimeCondition();
        condition.setWorkDays(Arrays.asList("MON", "TUE", "WED", "THU", "FRI"));
        condition.setTimeCronExpressions(Arrays.asList("0 0 9-11 * * ?")); // 上午9-11点 (Quartz格式)
        condition.setLogic(logic);
        condition.setEnabled(true);
        return condition;
    }
    
    @Test
    void testCronExpressionValidation() {
        // 测试Cron表达式验证 (Quartz格式：秒 分 时 日 月 星期)
        assertTrue(evaluator.isValidCronExpression("0 0 9 * * ?"));
        assertTrue(evaluator.isValidCronExpression("0 0 12 * * ?"));
        assertFalse(evaluator.isValidCronExpression("invalid cron"));
        assertFalse(evaluator.isValidCronExpression(""));
        assertFalse(evaluator.isValidCronExpression(null));
    }

    @Test
    void testGetNextValidTime() {
        // 测试获取下一个有效时间
        LocalDateTime fromTime = LocalDateTime.of(2024, 6, 15, 8, 0);
        LocalDateTime nextTime = evaluator.getNextValidTime("0 0 9 * * ?", fromTime);

        assertNotNull(nextTime);
        assertEquals(9, nextTime.getHour());
        assertEquals(0, nextTime.getMinute());

        // 无效表达式应该返回null
        assertNull(evaluator.getNextValidTime("invalid", fromTime));
    }
}
