package com.inxaiot.ruleengine.scheduler.service;

import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.core.engine.RuleEngineService;
import com.inxaiot.ruleengine.storage.RuleService;
import com.inxaiot.ruleengine.trigger.time.TimeConditionEvaluator;
import com.inxaiot.ruleengine.trigger.time.TimeSchedulerService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * timeScheduler测试类
 */
class timeSchedulerTest {

    private TimeSchedulerService timeScheduler;

    @Mock
    private ScheduledExecutorService scheduler;

    @Mock
    private RuleService ruleStorageService;

    @Mock
    private TimeConditionEvaluator timeConditionEvaluator;

    @Mock
    private RuleEngineService ruleEngineService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        timeScheduler = new TimeSchedulerService();
        
        // 注入mock对象
        ReflectionTestUtils.setField(timeScheduler, "scheduler", scheduler);
        ReflectionTestUtils.setField(timeScheduler, "ruleStorageService", ruleStorageService);
        ReflectionTestUtils.setField(timeScheduler, "timeConditionEvaluator", timeConditionEvaluator);
        ReflectionTestUtils.setField(timeScheduler, "ruleEngineService", timeScheduler);
    }

    @Test
    void testCheckForTimeTriggers_NoTimeDrivenRules() {
        // 准备测试数据
        when(ruleStorageService.findAllEnabledRules()).thenReturn(Collections.emptyList());

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(timeScheduler, "checkForTimeTriggers");

        // 验证
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
    }

    @Test
    void testCheckForTimeTriggers_PointModeRule() {
        // 准备测试数据
        RuleDefinition pointRule = createTimeDrivenRule("point-rule-1", "0 9 * * *"); // 每天9点
        when(ruleStorageService.findAllEnabledRules()).thenReturn(Arrays.asList(pointRule));
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class))).thenReturn(true);

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(timeScheduler, "checkForTimeTriggers");

        // 验证：POINT模式规则应该触发激活
        verify(ruleEngineService, times(1)).triggerRuleActivation("point-rule-1");
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
    }

    @Test
    void testCheckForTimeTriggers_RangeModeRule_FirstCheck() {
        // 准备测试数据
        RuleDefinition rangeRule = createTimeDrivenRule("range-rule-1", "0 9-17 * * *"); // 9点到17点
        when(ruleStorageService.findAllEnabledRules()).thenReturn(Arrays.asList(rangeRule));
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class))).thenReturn(true);

        // 调用私有方法进行测试
        ReflectionTestUtils.invokeMethod(timeScheduler, "checkForTimeTriggers");

        // 验证：RANGE模式规则首次检查且当前活跃，应该触发激活
        verify(ruleEngineService, times(1)).triggerRuleActivation("range-rule-1");
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
        
        // 验证状态已保存
        assertEquals(1, timeScheduler.getStateCount());
    }

    @Test
    void testCheckForTimeTriggers_RangeModeRule_StateChange() {
        // 准备测试数据
        RuleDefinition rangeRule = createTimeDrivenRule("range-rule-1", "0 9-17 * * *"); // 9点到17点
        when(ruleStorageService.findAllEnabledRules()).thenReturn(Arrays.asList(rangeRule));

        // 第一次检查：激活状态
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class))).thenReturn(true);
        ReflectionTestUtils.invokeMethod(timeScheduler, "checkForTimeTriggers");

        // 重置mock
        reset(ruleEngineService);

        // 第二次检查：变为非激活状态
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class))).thenReturn(false);
        ReflectionTestUtils.invokeMethod(timeScheduler, "checkForTimeTriggers");

        // 验证：应该触发失活
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(ruleEngineService, times(1)).triggerRuleDeactivation("range-rule-1");
    }

    @Test
    void testInferTriggerMode_PointMode() {
        // 测试POINT模式推断
        TimeCondition pointCondition = new TimeCondition();
        pointCondition.setTimeCronExpressions(Arrays.asList("0 9 * * *")); // 每天9点

        Object result = ReflectionTestUtils.invokeMethod(timeScheduler, "inferTriggerMode",
                                                        Arrays.asList(pointCondition));

        // 验证推断结果为POINT
        assertEquals("POINT", result.toString());
    }

    @Test
    void testInferTriggerMode_RangeMode() {
        // 测试RANGE模式推断
        TimeCondition rangeCondition = new TimeCondition();
        rangeCondition.setTimeCronExpressions(Arrays.asList("0 9-17 * * *")); // 9点到17点

        Object result = ReflectionTestUtils.invokeMethod(timeScheduler, "inferTriggerMode",
                                                        Arrays.asList(rangeCondition));

        // 验证推断结果为RANGE
        assertEquals("RANGE", result.toString());
    }

    @Test
    void testIsRangeExpression() {
        // 测试范围表达式判断
        assertTrue((Boolean) ReflectionTestUtils.invokeMethod(timeScheduler, "isRangeExpression", "0 9-17 * * *"));
        assertTrue((Boolean) ReflectionTestUtils.invokeMethod(timeScheduler, "isRangeExpression", "*/5 * * * *"));
        assertTrue((Boolean) ReflectionTestUtils.invokeMethod(timeScheduler, "isRangeExpression", "* 9 * * *"));
        
        assertFalse((Boolean) ReflectionTestUtils.invokeMethod(timeScheduler, "isRangeExpression", "0 9 * * *"));
        assertFalse((Boolean) ReflectionTestUtils.invokeMethod(timeScheduler, "isRangeExpression", "30 14 * * *"));
    }

    @Test
    void testEvictStateForRule() {
        // 准备测试数据
        RuleDefinition rangeRule = createTimeDrivenRule("range-rule-1", "0 9-17 * * *");
        when(ruleStorageService.findAllEnabledRules()).thenReturn(Arrays.asList(rangeRule));
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class))).thenReturn(true);

        // 触发一次检查以建立状态
        ReflectionTestUtils.invokeMethod(timeScheduler, "checkForTimeTriggers");
        assertEquals(1, timeScheduler.getStateCount());

        // 清除状态
        timeScheduler.evictStateForRule("range-rule-1");
        assertEquals(0, timeScheduler.getStateCount());
    }

    @Test
    void testProcessTimeDrivenRule_WithoutTimeConditions() {
        // 准备测试数据：没有时间条件的规则
        RuleDefinition ruleWithoutTimeConditions = new RuleDefinition();
        ruleWithoutTimeConditions.setRuleId("no-time-rule");
        ruleWithoutTimeConditions.setTriggerType(RuleDefinition.TriggerType.TIME_DRIVEN);
        ruleWithoutTimeConditions.setTimeConditions(null);

        // 调用私有方法
        ReflectionTestUtils.invokeMethod(timeScheduler, "processTimeDrivenRule", 
                                        ruleWithoutTimeConditions, LocalDateTime.now());

        // 验证：没有时间条件的规则不应该触发任何动作
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
    }

    @Test
    void testProcessTimeDrivenRule_ExceptionHandling() {
        // 准备测试数据
        RuleDefinition rule = createTimeDrivenRule("error-rule", "0 9 * * *");
        when(timeConditionEvaluator.isTimeConditionMet(any(List.class), any(LocalDateTime.class))).thenThrow(new RuntimeException("Test exception"));

        // 调用私有方法，应该不抛出异常
        assertDoesNotThrow(() -> {
            ReflectionTestUtils.invokeMethod(timeScheduler, "processTimeDrivenRule", 
                                            rule, LocalDateTime.now());
        });

        // 验证：异常情况下不应该触发任何动作
        verify(ruleEngineService, never()).triggerRuleActivation(any());
        verify(ruleEngineService, never()).triggerRuleDeactivation(any());
    }

    /**
     * 创建时间驱动规则的辅助方法
     */
    private RuleDefinition createTimeDrivenRule(String ruleId, String cronExpression) {
        RuleDefinition rule = new RuleDefinition();
        rule.setRuleId(ruleId);
        rule.setRuleName("Test Time Driven Rule");
        rule.setTriggerType(RuleDefinition.TriggerType.TIME_DRIVEN);
        rule.setEnabled(true);

        TimeCondition timeCondition = new TimeCondition();
        timeCondition.setTimeCronExpressions(Arrays.asList(cronExpression));
        rule.setTimeConditions(Arrays.asList(timeCondition));

        // 添加一些测试动作
        ActionDefinition action = new ActionDefinition();
        action.setActionType("LOG_EVENT");
        rule.setActions(Arrays.asList(action));

        return rule;
    }
}
