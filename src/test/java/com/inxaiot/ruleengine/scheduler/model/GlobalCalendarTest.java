package com.inxaiot.ruleengine.scheduler.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.TimeRange;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GlobalCalendar 单元测试
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
class GlobalCalendarTest {
    
    private GlobalCalendar globalCalendar;
    
    @BeforeEach
    void setUp() {
        globalCalendar = new GlobalCalendar();
    }
    
    @Test
    void testHolidays() {
        // 测试节假日功能
        LocalDate holiday1 = LocalDate.of(2024, 1, 1);  // 元旦
        LocalDate holiday2 = LocalDate.of(2024, 10, 1); // 国庆节
        LocalDate normalDay = LocalDate.of(2024, 6, 15); // 普通日期
        
        // 添加节假日
        globalCalendar.addHoliday(holiday1);
        globalCalendar.addHoliday(holiday2);
        
        // 验证节假日判断
        assertTrue(globalCalendar.isHoliday(holiday1));
        assertTrue(globalCalendar.isHoliday(holiday2));
        assertFalse(globalCalendar.isHoliday(normalDay));
        
        // 验证节假日列表
        assertEquals(2, globalCalendar.getHolidays().size());
        assertTrue(globalCalendar.getHolidays().contains(holiday1));
        assertTrue(globalCalendar.getHolidays().contains(holiday2));
    }
    
    @Test
    void testRemoveHoliday() {
        // 测试移除节假日
        LocalDate holiday = LocalDate.of(2024, 1, 1);
        
        globalCalendar.addHoliday(holiday);
        assertTrue(globalCalendar.isHoliday(holiday));
        
        globalCalendar.removeHoliday(holiday);
        assertFalse(globalCalendar.isHoliday(holiday));
        assertEquals(0, globalCalendar.getHolidays().size());
    }
    
    @Test
    void testSummerTime() {
        // 测试夏令时间
        LocalDate summerStart = LocalDate.of(2024, 3, 1);
        LocalDate summerEnd = LocalDate.of(2024, 10, 31);
        
        // 设置夏令时间
        globalCalendar.setSummerTime(summerStart, summerEnd);
        
        // 验证夏令时间判断
        assertTrue(globalCalendar.isSummerTime(LocalDate.of(2024, 6, 15))); // 夏季中间
        assertTrue(globalCalendar.isSummerTime(summerStart)); // 开始日期
        assertTrue(globalCalendar.isSummerTime(summerEnd));   // 结束日期
        assertFalse(globalCalendar.isSummerTime(LocalDate.of(2024, 2, 15))); // 夏季之前
        assertFalse(globalCalendar.isSummerTime(LocalDate.of(2024, 11, 15))); // 夏季之后
        
        // 验证TimeRange对象
        TimeRange summerTime = globalCalendar.getSummerTime();
        assertNotNull(summerTime);
        assertEquals(summerStart, summerTime.getStartDate());
        assertEquals(summerEnd, summerTime.getEndDate());
        assertEquals("夏令时间", summerTime.getName());
        assertTrue(summerTime.isEnabled());
    }
    
    @Test
    void testWinterTime() {
        // 测试冬令时间
        LocalDate winterStart = LocalDate.of(2024, 11, 1);
        LocalDate winterEnd = LocalDate.of(2025, 2, 28);
        
        // 设置冬令时间
        globalCalendar.setWinterTime(winterStart, winterEnd);
        
        // 验证冬令时间判断
        assertTrue(globalCalendar.isWinterTime(LocalDate.of(2024, 12, 15))); // 冬季中间
        assertTrue(globalCalendar.isWinterTime(winterStart)); // 开始日期
        assertTrue(globalCalendar.isWinterTime(winterEnd));   // 结束日期
        assertFalse(globalCalendar.isWinterTime(LocalDate.of(2024, 10, 15))); // 冬季之前
        assertFalse(globalCalendar.isWinterTime(LocalDate.of(2025, 3, 15))); // 冬季之后
        
        // 验证TimeRange对象
        TimeRange winterTime = globalCalendar.getWinterTime();
        assertNotNull(winterTime);
        assertEquals(winterStart, winterTime.getStartDate());
        assertEquals(winterEnd, winterTime.getEndDate());
        assertEquals("冬令时间", winterTime.getName());
        assertTrue(winterTime.isEnabled());
    }
    
    @Test
    void testSeasonCheck() {
        // 测试季节检查
        LocalDate summerDate = LocalDate.of(2024, 6, 15);
        LocalDate winterDate = LocalDate.of(2024, 12, 15);
        LocalDate otherDate = LocalDate.of(2024, 9, 15);
        
        // 设置夏令时间和冬令时间
        globalCalendar.setSummerTime(LocalDate.of(2024, 3, 1), LocalDate.of(2024, 8, 31));
        globalCalendar.setWinterTime(LocalDate.of(2024, 11, 1), LocalDate.of(2025, 2, 28));
        
        // 测试季节判断
        assertTrue(globalCalendar.isInSeason(summerDate, "SUMMER"));
        assertFalse(globalCalendar.isInSeason(summerDate, "WINTER"));
        
        assertTrue(globalCalendar.isInSeason(winterDate, "WINTER"));
        assertFalse(globalCalendar.isInSeason(winterDate, "SUMMER"));
        
        assertFalse(globalCalendar.isInSeason(otherDate, "SUMMER"));
        assertFalse(globalCalendar.isInSeason(otherDate, "WINTER"));
        
        // 测试特殊值
        assertTrue(globalCalendar.isInSeason(summerDate, "ALL"));
        assertTrue(globalCalendar.isInSeason(summerDate, null));
        assertFalse(globalCalendar.isInSeason(summerDate, "INVALID"));
    }
    
    @Test
    void testEmptyCalendar() {
        // 测试空日历
        assertTrue(globalCalendar.isEmpty());
        assertEquals(0, globalCalendar.getConfigCount());
        
        // 添加节假日后不再为空
        globalCalendar.addHoliday(LocalDate.of(2024, 1, 1));
        assertFalse(globalCalendar.isEmpty());
        assertEquals(1, globalCalendar.getConfigCount());
        
        // 添加夏令时间
        globalCalendar.setSummerTime(LocalDate.of(2024, 3, 1), LocalDate.of(2024, 10, 31));
        assertEquals(2, globalCalendar.getConfigCount());
        
        // 添加冬令时间
        globalCalendar.setWinterTime(LocalDate.of(2024, 11, 1), LocalDate.of(2025, 2, 28));
        assertEquals(3, globalCalendar.getConfigCount());
    }
    
    @Test
    void testSetHolidaysList() {
        // 测试设置节假日列表
        LocalDate holiday1 = LocalDate.of(2024, 1, 1);
        LocalDate holiday2 = LocalDate.of(2024, 10, 1);
        
        globalCalendar.setHolidays(Arrays.asList(holiday1, holiday2));
        
        assertEquals(2, globalCalendar.getHolidays().size());
        assertTrue(globalCalendar.isHoliday(holiday1));
        assertTrue(globalCalendar.isHoliday(holiday2));
    }
    
    @Test
    void testSetTimeRangeObjects() {
        // 测试直接设置TimeRange对象
        TimeRange summerTime = new TimeRange(
            LocalDate.of(2024, 3, 1), 
            LocalDate.of(2024, 10, 31), 
            "自定义夏令时间", 
            true
        );
        
        TimeRange winterTime = new TimeRange(
            LocalDate.of(2024, 11, 1), 
            LocalDate.of(2025, 2, 28), 
            "自定义冬令时间", 
            false  // 禁用
        );
        
        globalCalendar.setSummerTime(summerTime);
        globalCalendar.setWinterTime(winterTime);
        
        // 验证夏令时间
        assertTrue(globalCalendar.isSummerTime(LocalDate.of(2024, 6, 15)));
        assertEquals("自定义夏令时间", globalCalendar.getSummerTime().getName());
        
        // 验证冬令时间（禁用状态）
        assertFalse(globalCalendar.isWinterTime(LocalDate.of(2024, 12, 15))); // 因为disabled
        assertEquals("自定义冬令时间", globalCalendar.getWinterTime().getName());
        assertFalse(globalCalendar.getWinterTime().isEnabled());
    }
    
    @Test
    void testNullHandling() {
        // 测试空值处理
        assertFalse(globalCalendar.isHoliday(null));
        assertFalse(globalCalendar.isSummerTime(null));
        assertFalse(globalCalendar.isWinterTime(null));
        
        // 设置null的TimeRange
        globalCalendar.setSummerTime(null);
        globalCalendar.setWinterTime(null);
        
        assertFalse(globalCalendar.isSummerTime(LocalDate.now()));
        assertFalse(globalCalendar.isWinterTime(LocalDate.now()));
        
        // 设置null的节假日列表
        globalCalendar.setHolidays(null);
        assertEquals(0, globalCalendar.getHolidays().size());
    }
    
    @Test
    void testToString() {
        // 测试toString方法
        globalCalendar.addHoliday(LocalDate.of(2024, 1, 1));
        globalCalendar.setSummerTime(LocalDate.of(2024, 3, 1), LocalDate.of(2024, 10, 31));
        
        String str = globalCalendar.toString();
        assertNotNull(str);
        assertTrue(str.contains("holidays=1"));
        assertTrue(str.contains("summerTime="));
        assertTrue(str.contains("configCount=2"));
    }
    
    @Test
    void testJsonSerialization() {
        // 测试JSON序列化 - 使用ObjectMapper
        globalCalendar.addHoliday(LocalDate.of(2024, 1, 1));
        globalCalendar.setSummerTime(LocalDate.of(2024, 3, 1), LocalDate.of(2024, 10, 31));

        try {
            ObjectMapper mapper = new ObjectMapper();
            mapper.findAndRegisterModules();
            String json = mapper.writeValueAsString(globalCalendar);
            assertNotNull(json);
            assertFalse(json.equals("{}"));
            assertTrue(json.contains("holidays"));
            assertTrue(json.contains("summerTime"));
        } catch (Exception e) {
            fail("JSON serialization should not fail: " + e.getMessage());
        }
    }
}
