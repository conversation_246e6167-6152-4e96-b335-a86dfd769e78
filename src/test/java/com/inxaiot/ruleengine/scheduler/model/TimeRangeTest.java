package com.inxaiot.ruleengine.scheduler.model;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TimeRange 单元测试
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
class TimeRangeTest {
    
    @Test
    void testBasicConstructors() {
        // 测试基本构造函数
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 10, 31);
        
        // 无参构造函数
        TimeRange range1 = new TimeRange();
        assertNull(range1.getStartDate());
        assertNull(range1.getEndDate());
        assertNull(range1.getName());
        assertTrue(range1.isEnabled());
        
        // 两参数构造函数
        TimeRange range2 = new TimeRange(start, end);
        assertEquals(start, range2.getStartDate());
        assertEquals(end, range2.getEndDate());
        assertNull(range2.getName());
        assertTrue(range2.isEnabled());
        
        // 三参数构造函数
        TimeRange range3 = new TimeRange(start, end, "测试时间段");
        assertEquals(start, range3.getStartDate());
        assertEquals(end, range3.getEndDate());
        assertEquals("测试时间段", range3.getName());
        assertTrue(range3.isEnabled());
        
        // 四参数构造函数
        TimeRange range4 = new TimeRange(start, end, "禁用时间段", false);
        assertEquals(start, range4.getStartDate());
        assertEquals(end, range4.getEndDate());
        assertEquals("禁用时间段", range4.getName());
        assertFalse(range4.isEnabled());
    }
    
    @Test
    void testContains() {
        // 测试日期包含判断
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 10, 31);
        TimeRange range = new TimeRange(start, end, "测试时间段", true);
        
        // 在范围内的日期
        assertTrue(range.contains(start));  // 开始日期
        assertTrue(range.contains(end));    // 结束日期
        assertTrue(range.contains(LocalDate.of(2024, 6, 15))); // 中间日期
        
        // 在范围外的日期
        assertFalse(range.contains(LocalDate.of(2024, 2, 28))); // 开始之前
        assertFalse(range.contains(LocalDate.of(2024, 11, 1))); // 结束之后
        
        // 边界情况
        assertFalse(range.contains(null));
        
        // 禁用状态
        range.setEnabled(false);
        assertFalse(range.contains(LocalDate.of(2024, 6, 15)));
        
        // 无效范围
        TimeRange invalidRange = new TimeRange();
        assertFalse(invalidRange.contains(LocalDate.of(2024, 6, 15)));
    }
    
    @Test
    void testIsValid() {
        // 测试有效性判断
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 10, 31);
        
        // 有效范围
        TimeRange validRange = new TimeRange(start, end);
        assertTrue(validRange.isValid());
        
        // 开始日期等于结束日期
        TimeRange singleDayRange = new TimeRange(start, start);
        assertTrue(singleDayRange.isValid());
        
        // 开始日期晚于结束日期
        TimeRange invalidRange = new TimeRange(end, start);
        assertFalse(invalidRange.isValid());
        
        // 缺少开始日期
        TimeRange noStartRange = new TimeRange();
        noStartRange.setEndDate(end);
        assertFalse(noStartRange.isValid());
        
        // 缺少结束日期
        TimeRange noEndRange = new TimeRange();
        noEndRange.setStartDate(start);
        assertFalse(noEndRange.isValid());
        
        // 都为null
        TimeRange nullRange = new TimeRange();
        assertFalse(nullRange.isValid());
    }
    
    @Test
    void testGetDays() {
        // 测试天数计算
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 3, 31);
        TimeRange range = new TimeRange(start, end);
        
        assertEquals(31, range.getDays()); // 3月有31天
        
        // 单天范围
        TimeRange singleDay = new TimeRange(start, start);
        assertEquals(1, singleDay.getDays());
        
        // 跨月范围
        TimeRange crossMonth = new TimeRange(
            LocalDate.of(2024, 2, 29), 
            LocalDate.of(2024, 3, 1)
        );
        assertEquals(2, crossMonth.getDays()); // 2月29日和3月1日
        
        // 无效范围
        TimeRange invalidRange = new TimeRange();
        assertEquals(0, invalidRange.getDays());
    }
    
    @Test
    void testOverlaps() {
        // 测试重叠判断
        LocalDate start1 = LocalDate.of(2024, 3, 1);
        LocalDate end1 = LocalDate.of(2024, 6, 30);
        TimeRange range1 = new TimeRange(start1, end1, "春夏时间");
        
        LocalDate start2 = LocalDate.of(2024, 5, 1);
        LocalDate end2 = LocalDate.of(2024, 8, 31);
        TimeRange range2 = new TimeRange(start2, end2, "夏秋时间");
        
        // 重叠情况
        assertTrue(range1.overlaps(range2));
        assertTrue(range2.overlaps(range1));
        
        // 不重叠情况
        TimeRange range3 = new TimeRange(
            LocalDate.of(2024, 9, 1), 
            LocalDate.of(2024, 12, 31), 
            "秋冬时间"
        );
        assertFalse(range1.overlaps(range3));
        assertFalse(range3.overlaps(range1));
        
        // 相邻但不重叠
        TimeRange range4 = new TimeRange(
            LocalDate.of(2024, 7, 1), 
            LocalDate.of(2024, 8, 31), 
            "相邻时间"
        );
        assertFalse(range1.overlaps(range4));
        
        // 边界重叠（结束日期等于开始日期）
        TimeRange range5 = new TimeRange(
            end1, // 6月30日
            LocalDate.of(2024, 9, 30), 
            "边界时间"
        );
        assertTrue(range1.overlaps(range5));
        
        // null和无效范围
        assertFalse(range1.overlaps(null));
        assertFalse(range1.overlaps(new TimeRange()));
    }
    
    @Test
    void testCreateSummerTime() {
        // 测试创建夏令时间的便捷方法
        TimeRange summerTime = TimeRange.createSummerTime(2024);
        
        assertNotNull(summerTime);
        assertEquals(LocalDate.of(2024, 3, 1), summerTime.getStartDate());
        assertEquals(LocalDate.of(2024, 10, 31), summerTime.getEndDate());
        assertEquals("夏令时间", summerTime.getName());
        assertTrue(summerTime.isEnabled());
        assertTrue(summerTime.isValid());
        
        // 验证包含夏季日期
        assertTrue(summerTime.contains(LocalDate.of(2024, 6, 15)));
        assertFalse(summerTime.contains(LocalDate.of(2024, 11, 15)));
    }
    
    @Test
    void testCreateWinterTime() {
        // 测试创建冬令时间的便捷方法
        TimeRange winterTime = TimeRange.createWinterTime(2024);
        
        assertNotNull(winterTime);
        assertEquals(LocalDate.of(2024, 11, 1), winterTime.getStartDate());
        assertEquals(LocalDate.of(2025, 2, 28), winterTime.getEndDate());
        assertEquals("冬令时间", winterTime.getName());
        assertTrue(winterTime.isEnabled());
        assertTrue(winterTime.isValid());
        
        // 验证包含冬季日期
        assertTrue(winterTime.contains(LocalDate.of(2024, 12, 15)));
        assertTrue(winterTime.contains(LocalDate.of(2025, 1, 15)));
        assertFalse(winterTime.contains(LocalDate.of(2024, 10, 15)));
    }
    
    @Test
    void testEqualsAndHashCode() {
        // 测试equals和hashCode
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 10, 31);
        
        TimeRange range1 = new TimeRange(start, end, "测试时间", true);
        TimeRange range2 = new TimeRange(start, end, "测试时间", true);
        TimeRange range3 = new TimeRange(start, end, "不同名称", true);
        TimeRange range4 = new TimeRange(start, end, "测试时间", false);
        
        // 相等情况
        assertEquals(range1, range2);
        assertEquals(range1.hashCode(), range2.hashCode());
        
        // 不相等情况
        assertNotEquals(range1, range3); // 名称不同
        assertNotEquals(range1, range4); // enabled不同
        assertNotEquals(range1, null);
        assertNotEquals(range1, "字符串");
        
        // 自反性
        assertEquals(range1, range1);
    }
    
    @Test
    void testToString() {
        // 测试toString方法
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 10, 31);
        TimeRange range = new TimeRange(start, end, "测试时间段", true);
        
        String str = range.toString();
        assertNotNull(str);
        assertTrue(str.contains("2024-03-01"));
        assertTrue(str.contains("2024-10-31"));
        assertTrue(str.contains("测试时间段"));
        assertTrue(str.contains("enabled=true"));
        assertTrue(str.contains("days=")); // 包含天数信息即可，不验证具体数值
    }
    
    @Test
    void testSettersAndGetters() {
        // 测试setter和getter方法
        TimeRange range = new TimeRange();
        
        LocalDate start = LocalDate.of(2024, 3, 1);
        LocalDate end = LocalDate.of(2024, 10, 31);
        String name = "测试时间段";
        
        range.setStartDate(start);
        range.setEndDate(end);
        range.setName(name);
        range.setEnabled(false);
        
        assertEquals(start, range.getStartDate());
        assertEquals(end, range.getEndDate());
        assertEquals(name, range.getName());
        assertFalse(range.isEnabled());
    }
}
