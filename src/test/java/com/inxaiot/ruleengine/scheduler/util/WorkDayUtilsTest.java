package com.inxaiot.ruleengine.scheduler.util;

import org.junit.jupiter.api.Test;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * WorkDayUtils 单元测试
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
class WorkDayUtilsTest {
    
    @Test
    void testParseWorkDaysShortFormat() {
        // 测试短格式工作日解析
        List<String> workDayStrings = Arrays.asList("MON", "TUE", "WED", "THU", "FRI");
        Set<DayOfWeek> workDays = WorkDayUtils.parseWorkDays(workDayStrings);
        
        assertEquals(5, workDays.size());
        assertTrue(workDays.contains(DayOfWeek.MONDAY));
        assertTrue(workDays.contains(DayOfWeek.TUESDAY));
        assertTrue(workDays.contains(DayOfWeek.WEDNESDAY));
        assertTrue(workDays.contains(DayOfWeek.THURSDAY));
        assertTrue(workDays.contains(DayOfWeek.FRIDAY));
        assertFalse(workDays.contains(DayOfWeek.SATURDAY));
        assertFalse(workDays.contains(DayOfWeek.SUNDAY));
    }
    
    @Test
    void testParseWorkDaysLongFormat() {
        // 测试长格式工作日解析
        List<String> workDayStrings = Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY");
        Set<DayOfWeek> workDays = WorkDayUtils.parseWorkDays(workDayStrings);
        
        assertEquals(5, workDays.size());
        assertTrue(workDays.contains(DayOfWeek.MONDAY));
        assertTrue(workDays.contains(DayOfWeek.FRIDAY));
    }
    
    @Test
    void testParseWorkDaysMixedFormat() {
        // 测试混合格式
        List<String> workDayStrings = Arrays.asList("MON", "TUESDAY", "wed", "THU", "friday");
        Set<DayOfWeek> workDays = WorkDayUtils.parseWorkDays(workDayStrings);
        
        assertEquals(5, workDays.size());
        assertTrue(workDays.contains(DayOfWeek.MONDAY));
        assertTrue(workDays.contains(DayOfWeek.TUESDAY));
        assertTrue(workDays.contains(DayOfWeek.WEDNESDAY));
        assertTrue(workDays.contains(DayOfWeek.THURSDAY));
        assertTrue(workDays.contains(DayOfWeek.FRIDAY));
    }
    
    @Test
    void testParseWorkDaysWithInvalidValues() {
        // 测试包含无效值的情况
        List<String> workDayStrings = Arrays.asList("MON", "INVALID", "TUE", "", null, "WED");
        Set<DayOfWeek> workDays = WorkDayUtils.parseWorkDays(workDayStrings);
        
        assertEquals(3, workDays.size());
        assertTrue(workDays.contains(DayOfWeek.MONDAY));
        assertTrue(workDays.contains(DayOfWeek.TUESDAY));
        assertTrue(workDays.contains(DayOfWeek.WEDNESDAY));
    }
    
    @Test
    void testParseWorkDaysEmptyList() {
        // 测试空列表
        Set<DayOfWeek> workDays = WorkDayUtils.parseWorkDays(null);
        assertTrue(workDays.isEmpty());
        
        workDays = WorkDayUtils.parseWorkDays(Arrays.asList());
        assertTrue(workDays.isEmpty());
    }
    
    @Test
    void testToShortNames() {
        // 测试转换为短格式名称
        Set<DayOfWeek> workDays = WorkDayUtils.getStandardWorkDays();
        List<String> shortNames = WorkDayUtils.toShortNames(workDays);
        
        assertEquals(5, shortNames.size());
        assertTrue(shortNames.contains("MON"));
        assertTrue(shortNames.contains("TUE"));
        assertTrue(shortNames.contains("WED"));
        assertTrue(shortNames.contains("THU"));
        assertTrue(shortNames.contains("FRI"));
    }
    
    @Test
    void testToLongNames() {
        // 测试转换为长格式名称
        Set<DayOfWeek> workDays = WorkDayUtils.getStandardWorkDays();
        List<String> longNames = WorkDayUtils.toLongNames(workDays);
        
        assertEquals(5, longNames.size());
        assertTrue(longNames.contains("MONDAY"));
        assertTrue(longNames.contains("TUESDAY"));
        assertTrue(longNames.contains("WEDNESDAY"));
        assertTrue(longNames.contains("THURSDAY"));
        assertTrue(longNames.contains("FRIDAY"));
    }
    
    @Test
    void testIsWorkDay() {
        // 测试工作日判断
        List<String> workDayStrings = Arrays.asList("MON", "TUE", "WED", "THU", "FRI");
        
        // 2024年12月19日是周四，应该是工作日
        LocalDate thursday = LocalDate.of(2024, 12, 19);
        assertTrue(WorkDayUtils.isWorkDay(thursday, workDayStrings));
        
        // 2024年12月21日是周六，不应该是工作日
        LocalDate saturday = LocalDate.of(2024, 12, 21);
        assertFalse(WorkDayUtils.isWorkDay(saturday, workDayStrings));
        
        // 2024年12月22日是周日，不应该是工作日
        LocalDate sunday = LocalDate.of(2024, 12, 22);
        assertFalse(WorkDayUtils.isWorkDay(sunday, workDayStrings));
    }
    
    @Test
    void testIsWeekend() {
        // 测试周末判断
        LocalDate saturday = LocalDate.of(2024, 12, 21);
        assertTrue(WorkDayUtils.isWeekend(saturday));
        
        LocalDate sunday = LocalDate.of(2024, 12, 22);
        assertTrue(WorkDayUtils.isWeekend(sunday));
        
        LocalDate monday = LocalDate.of(2024, 12, 23);
        assertFalse(WorkDayUtils.isWeekend(monday));
        
        assertFalse(WorkDayUtils.isWeekend(null));
    }
    
    @Test
    void testGetStandardWorkDays() {
        // 测试获取标准工作日
        Set<DayOfWeek> standardWorkDays = WorkDayUtils.getStandardWorkDays();
        
        assertEquals(5, standardWorkDays.size());
        assertTrue(standardWorkDays.contains(DayOfWeek.MONDAY));
        assertTrue(standardWorkDays.contains(DayOfWeek.TUESDAY));
        assertTrue(standardWorkDays.contains(DayOfWeek.WEDNESDAY));
        assertTrue(standardWorkDays.contains(DayOfWeek.THURSDAY));
        assertTrue(standardWorkDays.contains(DayOfWeek.FRIDAY));
        assertFalse(standardWorkDays.contains(DayOfWeek.SATURDAY));
        assertFalse(standardWorkDays.contains(DayOfWeek.SUNDAY));
    }
    
    @Test
    void testGetWeekends() {
        // 测试获取周末
        Set<DayOfWeek> weekends = WorkDayUtils.getWeekends();
        
        assertEquals(2, weekends.size());
        assertTrue(weekends.contains(DayOfWeek.SATURDAY));
        assertTrue(weekends.contains(DayOfWeek.SUNDAY));
        assertFalse(weekends.contains(DayOfWeek.MONDAY));
    }
    
    @Test
    void testIsValidWorkDayString() {
        // 测试工作日字符串验证
        assertTrue(WorkDayUtils.isValidWorkDayString("MON"));
        assertTrue(WorkDayUtils.isValidWorkDayString("MONDAY"));
        assertTrue(WorkDayUtils.isValidWorkDayString("mon"));
        assertTrue(WorkDayUtils.isValidWorkDayString("monday"));
        assertTrue(WorkDayUtils.isValidWorkDayString(" TUE "));
        
        assertFalse(WorkDayUtils.isValidWorkDayString("INVALID"));
        assertFalse(WorkDayUtils.isValidWorkDayString(""));
        assertFalse(WorkDayUtils.isValidWorkDayString(null));
        assertFalse(WorkDayUtils.isValidWorkDayString("   "));
    }
    
    @Test
    void testGetStandardWorkDayNames() {
        // 测试获取标准工作日名称
        List<String> shortNames = WorkDayUtils.getStandardWorkDayShortNames();
        assertEquals(Arrays.asList("MON", "TUE", "WED", "THU", "FRI"), shortNames);
        
        List<String> longNames = WorkDayUtils.getStandardWorkDayLongNames();
        assertEquals(Arrays.asList("MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"), longNames);
    }
    
    @Test
    void testGetWeekendNames() {
        // 测试获取周末名称
        List<String> weekendNames = WorkDayUtils.getWeekendShortNames();
        assertEquals(Arrays.asList("SAT", "SUN"), weekendNames);
    }
    
    @Test
    void testSupportedFormats() {
        // 测试格式说明
        String formats = WorkDayUtils.getSupportedFormats();
        assertNotNull(formats);
        assertTrue(formats.contains("MON"));
        assertTrue(formats.contains("MONDAY"));
    }
    
    @Test
    void testCustomWorkDays() {
        // 测试自定义工作日（比如周六也是工作日）
        List<String> customWorkDays = Arrays.asList("MON", "TUE", "WED", "THU", "FRI", "SAT");
        
        LocalDate saturday = LocalDate.of(2024, 12, 21);
        assertTrue(WorkDayUtils.isWorkDay(saturday, customWorkDays));
        
        LocalDate sunday = LocalDate.of(2024, 12, 22);
        assertFalse(WorkDayUtils.isWorkDay(sunday, customWorkDays));
    }
}
