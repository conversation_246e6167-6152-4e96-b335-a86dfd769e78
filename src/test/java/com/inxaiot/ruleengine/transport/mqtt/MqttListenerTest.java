package com.inxaiot.ruleengine.transport.mqtt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.device.state.StateManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * MqttListener unit test
 * Test core functionality of MQTT message listener
 */
@ExtendWith(MockitoExtension.class)
public class MqttListenerTest {

    @Mock
    private StateManager stateManager;

    private MqttListener mqttListener;

    @BeforeEach
    void setUp() {
        mqttListener = new MqttListener(stateManager, new ObjectMapper());
    }

    /**
     * Test handling MQTT message - standard topic format
     */
    @Test
    void testHandleMessage_StandardTopicFormat() {
        // Given
        String topic = "/device/temp_sensor_001/temperature";
        String payload = "{\"value\": 28.5, \"dataType\": \"DOUBLE\", \"timestamp\": 1703001600000}";

        // When
        mqttListener.handleMessage(topic, payload);

        // Then
        ArgumentCaptor<String> deviceIdCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> pointIdCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<Object> valueCaptor = ArgumentCaptor.forClass(Object.class);
        ArgumentCaptor<String> dataTypeCaptor = ArgumentCaptor.forClass(String.class);

        verify(stateManager).processDevicePointUpdate(
            deviceIdCaptor.capture(),
            pointIdCaptor.capture(),
            valueCaptor.capture(),
            dataTypeCaptor.capture()
        );

        assertEquals("temp_sensor_001", deviceIdCaptor.getValue());
        assertEquals("temperature", pointIdCaptor.getValue());
        assertEquals(28.5, valueCaptor.getValue());
        assertEquals("DOUBLE", dataTypeCaptor.getValue());
    }

    /**
     * Test handling MQTT message - boolean value
     */
    @Test
    void testHandleMessage_BooleanValue() {
        // Given
        String topic = "/device/occupancy_sensor_001/occupied";
        String payload = "{\"value\": true, \"dataType\": \"BOOLEAN\", \"timestamp\": 1703001600000}";

        // When
        mqttListener.handleMessage(topic, payload);

        // Then
        ArgumentCaptor<Object> valueCaptor = ArgumentCaptor.forClass(Object.class);
        ArgumentCaptor<String> dataTypeCaptor = ArgumentCaptor.forClass(String.class);

        verify(stateManager).processDevicePointUpdate(
            eq("occupancy_sensor_001"),
            eq("occupied"),
            valueCaptor.capture(),
            dataTypeCaptor.capture()
        );

        assertEquals(true, valueCaptor.getValue());
        assertEquals("BOOLEAN", dataTypeCaptor.getValue());
    }

    /**
     * Test handling invalid topic format
     */
    @Test
    void testHandleMessage_InvalidTopicFormat() {
        // Given
        String topic = "invalid/topic";
        String payload = "{\"value\": 25.5, \"dataType\": \"DOUBLE\"}";

        // When
        mqttListener.handleMessage(topic, payload);

        // Then
        verify(stateManager, never()).processDevicePointUpdate(any(), any(), any(), any());
    }

    /**
     * Test handling invalid JSON payload
     */
    @Test
    void testHandleMessage_InvalidJsonPayload() {
        // Given
        String topic = "/device/temp_sensor_001/temperature";
        String payload = "invalid json";

        // When
        mqttListener.handleMessage(topic, payload);

        // Then
        verify(stateManager, never()).processDevicePointUpdate(any(), any(), any(), any());
    }

    /**
     * Test handling empty payload
     */
    @Test
    void testHandleMessage_EmptyPayload() {
        // Given
        String topic = "/device/temp_sensor_001/temperature";
        String payload = "";

        // When
        mqttListener.handleMessage(topic, payload);

        // Then
        verify(stateManager, never()).processDevicePointUpdate(any(), any(), any(), any());
    }

    /**
     * Test exception handling
     */
    @Test
    void testHandleMessage_StateManagerException() {
        // Given
        String topic = "/device/temp_sensor_001/temperature";
        String payload = "{\"value\": 25.5, \"dataType\": \"DOUBLE\", \"timestamp\": 1703001600000}";

        doThrow(new RuntimeException("State processing failed"))
            .when(stateManager).processDevicePointUpdate(any(), any(), any(), any());

        // When & Then
        assertDoesNotThrow(() -> {
            mqttListener.handleMessage(topic, payload);
        });

        verify(stateManager).processDevicePointUpdate(any(), any(), any(), any());
    }
}
