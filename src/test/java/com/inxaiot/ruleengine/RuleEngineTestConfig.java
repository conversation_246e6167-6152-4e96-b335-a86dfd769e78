package com.inxaiot.ruleengine;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;

/**
 * 测试配置类
 * 提供测试环境所需的Bean配置
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@TestConfiguration
public class RuleEngineTestConfig {

    /**
     * 测试用规则评估线程池
     */
    @Bean("ruleEvaluationExecutor")
    @Primary
    public Executor testRuleEvaluationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("test-rule-eval-");
        executor.initialize();
        return executor;
    }

    /**
     * 测试用动作执行线程池
     */
    @Bean("actionExecutionExecutor")
    @Primary
    public Executor testActionExecutionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(2);
        executor.setMaxPoolSize(4);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("test-action-exec-");
        executor.initialize();
        return executor;
    }

    /**
     * 测试用设备状态调度器
     */
    @Bean("deviceStateScheduler")
    @Primary
    public ScheduledExecutorService testDeviceStateScheduler() {
        return Executors.newScheduledThreadPool(1);
    }

    /**
     * 测试用时间触发调度器
     */
    @Bean("timeTriggerScheduler")
    @Primary
    public ScheduledExecutorService testTimeTriggerScheduler() {
        return Executors.newScheduledThreadPool(1);
    }
}
