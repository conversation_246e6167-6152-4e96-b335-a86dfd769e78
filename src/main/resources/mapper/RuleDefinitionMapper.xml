<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.inxaiot.ruleengine.storage.mapper.RuleDefinitionMapper">

    <!-- 结果映射 -->
    <resultMap id="RuleDefinitionResultMap" type="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="rule_id" property="ruleId" jdbcType="VARCHAR"/>
        <result column="rule_name" property="ruleName" jdbcType="VARCHAR"/>
        <result column="target_device_id" property="targetDeviceId" jdbcType="VARCHAR"/>
        <result column="target_device_type" property="targetDeviceType" jdbcType="VARCHAR"/>
        <result column="biz_id" property="bizId" jdbcType="VARCHAR"/>
        <result column="group_id" property="groupId" jdbcType="VARCHAR"/>
        <result column="priority" property="priority" jdbcType="INTEGER"/>
        <result column="enabled" property="enabled" jdbcType="BOOLEAN"/>
        <result column="time_conditions" property="timeConditions" jdbcType="VARCHAR"/>
        <result column="trigger_conditions" property="triggerCondition" jdbcType="VARCHAR"/>
        <result column="actions" property="actions" jdbcType="VARCHAR"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, rule_id, rule_name, target_device_id, target_device_type, biz_id, group_id,
        priority, enabled, time_conditions, trigger_conditions, actions, description,
        create_time, update_time
    </sql>

    <!-- 插入规则定义 -->
    <insert id="insertRule" parameterType="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO rule_definition (
            rule_id, rule_name, target_device_id, target_device_type, biz_id, group_id,
            priority, enabled, time_conditions, trigger_conditions, actions, description
        ) VALUES (
            #{ruleId}, #{ruleName}, #{targetDeviceId}, #{targetDeviceType}, #{bizId}, #{groupId},
            #{priority}, #{enabled}, #{timeConditions}, #{triggerCondition}, #{actions}, #{description}
        )
    </insert>

    <!-- 根据ID更新规则定义 -->
    <update id="updateRuleById" parameterType="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity">
        UPDATE rule_definition SET
            rule_name = #{ruleName},
            target_device_id = #{targetDeviceId},
            target_device_type = #{targetDeviceType},
            biz_id = #{bizId},
            group_id = #{groupId},
            priority = #{priority},
            enabled = #{enabled},
            time_conditions = #{timeConditions},
            trigger_conditions = #{triggerCondition},
            actions = #{actions},
            description = #{description}
        WHERE id = #{id}
    </update>

    <!-- 根据ruleId更新规则定义 -->
    <update id="updateRuleByRuleId" parameterType="com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity">
        UPDATE rule_definition SET
            rule_name = #{ruleName},
            target_device_id = #{targetDeviceId},
            target_device_type = #{targetDeviceType},
            biz_id = #{bizId},
            group_id = #{groupId},
            priority = #{priority},
            enabled = #{enabled},
            time_conditions = #{timeConditions},
            trigger_conditions = #{triggerCondition},
            actions = #{actions},
            description = #{description}
        WHERE rule_id = #{ruleId}
    </update>

    <!-- 根据ID删除规则定义 -->
    <delete id="deleteRuleById" parameterType="java.lang.Long">
        DELETE FROM rule_definition WHERE id = #{id}
    </delete>

    <!-- 根据ruleId删除规则定义 -->
    <delete id="deleteRuleByRuleId" parameterType="java.lang.String">
        DELETE FROM rule_definition WHERE rule_id = #{ruleId}
    </delete>

    <!-- 根据ID查询规则定义 -->
    <select id="findRuleById" parameterType="java.lang.Long" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE id = #{id}
    </select>

    <!-- 根据ruleId查询规则定义 -->
    <select id="findRuleByRuleId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE rule_id = #{ruleId}
    </select>

    <!-- 查询所有规则定义 -->
    <select id="findAllRules" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 查询所有启用的规则定义 -->
    <select id="findAllEnabledRules" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE enabled = 1
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据目标设备ID查询规则定义 -->
    <select id="findRulesByTargetDeviceId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE target_device_id = #{targetDeviceId}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据目标设备ID查询启用的规则定义 -->
    <select id="findEnabledRulesByTargetDeviceId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE target_device_id = #{targetDeviceId} AND enabled = 1
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据业务ID查询规则定义 -->
    <select id="findRulesByBizId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE biz_id = #{bizId}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据分组ID查询规则定义 -->
    <select id="findRulesByGroupId" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE group_id = #{groupId}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据设备类型查询规则定义 -->
    <select id="findRulesByDeviceType" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE target_device_type = #{deviceType}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 查询与指定设备相关的所有启用规则 -->
    <select id="findAllEnabledRulesRelevantToDevice" parameterType="java.lang.String" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE enabled = 1 AND (
            target_device_id = #{deviceId} OR
            trigger_conditions LIKE CONCAT('%', #{deviceId}, '%')
        )
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 根据优先级范围查询规则定义 -->
    <select id="findRulesByPriorityRange" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        WHERE priority BETWEEN #{minPriority} AND #{maxPriority}
        ORDER BY priority ASC, create_time DESC
    </select>

    <!-- 批量插入规则定义 -->
    <insert id="batchInsertRules" parameterType="java.util.List">
        INSERT INTO rule_definition (
            rule_id, rule_name, target_device_id, target_device_type, biz_id, group_id,
            priority, enabled, time_conditions, trigger_conditions, actions, description
        ) VALUES
        <foreach collection="rules" item="rule" separator=",">
            (#{rule.ruleId}, #{rule.ruleName}, #{rule.targetDeviceId}, #{rule.targetDeviceType}, 
             #{rule.bizId}, #{rule.groupId}, #{rule.priority}, #{rule.enabled}, 
             #{rule.timeConditions}, #{rule.triggerCondition}, #{rule.actions}, #{rule.description})
        </foreach>
    </insert>

    <!-- 批量更新规则启用状态 -->
    <update id="batchUpdateRuleEnabled">
        UPDATE rule_definition SET enabled = #{enabled}
        WHERE rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </update>

    <!-- 批量删除规则定义 -->
    <delete id="batchDeleteRulesByRuleIds">
        DELETE FROM rule_definition
        WHERE rule_id IN
        <foreach collection="ruleIds" item="ruleId" open="(" separator="," close=")">
            #{ruleId}
        </foreach>
    </delete>

    <!-- 根据业务ID批量删除规则定义 -->
    <delete id="batchDeleteRulesByBizId" parameterType="java.lang.String">
        DELETE FROM rule_definition WHERE biz_id = #{bizId}
    </delete>

    <!-- 根据分组ID批量删除规则定义 -->
    <delete id="batchDeleteRulesByGroupId" parameterType="java.lang.String">
        DELETE FROM rule_definition WHERE group_id = #{groupId}
    </delete>

    <!-- 统计规则总数 -->
    <select id="countAllRules" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rule_definition
    </select>

    <!-- 统计启用的规则数 -->
    <select id="countEnabledRules" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rule_definition WHERE enabled = 1
    </select>

    <!-- 根据设备ID统计相关规则数 -->
    <select id="countRulesByDeviceId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rule_definition
        WHERE target_device_id = #{deviceId} OR trigger_conditions LIKE CONCAT('%', #{deviceId}, '%')
    </select>

    <!-- 根据业务ID统计规则数 -->
    <select id="countRulesByBizId" parameterType="java.lang.String" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rule_definition WHERE biz_id = #{bizId}
    </select>

    <!-- 检查ruleId是否存在 -->
    <select id="existsRuleByRuleId" parameterType="java.lang.String" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0 FROM rule_definition WHERE rule_id = #{ruleId}
    </select>

    <!-- 分页查询规则定义 -->
    <select id="findRulesWithPagination" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        ORDER BY priority ASC, create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

    <!-- 根据条件分页查询规则定义 -->
    <select id="findRulesByConditionWithPagination" resultMap="RuleDefinitionResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM rule_definition
        <where>
            <if test="targetDeviceId != null and targetDeviceId != ''">
                AND target_device_id = #{targetDeviceId}
            </if>
            <if test="bizId != null and bizId != ''">
                AND biz_id = #{bizId}
            </if>
            <if test="groupId != null and groupId != ''">
                AND group_id = #{groupId}
            </if>
            <if test="enabled != null">
                AND enabled = #{enabled}
            </if>
        </where>
        ORDER BY priority ASC, create_time DESC
        LIMIT #{limit} OFFSET #{offset}
    </select>

</mapper>
