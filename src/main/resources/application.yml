server:
  port: 8080
  servlet:
    context-path: /rule-engine

spring:
  application:
    name: iot-rule-engine
  
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    driver-class-name: org.sqlite.JDBC
    url: jdbc:sqlite:${rule.engine.data.path:./data}/rules_engine.db
    username: 
    password: 
    
  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
    deserialization:
      fail-on-unknown-properties: false

# MyBatis配置
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.inxaiot.ruleengine.storage.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    lazy-loading-enabled: true
    multiple-result-sets-enabled: true
    use-column-label: true
    use-generated-keys: true
    auto-mapping-behavior: partial
    default-executor-type: simple
    default-statement-timeout: 25000

# 日志配置
logging:
  level:
    com.inxaiot.ruleengine: DEBUG
    org.springframework: INFO
    org.mybatis: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: ${rule.engine.log.path:./logs}/rule-engine.log
  logback:
    rollingpolicy:
      max-history: 30
      max-file-size: 100MB

# 规则引擎配置
rule:
  engine:
    # 基础标识配置
    id: edge-rule-engine
    region:
      id: region-001
    building:
      id: building-A
    version: 1.0.0
    timezone: Asia/Shanghai
    locale: zh_CN
    debug: false

    # 数据存储路径
    data:
      path: ./data
    # 日志路径
    log:
      path: ./logs
    # 规则执行配置
    execution:
      # 是否跳过第一个匹配的规则后继续执行其他规则
      skip-on-first-applied: false
      # 规则优先级阈值
      priority-threshold: **********
      # 规则执行超时时间(秒)
      timeout: 30
    # 状态管理配置
    state:
      # 设备状态缓存大小
      cache-size: 10000
      # 状态清理间隔(分钟)
      cleanup-interval: 60
    # 时间条件配置
    time:
      # 全局日历刷新间隔(小时)
      calendar-refresh-interval: 24
      # 时区
      timezone: Asia/Shanghai

# 监控配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  
logging:
  level:
    root: INFO
    com.inxaiot.ruleengine: DEBUG

rule:
  engine:
    data:
      path: dev-data
    log:
      path: dev-logs

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod

logging:
  level:
    root: WARN
    com.inxaiot.ruleengine: INFO

rule:
  engine:
    data:
      path: /opt/rule-engine/data
    log:
      path: /opt/rule-engine/logs
