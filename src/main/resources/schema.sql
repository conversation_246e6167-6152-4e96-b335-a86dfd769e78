-- 物联网规则引擎数据库表结构
-- 数据库: SQLite
-- 创建时间: 2024-12-19

-- 规则定义表
CREATE TABLE IF NOT EXISTS rule_definition (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id VARCHAR(64) NOT NULL UNIQUE,
    rule_name VARCHAR(255),
    target_device_id VARCHAR(64),
    target_device_type VARCHAR(32),
    biz_id VARCHAR(64),
    group_id VARCHAR(64),
    priority INTEGER DEFAULT 1,
    enabled BOOLEAN DEFAULT 1,
    time_conditions TEXT,  -- JSON格式存储时间条件
    trigger_conditions TEXT,  -- JSON格式存储触发条件
    actions TEXT,  -- JSON格式存储动作列表
    description TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_rule_definition_rule_id ON rule_definition(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_definition_target_device ON rule_definition(target_device_id);
CREATE INDEX IF NOT EXISTS idx_rule_definition_biz_id ON rule_definition(biz_id);
CREATE INDEX IF NOT EXISTS idx_rule_definition_group_id ON rule_definition(group_id);
CREATE INDEX IF NOT EXISTS idx_rule_definition_enabled ON rule_definition(enabled);

-- 全局日历表
CREATE TABLE IF NOT EXISTS global_calendar (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    calendar_date DATE NOT NULL UNIQUE,
    calendar_type VARCHAR(32) NOT NULL,  -- WORKDAY, HOLIDAY, WEEKEND, SPRING, SUMMER, AUTUMN, WINTER
    description VARCHAR(255),
    enabled BOOLEAN DEFAULT 1,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_global_calendar_date ON global_calendar(calendar_date);
CREATE INDEX IF NOT EXISTS idx_global_calendar_type ON global_calendar(calendar_type);
CREATE INDEX IF NOT EXISTS idx_global_calendar_enabled ON global_calendar(enabled);

-- 设备信息表
CREATE TABLE IF NOT EXISTS device_info (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(64) NOT NULL UNIQUE,
    device_name VARCHAR(255),
    device_type VARCHAR(32),
    location VARCHAR(255),
    status VARCHAR(16) DEFAULT 'ONLINE',  -- ONLINE, OFFLINE, MAINTENANCE
    properties TEXT,  -- JSON格式存储设备属性
    description TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_device_info_device_id ON device_info(device_id);
CREATE INDEX IF NOT EXISTS idx_device_info_device_type ON device_info(device_type);
CREATE INDEX IF NOT EXISTS idx_device_info_status ON device_info(status);

-- 设备状态表 (用于存储设备的实时状态)
CREATE TABLE IF NOT EXISTS device_state (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    device_id VARCHAR(64) NOT NULL,
    point_id VARCHAR(64) NOT NULL,
    point_value TEXT,
    data_type VARCHAR(16) DEFAULT 'STRING',  -- STRING, INTEGER, DOUBLE, BOOLEAN
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(device_id, point_id)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_device_state_device_id ON device_state(device_id);
CREATE INDEX IF NOT EXISTS idx_device_state_point_id ON device_state(point_id);
CREATE INDEX IF NOT EXISTS idx_device_state_update_time ON device_state(update_time);

-- 规则执行日志表
CREATE TABLE IF NOT EXISTS rule_execution_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    rule_id VARCHAR(64) NOT NULL,
    execution_id VARCHAR(64) NOT NULL,
    trigger_device_id VARCHAR(64),
    trigger_point_id VARCHAR(64),
    trigger_value TEXT,
    execution_status VARCHAR(16),  -- SUCCESS, FAILED, TIMEOUT
    execution_result TEXT,
    error_message TEXT,
    execution_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    duration_ms INTEGER
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_rule_execution_log_rule_id ON rule_execution_log(rule_id);
CREATE INDEX IF NOT EXISTS idx_rule_execution_log_execution_time ON rule_execution_log(execution_time);
CREATE INDEX IF NOT EXISTS idx_rule_execution_log_status ON rule_execution_log(execution_status);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(64) NOT NULL UNIQUE,
    config_value TEXT,
    config_type VARCHAR(16) DEFAULT 'STRING',  -- STRING, INTEGER, DOUBLE, BOOLEAN, JSON
    description TEXT,
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_system_config_key ON system_config(config_key);

-- 插入默认系统配置
INSERT OR IGNORE INTO system_config (config_key, config_value, config_type, description) VALUES
('rule.engine.version', '1.0.0', 'STRING', '规则引擎版本'),
('rule.engine.timezone', 'Asia/Shanghai', 'STRING', '系统时区'),
('rule.engine.max_rules', '5000', 'INTEGER', '最大规则数量'),
('rule.engine.execution.timeout', '30', 'INTEGER', '规则执行超时时间(秒)'),
('rule.engine.state.cleanup.interval', '60', 'INTEGER', '状态清理间隔(分钟)'),
('calendar.refresh.interval', '24', 'INTEGER', '日历刷新间隔(小时)');

-- 插入默认全局日历数据 (示例)
-- 2024年的一些节假日和季节定义
INSERT OR IGNORE INTO global_calendar (calendar_date, calendar_type, description) VALUES
('2024-01-01', 'HOLIDAY', '元旦'),
('2024-02-10', 'HOLIDAY', '春节'),
('2024-02-11', 'HOLIDAY', '春节'),
('2024-02-12', 'HOLIDAY', '春节'),
('2024-04-04', 'HOLIDAY', '清明节'),
('2024-05-01', 'HOLIDAY', '劳动节'),
('2024-06-10', 'HOLIDAY', '端午节'),
('2024-09-15', 'HOLIDAY', '中秋节'),
('2024-10-01', 'HOLIDAY', '国庆节'),
('2024-10-02', 'HOLIDAY', '国庆节'),
('2024-10-03', 'HOLIDAY', '国庆节');

-- 季节定义 (示例)
INSERT OR IGNORE INTO global_calendar (calendar_date, calendar_type, description) VALUES
('2024-03-20', 'SPRING', '春季开始'),
('2024-06-21', 'SUMMER', '夏季开始'),
('2024-09-23', 'AUTUMN', '秋季开始'),
('2024-12-21', 'WINTER', '冬季开始');

-- 创建触发器，自动更新update_time字段
CREATE TRIGGER IF NOT EXISTS update_rule_definition_time 
    AFTER UPDATE ON rule_definition
    FOR EACH ROW
    BEGIN
        UPDATE rule_definition SET update_time = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_global_calendar_time 
    AFTER UPDATE ON global_calendar
    FOR EACH ROW
    BEGIN
        UPDATE global_calendar SET update_time = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_device_info_time 
    AFTER UPDATE ON device_info
    FOR EACH ROW
    BEGIN
        UPDATE device_info SET update_time = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;

CREATE TRIGGER IF NOT EXISTS update_system_config_time 
    AFTER UPDATE ON system_config
    FOR EACH ROW
    BEGIN
        UPDATE system_config SET update_time = CURRENT_TIMESTAMP WHERE id = NEW.id;
    END;
