package com.inxaiot.ruleengine.app;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 规则引擎配置类
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Configuration
public class RuleEngineConfig {

    /**
     * 配置Jackson ObjectMapper
     */
    @Bean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.registerModule(new JavaTimeModule());
        return mapper;
    }

    /**
     * 规则评估线程池
     * 用于规则条件判断和规则引擎核心处理 - CPU密集型任务
     */
    @Bean("ruleEvaluationExecutor")
    public ThreadPoolTaskExecutor ruleEvaluationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors()); // CPU核心数
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("RuleEval-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 动作执行线程池
     * 用于执行规则动作（设备控制、API调用、消息发送等）- IO密集型任务
     */
    @Bean("actionExecutionExecutor")
    public ThreadPoolTaskExecutor actionExecutionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);  // IO密集型，需要更多线程
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("ActionExec-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 设备状态管理定时任务执行器
     * 用于设备状态超时检查等定时任务
     */
    @Bean("deviceStateScheduler")
    public ScheduledExecutorService deviceStateScheduler() {
        return new ScheduledThreadPoolExecutor(2, r -> {
            Thread thread = new Thread(r);
            thread.setName("DeviceState-Scheduler-" + thread.getId());
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 时间触发器定时任务执行器
     * 用于时间驱动规则的定时检查（预留给未来的TimeTriggerService）
     */
    @Bean("timeTriggerScheduler")
    public ScheduledExecutorService timeTriggerScheduler() {
        return new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("TimeTrigger-Scheduler-" + thread.getId());
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 规则引擎配置属性
     */
    @Component
    @ConfigurationProperties(prefix = "rule.engine")
    public static class RuleEngineProperties {
        
        private Data data = new Data();
        private Log log = new Log();
        private Execution execution = new Execution();
        private State state = new State();
        private Time time = new Time();

        // Getters and Setters
        public Data getData() { return data; }
        public void setData(Data data) { this.data = data; }
        
        public Log getLog() { return log; }
        public void setLog(Log log) { this.log = log; }
        
        public Execution getExecution() { return execution; }
        public void setExecution(Execution execution) { this.execution = execution; }
        
        public State getState() { return state; }
        public void setState(State state) { this.state = state; }
        
        public Time getTime() { return time; }
        public void setTime(Time time) { this.time = time; }

        public static class Data {
            private String path = "./data";
            
            public String getPath() { return path; }
            public void setPath(String path) { this.path = path; }
        }

        public static class Log {
            private String path = "./logs";
            
            public String getPath() { return path; }
            public void setPath(String path) { this.path = path; }
        }

        public static class Execution {
            private boolean skipOnFirstApplied = false;
            private int priorityThreshold = Integer.MAX_VALUE;
            private int timeout = 30;
            
            public boolean isSkipOnFirstApplied() { return skipOnFirstApplied; }
            public void setSkipOnFirstApplied(boolean skipOnFirstApplied) { this.skipOnFirstApplied = skipOnFirstApplied; }
            
            public int getPriorityThreshold() { return priorityThreshold; }
            public void setPriorityThreshold(int priorityThreshold) { this.priorityThreshold = priorityThreshold; }
            
            public int getTimeout() { return timeout; }
            public void setTimeout(int timeout) { this.timeout = timeout; }
        }

        public static class State {
            private int cacheSize = 10000;
            private int cleanupInterval = 60;
            
            public int getCacheSize() { return cacheSize; }
            public void setCacheSize(int cacheSize) { this.cacheSize = cacheSize; }
            
            public int getCleanupInterval() { return cleanupInterval; }
            public void setCleanupInterval(int cleanupInterval) { this.cleanupInterval = cleanupInterval; }
        }

        public static class Time {
            private int calendarRefreshInterval = 24;
            private String timezone = "Asia/Shanghai";
            
            public int getCalendarRefreshInterval() { return calendarRefreshInterval; }
            public void setCalendarRefreshInterval(int calendarRefreshInterval) { this.calendarRefreshInterval = calendarRefreshInterval; }
            
            public String getTimezone() { return timezone; }
            public void setTimezone(String timezone) { this.timezone = timezone; }
        }
    }
}
