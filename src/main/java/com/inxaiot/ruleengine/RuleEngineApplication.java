package com.inxaiot.ruleengine;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 物联网规则引擎主启动类
 * SystemContextService
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@SpringBootApplication
@MapperScan("com.inxaiot.ruleengine.storage.mapper")
@EnableScheduling
public class RuleEngineApplication {

    public static void main(String[] args) {
        SpringApplication.run(RuleEngineApplication.class, args);
    }
}
