package com.inxaiot.ruleengine.trigger.time;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;

/**
 * 时间范围模型
 * 用于表示夏令时间、冬令时间等时间段
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class TimeRange {
    
    /**
     * 开始日期
     */
    @JsonProperty("startDate")
    private LocalDate startDate;
    
    /**
     * 结束日期
     */
    @JsonProperty("endDate")
    private LocalDate endDate;
    
    /**
     * 时间范围名称/描述
     */
    @JsonProperty("name")
    private String name;
    
    /**
     * 是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    // 构造函数
    public TimeRange() {
    }
    
    public TimeRange(LocalDate startDate, LocalDate endDate) {
        this.startDate = startDate;
        this.endDate = endDate;
    }
    
    public TimeRange(LocalDate startDate, LocalDate endDate, String name) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.name = name;
    }
    
    public TimeRange(LocalDate startDate, LocalDate endDate, String name, boolean enabled) {
        this.startDate = startDate;
        this.endDate = endDate;
        this.name = name;
        this.enabled = enabled;
    }
    
    // Getters and Setters
    public LocalDate getStartDate() {
        return startDate;
    }
    
    public void setStartDate(LocalDate startDate) {
        this.startDate = startDate;
    }
    
    public LocalDate getEndDate() {
        return endDate;
    }
    
    public void setEndDate(LocalDate endDate) {
        this.endDate = endDate;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    /**
     * 检查指定日期是否在时间范围内
     * 
     * @param date 要检查的日期
     * @return 如果在范围内返回true，否则返回false
     */
    public boolean contains(LocalDate date) {
        if (!enabled || date == null || startDate == null || endDate == null) {
            return false;
        }
        
        return !date.isBefore(startDate) && !date.isAfter(endDate);
    }
    
    /**
     * 检查时间范围是否有效
     * 
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValid() {
        return startDate != null && endDate != null && !startDate.isAfter(endDate);
    }
    
    /**
     * 获取时间范围的天数
     * 
     * @return 天数，如果范围无效返回0
     */
    public long getDays() {
        if (!isValid()) {
            return 0;
        }
        
        return startDate.until(endDate).getDays() + 1; // 包含结束日期
    }
    
    /**
     * 检查是否与另一个时间范围重叠
     * 
     * @param other 另一个时间范围
     * @return 如果重叠返回true，否则返回false
     */
    public boolean overlaps(TimeRange other) {
        if (other == null || !this.isValid() || !other.isValid()) {
            return false;
        }
        
        return !this.endDate.isBefore(other.startDate) && !this.startDate.isAfter(other.endDate);
    }
    
    /**
     * 创建夏令时间范围的便捷方法
     * 
     * @param year 年份
     * @return 夏令时间范围（通常是3月到10月）
     */
    public static TimeRange createSummerTime(int year) {
        LocalDate startDate = LocalDate.of(year, 3, 1);  // 3月1日开始
        LocalDate endDate = LocalDate.of(year, 10, 31);  // 10月31日结束
        return new TimeRange(startDate, endDate, "夏令时间", true);
    }
    
    /**
     * 创建冬令时间范围的便捷方法
     * 
     * @param year 年份
     * @return 冬令时间范围（通常是11月到次年2月）
     */
    public static TimeRange createWinterTime(int year) {
        LocalDate startDate = LocalDate.of(year, 11, 1);  // 11月1日开始
        LocalDate endDate = LocalDate.of(year + 1, 2, 28); // 次年2月28日结束（不考虑闰年）
        return new TimeRange(startDate, endDate, "冬令时间", true);
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        TimeRange timeRange = (TimeRange) obj;
        
        if (enabled != timeRange.enabled) return false;
        if (startDate != null ? !startDate.equals(timeRange.startDate) : timeRange.startDate != null)
            return false;
        if (endDate != null ? !endDate.equals(timeRange.endDate) : timeRange.endDate != null) return false;
        return name != null ? name.equals(timeRange.name) : timeRange.name == null;
    }
    
    @Override
    public int hashCode() {
        int result = startDate != null ? startDate.hashCode() : 0;
        result = 31 * result + (endDate != null ? endDate.hashCode() : 0);
        result = 31 * result + (name != null ? name.hashCode() : 0);
        result = 31 * result + (enabled ? 1 : 0);
        return result;
    }
    
    @Override
    public String toString() {
        return "TimeRange{" +
                "startDate=" + startDate +
                ", endDate=" + endDate +
                ", name='" + name + '\'' +
                ", enabled=" + enabled +
                ", days=" + getDays() +
                '}';
    }
}
