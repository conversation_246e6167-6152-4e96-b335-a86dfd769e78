package com.inxaiot.ruleengine.trigger.time;

import com.inxaiot.ruleengine.rule.engine.RuleEngineService;
import com.inxaiot.ruleengine.rule.definition.RuleDefinition;
import com.inxaiot.ruleengine.rule.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.service.RuleStorageService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 时间调度器
 * 负责定期检查时间驱动的规则，并触发相应的激活或失活动作
 *
 * 核心功能：
 * 1. 每分钟检查所有TIME_DRIVEN类型的规则
 * 2. 根据Cron表达式推断规则为POINT或RANGE模式
 * 3. 对POINT模式规则：满足时间条件即触发
 * 4. 对RANGE模式规则：维护状态，在进入/离开时间段时触发激活/失活
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class TimeScheduler {

    private static final Logger logger = LoggerFactory.getLogger(TimeScheduler.class);
    
    /**
     * 时间触发模式枚举（内部推断使用）
     */
    private enum InferredTriggerMode {
        /**
         * 时间点模式：在特定时间点触发，无状态
         */
        POINT,
        
        /**
         * 时间段模式：在时间段开始和结束时触发，有状态
         */
        RANGE
    }
    
    /**
     * 存储RANGE模式规则的前一次时间条件状态
     * Key: ruleId, Value: 上次检查时是否满足时间条件
     */
    private final Map<String, Boolean> previousTimeConditionState = new ConcurrentHashMap<>();

    /**
     * 记录上次检查的日期，用于检测跨天情况
     */
    private volatile LocalDate lastCheckDate = LocalDate.now();
    
    @Autowired
    @Qualifier("timeTriggerScheduler")
    private ScheduledExecutorService scheduler;
    
    @Autowired
    private RuleStorageService ruleStorageService;
    
    @Autowired
    private TimeConditionEvaluator timeConditionEvaluator;
    
    @Autowired
    private RuleEngineService ruleEngineService;
    
    /**
     * 初始化时间触发器，启动定时调度
     */
    @PostConstruct
    private void initialize() {
        logger.info("Initializing TimeScheduler...");

        // 每分钟执行一次时间触发检查
        scheduler.scheduleAtFixedRate(this::checkForTimeTriggers, 1, 1, TimeUnit.MINUTES);

        logger.info("TimeScheduler initialized successfully. Checking time triggers every minute.");
    }
    
    /**
     * 销毁时清理资源
     */
    @PreDestroy
    private void shutdown() {
        logger.info("Shutting down TimeScheduler...");
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        previousTimeConditionState.clear();
        logger.info("TimeScheduler shutdown completed.");
    }
    
    /**
     * 检查所有时间驱动的规则
     * 这个方法每分钟被调度器调用一次
     */
    private void checkForTimeTriggers() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDate today = now.toLocalDate();

            logger.debug("Starting time trigger check at {}", now);

            // 检查是否跨天，如果跨天则清理状态
            checkAndHandleDateChange(today);

            // 获取所有启用的时间驱动规则
            List<RuleDefinition> timeDrivenRules = ruleStorageService.findAllEnabledRules().stream()
                    .filter(rule -> rule.getTriggerType() == RuleDefinition.TriggerType.TIME_DRIVEN)
                    .collect(Collectors.toList());

            if (timeDrivenRules.isEmpty()) {
                logger.debug("No time-driven rules found, skipping check.");
                return;
            }

            logger.debug("Found {} time-driven rules to check", timeDrivenRules.size());

            for (RuleDefinition rule : timeDrivenRules) {
                try {
                    processTimeDrivenRule(rule, now);
                } catch (Exception e) {
                    logger.error("Error processing time-driven rule {}: {}", rule.getRuleId(), e.getMessage(), e);
                }
            }

            logger.debug("Completed time trigger check");

        } catch (Exception e) {
            logger.error("Error during time trigger check: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查并处理日期变化（跨天处理）
     */
    private void checkAndHandleDateChange(LocalDate today) {
        if (!today.equals(lastCheckDate)) {
            logger.info("Date changed from {} to {}, clearing time condition states", lastCheckDate, today);

            // 清理所有时间条件状态，避免跨天影响
            int clearedCount = previousTimeConditionState.size();
            previousTimeConditionState.clear();

            // 更新检查日期
            lastCheckDate = today;

            logger.info("Cleared {} time condition states due to date change", clearedCount);
        }
    }
    
    /**
     * 处理单个时间驱动规则
     * 
     * @param rule 规则定义
     * @param now 当前时间
     */
    private void processTimeDrivenRule(RuleDefinition rule, LocalDateTime now) {
        String ruleId = rule.getRuleId();
        List<TimeCondition> timeConditions = rule.getTimeConditions();
        
        if (timeConditions == null || timeConditions.isEmpty()) {
            logger.debug("Rule {} has no time conditions, skipping", ruleId);
            return;
        }
        
        // 推断触发模式
        InferredTriggerMode mode = inferTriggerMode(timeConditions);
        
        // 评估当前时间条件是否满足
        boolean isNowActive = timeConditionEvaluator.isTimeConditionMet(timeConditions, now);
        
        logger.debug("Rule {}: mode={}, isNowActive={}", ruleId, mode, isNowActive);
        
        if (mode == InferredTriggerMode.POINT) {
            // 时间点模式：无状态，满足条件即触发
            if (isNowActive) {
                logger.info("Triggering POINT mode rule activation: {}", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            }
        } else {
            // 时间段模式：有状态，检查状态变化
            processRangeMode(ruleId, isNowActive);
        }
    }
    
    /**
     * 处理RANGE模式的规则
     * 
     * @param ruleId 规则ID
     * @param isNowActive 当前是否满足时间条件
     */
    private void processRangeMode(String ruleId, boolean isNowActive) {
        Boolean wasPreviouslyActive = previousTimeConditionState.get(ruleId);
        
        if (wasPreviouslyActive == null) {
            // 首次检查，记录当前状态
            previousTimeConditionState.put(ruleId, isNowActive);
            if (isNowActive) {
                logger.info("First check for RANGE mode rule {}: currently active, triggering activation", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            }
        } else {
            // 检查状态变化
            if (isNowActive && !wasPreviouslyActive) {
                // 从不活跃变为活跃：触发激活
                logger.info("RANGE mode rule {} became active, triggering activation", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            } else if (!isNowActive && wasPreviouslyActive) {
                // 从活跃变为不活跃：触发失活
                logger.info("RANGE mode rule {} became inactive, triggering deactivation", ruleId);
                ruleEngineService.triggerRuleDeactivation(ruleId);
            }
            
            // 更新状态
            previousTimeConditionState.put(ruleId, isNowActive);
        }
    }
    
    /**
     * 根据时间条件推断触发模式
     * 
     * @param timeConditions 时间条件列表
     * @return 推断的触发模式
     */
    private InferredTriggerMode inferTriggerMode(List<TimeCondition> timeConditions) {
        // 检查所有时间条件中的Cron表达式
        for (TimeCondition condition : timeConditions) {
            List<String> cronExpressions = condition.getTimeCronExpressions();
            if (cronExpressions != null && !cronExpressions.isEmpty()) {
                for (String cronExpr : cronExpressions) {
                    if (isRangeExpression(cronExpr)) {
                        return InferredTriggerMode.RANGE;
                    }
                }
            }
        }

        // 默认为POINT模式
        return InferredTriggerMode.POINT;
    }
    
    /**
     * 判断Cron表达式是否表示时间段
     * 
     * @param cronExpression Cron表达式
     * @return true如果是时间段表达式
     */
    private boolean isRangeExpression(String cronExpression) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            return false;
        }
        
        String[] parts = cronExpression.trim().split("\\s+");
        if (parts.length < 6) {
            return false; // 不是有效的Cron表达式
        }
        
        // 检查分钟和小时字段（索引0和1）是否包含范围操作符
        String minuteField = parts[0];
        String hourField = parts[1];
        
        // 包含'-'（范围）、'/'（步长）、'*'（通配符）的表达式通常表示时间段
        return containsRangeOperators(minuteField) || containsRangeOperators(hourField);
    }
    
    /**
     * 检查字段是否包含范围操作符
     * 
     * @param field Cron表达式字段
     * @return true如果包含范围操作符
     */
    private boolean containsRangeOperators(String field) {
        return field.contains("-") || field.contains("/") || field.equals("*");
    }
    
    /**
     * 清除指定规则的状态（用于规则删除或禁用时）
     * 
     * @param ruleId 规则ID
     */
    public void evictStateForRule(String ruleId) {
        previousTimeConditionState.remove(ruleId);
        logger.debug("Evicted state for rule: {}", ruleId);
    }
    
    /**
     * 获取当前维护的规则状态数量（用于监控）
     * 
     * @return 状态数量
     */
    public int getStateCount() {
        return previousTimeConditionState.size();
    }
}
