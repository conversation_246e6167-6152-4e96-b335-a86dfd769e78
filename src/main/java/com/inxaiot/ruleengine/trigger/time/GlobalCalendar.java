package com.inxaiot.ruleengine.trigger.time;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;

/**
 * 全局日历模型
 * 只包含真正需要的全局时间条件：节假日、夏令时间、冬令时间
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class GlobalCalendar {

    /**
     * 节假日列表
     */
    @JsonProperty("holidays")
    private List<LocalDate> holidays = new ArrayList<>();

    /**
     * 夏令时间配置
     */
    @JsonProperty("summerTime")
    private TimeRange summerTime;

    /**
     * 冬令时间配置
     */
    @JsonProperty("winterTime")
    private TimeRange winterTime;

    // 构造函数
    public GlobalCalendar() {
    }

    // Getters and Setters
    public List<LocalDate> getHolidays() {
        return holidays;
    }

    public void setHolidays(List<LocalDate> holidays) {
        this.holidays = holidays != null ? holidays : new ArrayList<>();
    }

    public TimeRange getSummerTime() {
        return summerTime;
    }

    public void setSummerTime(TimeRange summerTime) {
        this.summerTime = summerTime;
    }

    public TimeRange getWinterTime() {
        return winterTime;
    }

    public void setWinterTime(TimeRange winterTime) {
        this.winterTime = winterTime;
    }

    /**
     * 检查指定日期是否为节假日
     */
    public boolean isHoliday(LocalDate date) {
        return holidays.contains(date);
    }

    /**
     * 检查指定日期是否在夏令时间
     */
    public boolean isSummerTime(LocalDate date) {
        return summerTime != null && summerTime.contains(date);
    }

    /**
     * 检查指定日期是否在冬令时间
     */
    public boolean isWinterTime(LocalDate date) {
        return winterTime != null && winterTime.contains(date);
    }

    /**
     * 根据季节名称检查日期
     * 只支持夏令时间和冬令时间
     */
    public boolean isInSeason(LocalDate date, String season) {
        if (season == null) {
            return true; // 如果没有指定季节，则认为满足条件
        }
        switch (season.toUpperCase()) {
            case "SUMMER":
                return isSummerTime(date);
            case "WINTER":
                return isWinterTime(date);
            case "ALL":
                return true;
            default:
                return false;
        }
    }

    /**
     * 添加节假日
     */
    public void addHoliday(LocalDate date) {
        if (!holidays.contains(date)) {
            holidays.add(date);
        }
    }

    /**
     * 移除节假日
     */
    public void removeHoliday(LocalDate date) {
        holidays.remove(date);
    }

    /**
     * 设置夏令时间（便捷方法）
     */
    public void setSummerTime(LocalDate startDate, LocalDate endDate) {
        this.summerTime = new TimeRange(startDate, endDate, "夏令时间", true);
    }

    /**
     * 设置冬令时间（便捷方法）
     */
    public void setWinterTime(LocalDate startDate, LocalDate endDate) {
        this.winterTime = new TimeRange(startDate, endDate, "冬令时间", true);
    }

    /**
     * 获取配置的项目数量
     */
    public int getConfigCount() {
        int count = holidays.size();
        if (summerTime != null) count++;
        if (winterTime != null) count++;
        return count;
    }

    /**
     * 检查日历是否为空
     */
    public boolean isEmpty() {
        return holidays.isEmpty() && summerTime == null && winterTime == null;
    }



    @Override
    public String toString() {
        return "GlobalCalendar{" +
                "holidays=" + holidays.size() +
                ", summerTime=" + (summerTime != null ? summerTime.toString() : "null") +
                ", winterTime=" + (winterTime != null ? winterTime.toString() : "null") +
                ", configCount=" + getConfigCount() +
                '}';
    }
}
