package com.inxaiot.ruleengine.trigger.time;


import com.inxaiot.ruleengine.common.WorkDayUtils;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.storage.GlobalCalendarService;
import org.quartz.CronExpression;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 时间条件评估服务
 * 按需评估给定规则的时间条件是否满足，不主动轮询
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class TimeConditionEvaluator {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeConditionEvaluator.class);
    
    private final GlobalCalendarService globalCalendarService;
    private volatile GlobalCalendar globalCalendar;
    private volatile long lastRefreshTime = 0;
    private static final long REFRESH_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24小时

    @Autowired
    public TimeConditionEvaluator(GlobalCalendarService globalCalendarService) {
        this.globalCalendarService = globalCalendarService;
        refreshGlobalCalendar(); // 初始化加载
    }

    /**
     * 刷新全局日历
     */
    public void refreshGlobalCalendar() {
        try {
            this.globalCalendar = globalCalendarService.loadGlobalCalendar();
            this.lastRefreshTime = System.currentTimeMillis();
            logger.info("Global calendar refreshed: {}", globalCalendar.toString());
        } catch (Exception e) {
            logger.error("Failed to refresh global calendar", e);
            if (this.globalCalendar == null) {
                this.globalCalendar = new GlobalCalendar(); // 创建空的日历作为fallback
            }
        }
    }

    /**
     * 检查是否需要刷新全局日历
     */
    private void checkAndRefreshCalendar() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastRefreshTime > REFRESH_INTERVAL_MS) {
            refreshGlobalCalendar();
        }
    }

    /**
     * 评估时间条件是否满足
     */
    public boolean isTimeConditionMet(List<TimeCondition> conditions, LocalDateTime evaluationTime) {
        if (conditions == null || conditions.isEmpty()) {
            return true; // 没有时间条件，视为满足
        }

        checkAndRefreshCalendar(); // 检查是否需要刷新日历

        // 分别收集AND条件和OR条件
        List<TimeCondition> andConditions = new ArrayList<>();
        List<TimeCondition> orConditions = new ArrayList<>();

        for (TimeCondition condition : conditions) {
            if (!condition.isEnabled()) {
                continue; // 跳过未启用的条件
            }

            if (condition.getLogic() == TimeCondition.TimeLogic.OR) {
                orConditions.add(condition);
            } else {
                andConditions.add(condition);
            }
        }

        // 评估AND条件：所有AND条件都必须满足
        boolean andResult = evaluateAndConditions(andConditions, evaluationTime);

        // 评估OR条件：至少一个OR条件满足即可
        boolean orResult = evaluateOrConditions(orConditions, evaluationTime);

        // 最终结果：AND条件必须满足，且如果有OR条件则至少一个OR条件满足
        return andResult && orResult;
    }

    /**
     * 评估AND条件：所有条件都必须满足
     */
    private boolean evaluateAndConditions(List<TimeCondition> andConditions, LocalDateTime evaluationTime) {
        if (andConditions.isEmpty()) {
            return true; // 没有AND条件，视为满足
        }

        for (TimeCondition condition : andConditions) {
            boolean conditionMet = evaluateSingleTimeCondition(condition, evaluationTime);
            if (!conditionMet) {
                logger.debug("AND condition not met: {}", condition);
                return false; // 任意一个AND条件不满足，整体失败
            }
        }

        logger.debug("All {} AND conditions satisfied", andConditions.size());
        return true; // 所有AND条件都满足
    }

    /**
     * 评估OR条件：至少一个条件满足即可
     */
    private boolean evaluateOrConditions(List<TimeCondition> orConditions, LocalDateTime evaluationTime) {
        if (orConditions.isEmpty()) {
            return true; // 没有OR条件，视为满足
        }

        for (TimeCondition condition : orConditions) {
            boolean conditionMet = evaluateSingleTimeCondition(condition, evaluationTime);
            if (conditionMet) {
                logger.debug("OR condition satisfied: {}", condition);
                return true; // 任意一个OR条件满足，整体成功
            }
        }

        logger.debug("None of {} OR conditions satisfied", orConditions.size());
        return false; // 所有OR条件都不满足
    }

    /**
     * 评估单个时间条件
     */
    private boolean evaluateSingleTimeCondition(TimeCondition condition, LocalDateTime evaluationTime) {
        LocalDate evaluationDate = evaluationTime.toLocalDate();

        // 1. 检查强制包含的日期 (优先级最高)
        if (condition.getIncludeDates() != null && condition.getIncludeDates().contains(evaluationDate)) {
            logger.debug("Date {} is in include dates, checking time expressions", evaluationDate);
            return checkCronExpressions(condition.getTimeCronExpressions(), evaluationTime);
        }

        // 2. 检查强制排除的日期 (优先级次高)
        if (condition.getExcludeDates() != null && condition.getExcludeDates().contains(evaluationDate)) {
            logger.debug("Date {} is in exclude dates, condition not met", evaluationDate);
            return false;
        }

        // 3. 检查季节性条件
        if (condition.getSeason() != null && !condition.getSeason().trim().isEmpty()) {
            if (!globalCalendar.isInSeason(evaluationDate, condition.getSeason())) {
                logger.debug("Date {} is not in season {}", evaluationDate, condition.getSeason());
                return false;
            }
        }

        // 4. 检查是否为排除日期，假如节假日
        if (globalCalendar.isHoliday(evaluationDate)) {
            logger.debug("Date {} is a holiday, condition not met", evaluationDate);
            return false;
        }

        // 4. 检查工作日条件
        if (condition.getWorkDays() != null && !condition.getWorkDays().isEmpty()) {
            if (!isWorkDay(evaluationDate, condition.getWorkDays())) {
                logger.debug("Date {} is not a work day according to condition", evaluationDate);
                return false;
            }
        }

        // 5. 检查Cron表达式定义的时间段
        if (!checkCronExpressions(condition.getTimeCronExpressions(), evaluationTime)) {
            logger.debug("Date {} time {} does not match cron expressions", evaluationDate, evaluationTime.toLocalTime());
            return false;
        }

        return true; // 所有时间条件均满足
    }

    /**
     * 检查工作日条件
     */
    private boolean isWorkDay(LocalDate date, List<String> workDays) {
        if (workDays == null || workDays.isEmpty()) {
            return true;
        }

        // 检查是否为节假日（节假日优先级最高）
        if (globalCalendar.isHoliday(date)) {
            return false;
        }

        // 使用WorkDayUtils检查是否为工作日
        return WorkDayUtils.isWorkDay(date, workDays);
    }

    /**
     * 检查Cron表达式
     */
    private boolean checkCronExpressions(List<String> cronExpressions, LocalDateTime evaluationTime) {
        if (cronExpressions == null || cronExpressions.isEmpty()) {
            return true; // 没有指定具体时间段，则视为全天满足（如果日期已满足）
        }

        Date dateToTest = Date.from(evaluationTime.atZone(ZoneId.systemDefault()).toInstant());
        
        for (String cronStr : cronExpressions) {
            if (cronStr == null || cronStr.trim().isEmpty()) {
                continue;
            }
            try {
                CronExpression cron = new CronExpression(cronStr.trim());
                if (cron.isSatisfiedBy(dateToTest)) {
                    logger.debug("Cron expression '{}' satisfied by time {}", cronStr, evaluationTime);
                    return true; // 满足任意一个Cron表达式即可
                }
            } catch (ParseException e) {
                logger.error("Failed to parse cron expression: {}", cronStr, e);
                // 根据策略，可以选择忽略错误表达式或直接返回false
                // 这里选择忽略错误的表达式，继续检查其他表达式
            }
        }
        
        return false; // 所有Cron表达式都不满足
    }

    /**
     * 验证Cron表达式是否有效
     */
    public boolean isValidCronExpression(String cronExpression) {
        if (cronExpression == null || cronExpression.trim().isEmpty()) {
            return false;
        }
        
        try {
            new CronExpression(cronExpression.trim());
            return true;
        } catch (ParseException e) {
            logger.warn("Invalid cron expression: {}", cronExpression);
            return false;
        }
    }

    /**
     * 获取下一个满足Cron表达式的时间
     */
    public LocalDateTime getNextValidTime(String cronExpression, LocalDateTime fromTime) {
        if (!isValidCronExpression(cronExpression)) {
            return null;
        }
        
        try {
            CronExpression cron = new CronExpression(cronExpression.trim());
            Date fromDate = Date.from(fromTime.atZone(ZoneId.systemDefault()).toInstant());
            Date nextDate = cron.getNextValidTimeAfter(fromDate);
            
            if (nextDate != null) {
                return LocalDateTime.ofInstant(nextDate.toInstant(), ZoneId.systemDefault());
            }
        } catch (ParseException e) {
            logger.error("Failed to get next valid time for cron expression: {}", cronExpression, e);
        }
        
        return null;
    }

    /**
     * 检查指定时间是否满足时间条件（便捷方法）
     */
    public boolean isTimeConditionMet(TimeCondition condition, LocalDateTime evaluationTime) {
        List<TimeCondition> conditions = new java.util.ArrayList<>();
        conditions.add(condition);
        return isTimeConditionMet(conditions, evaluationTime);
    }

    /**
     * 检查当前时间是否满足时间条件
     */
    public boolean isTimeConditionMetNow(List<TimeCondition> conditions) {
        return isTimeConditionMet(conditions, LocalDateTime.now());
    }

    /**
     * 获取全局日历（只读）
     */
    public GlobalCalendar getGlobalCalendar() {
        checkAndRefreshCalendar();
        return globalCalendar;
    }
}
