package com.inxaiot.ruleengine.common.operator;

/**
 * 统一操作符常量定义
 * 按类别组织所有操作符常量，避免重复定义
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public final class Operators {
    
    /**
     * 基础比较操作符
     */
    public static final class Basic {
        public static final String EQUALS = "EQUALS";
        public static final String NOT_EQUALS = "NOT_EQUALS";
        public static final String GREATER_THAN = "GREATER_THAN";
        public static final String GREATER_THAN_OR_EQUAL = "GREATER_THAN_OR_EQUAL";
        public static final String LESS_THAN = "LESS_THAN";
        public static final String LESS_THAN_OR_EQUAL = "LESS_THAN_OR_EQUAL";
        
        private Basic() {}
    }
    
    /**
     * 字符串操作符
     */
    public static final class Text {
        public static final String CONTAINS = "CONTAINS";
        public static final String NOT_CONTAINS = "NOT_CONTAINS";
        public static final String STARTS_WITH = "STARTS_WITH";
        public static final String ENDS_WITH = "ENDS_WITH";
        
        private Text() {}
    }
    
    /**
     * 范围操作符
     */
    public static final class Range {
        public static final String BETWEEN = "BETWEEN";
        public static final String NOT_BETWEEN = "NOT_BETWEEN";
        public static final String IN = "IN";
        public static final String NOT_IN = "NOT_IN";
        
        private Range() {}
    }
    /**
     * 数据类型
     */
    public static final class DataTypes {
        public static final String STRING = "STRING";
        public static final String INTEGER = "INTEGER";
        public static final String DOUBLE = "DOUBLE";
        public static final String FLOAT = "FLOAT";
        public static final String BOOLEAN = "BOOLEAN";
        public static final String LONG = "LONG";

        private DataTypes() {}
    }
    
    /**
     * 时间相关操作符
     */
    public static final class Duration {
        public static final String STATES_KEEP_MINUTES = "STATES_KEEP_MINUTES";
        
        private Duration() {}
    }
    
    private Operators() {
        // 工具类，禁止实例化
    }
}
