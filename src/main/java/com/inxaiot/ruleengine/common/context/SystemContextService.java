package com.inxaiot.ruleengine.common.context;

import java.util.Map;

/**
 * 系统上下文服务接口
 * 提供规则执行时需要的基础技术信息和标识信息
 * 不涉及业务逻辑概念（如模式切换等）
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface SystemContextService {
    
    /**
     * 获取规则引擎ID
     * 
     * @return 引擎ID
     */
    String getEngineId();
    
    /**
     * 获取区域ID
     * 
     * @return 区域ID
     */
    String getRegionId();
    
    /**
     * 获取楼宇ID
     * 
     * @return 楼宇ID
     */
    String getBuildingId();
    
    /**
     * 获取引擎版本
     * 
     * @return 版本号
     */
    String getEngineVersion();
    
    /**
     * 获取时区
     * 
     * @return 时区
     */
    String getTimezone();
    
    /**
     * 获取区域设置
     * 
     * @return 区域设置
     */
    String getLocale();
    
    /**
     * 是否为调试模式
     * 
     * @return 调试模式标志
     */
    boolean isDebugMode();
    
    /**
     * 获取技术配置信息
     * 
     * @param key 配置键
     * @return 配置值
     */
    Object getTechnicalConfig(String key);
    
    /**
     * 设置技术配置信息
     * 
     * @param key 配置键
     * @param value 配置值
     */
    void setTechnicalConfig(String key, Object value);
    
    /**
     * 获取所有上下文信息（用于添加到Facts）
     * 
     * @return 上下文信息Map
     */
    Map<String, Object> getAllContext();
    
    /**
     * 从业务服务端更新基础配置
     * 
     * @param config 配置信息
     */
    void updateBasicConfig(Map<String, Object> config);
    
    /**
     * 重置为默认配置
     */
    void resetToDefaults();
}
