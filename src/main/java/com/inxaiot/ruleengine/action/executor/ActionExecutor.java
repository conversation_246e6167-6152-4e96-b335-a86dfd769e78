package com.inxaiot.ruleengine.action.executor;

import com.inxaiot.ruleengine.rule.definition.ActionDefinition;
import org.jeasy.rules.api.Facts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDate;
import java.util.Iterator;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 动作执行器
 * 执行规则定义的各种动作，包括设备控制、消息发送、API调用等
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class ActionExecutor {

    private static final Logger logger = LoggerFactory.getLogger(ActionExecutor.class);

    private final RestTemplate restTemplate = new RestTemplate();

    // 操作记忆缓存：防止重复执行相同操作
    // Key: ruleId_deviceId_pointId, Value: LastOperationRecord
    private final Map<String, LastOperationRecord> operationMemory = new ConcurrentHashMap<>();

    // 重复操作检查时间窗口：30分钟内相同操作视为重复
    private static final long DUPLICATE_CHECK_WINDOW_MS = 30 * 60 * 1000; // 30分钟

    // 定期清理过期记录的调度器
    private final ScheduledExecutorService cleanupScheduler = Executors.newSingleThreadScheduledExecutor(r -> {
        Thread thread = new Thread(r);
        thread.setName("ActionMemoryCleanup");
        thread.setDaemon(true);
        return thread;
    });

    @Autowired
    @Qualifier("actionExecutionExecutor")
    private ThreadPoolTaskExecutor actionExecutor;

    // 这里可以注入其他服务，如MQTT发布器、消息服务等
    // private final MqttMessagePublisher mqttPublisher;
    // private final MessageService messageService;

    /**
     * 初始化方法：启动定期清理任务
     */
    @javax.annotation.PostConstruct
    public void init() {
        // 每小时清理一次过期的操作记录
        cleanupScheduler.scheduleAtFixedRate(this::cleanupExpiredRecords, 1, 1, TimeUnit.HOURS);
        logger.info("ActionExecutor initialized with memory cleanup scheduler");
    }

    /**
     * 销毁方法：关闭清理调度器
     */
    @javax.annotation.PreDestroy
    public void destroy() {
        cleanupScheduler.shutdown();
        logger.info("ActionExecutor cleanup scheduler shutdown");
    }

    /**
     * 执行动作
     */
    public void executeAction(String targetDeviceId, ActionDefinition actionDef, Facts facts) {
        if (actionDef == null || !actionDef.isEnabled()) {
            logger.debug("Action is null or disabled, skipping execution");
            return;
        }

        logger.info("Executing action: {} for device: {}", actionDef.getActionType(), targetDeviceId);

        try {
            // 使用配置的动作执行线程池，避免阻塞规则评估线程
            actionExecutor.execute(() -> {
                executeActionInternal(targetDeviceId, actionDef, facts);
            });

        } catch (Exception e) {
            logger.error("Error submitting action execution for device {}: {}", targetDeviceId, e.getMessage(), e);
        }
    }

    /**
     * 检查是否应该跳过重复操作
     * 基于规则ID + 设备点位 + 操作内容 + 同一天的去重逻辑
     */
    private boolean shouldSkipDuplicateOperation(String ruleId, String targetDeviceId, String pointId, ActionDefinition actionDef) {
        if (ruleId == null || targetDeviceId == null) {
            return false; // 无法判断，不跳过
        }

        // 生成操作键：规则ID + 设备ID + 点位ID
        String operationKey = ruleId + "_" + targetDeviceId + "_" + (pointId != null ? pointId : "default");

        // 获取上次操作记录
        LastOperationRecord lastRecord = operationMemory.get(operationKey);
        if (lastRecord == null) {
            return false; // 没有历史记录，不跳过
        }

        long now = System.currentTimeMillis();

        // 检查是否在时间窗口内、同一操作类型、同一操作内容
        boolean withinTimeWindow = (now - lastRecord.getTimestamp()) <= DUPLICATE_CHECK_WINDOW_MS;
        boolean isSameActionType = Objects.equals(actionDef.getActionType(), lastRecord.getActionType());
        boolean isSameParams = Objects.equals(actionDef.getParams(), lastRecord.getParams());

        if (withinTimeWindow && isSameActionType && isSameParams) {
            long minutesAgo = (now - lastRecord.getTimestamp()) / (60 * 1000);
            logger.info("Skipping duplicate operation ({}min ago): rule={}, device={}, point={}, action={}",
                       minutesAgo, ruleId, targetDeviceId, pointId, actionDef.getActionType());
            return true;
        }

        return false;
    }

    /**
     * 记录操作执行
     */
    private void recordOperation(String ruleId, String targetDeviceId, String pointId, ActionDefinition actionDef) {
        if (ruleId == null || targetDeviceId == null) {
            return;
        }

        String operationKey = ruleId + "_" + targetDeviceId + "_" + (pointId != null ? pointId : "default");

        LastOperationRecord record = new LastOperationRecord();
        record.setRuleId(ruleId);
        record.setDeviceId(targetDeviceId);
        record.setPointId(pointId);
        record.setActionType(actionDef.getActionType());
        record.setParams(actionDef.getParams());
        record.setExecuteDate(LocalDate.now());
        record.setTimestamp(System.currentTimeMillis());

        operationMemory.put(operationKey, record);

        logger.debug("Recorded operation: rule={}, device={}, point={}, action={}", ruleId, targetDeviceId, pointId, actionDef.getActionType());
    }

    /**
     * 内部执行动作的方法
     */
    private void executeActionInternal(String targetDeviceId, ActionDefinition actionDef, Facts facts) {
        try {
            // 从Facts中获取规则ID和点位ID（用于去重判断）
            String ruleId = (String) facts.get("triggeredRuleId");
            String pointId = extractPointIdFromFacts(facts, actionDef);

            // 检查是否应该跳过重复操作
            if (shouldSkipDuplicateOperation(ruleId, targetDeviceId, pointId, actionDef)) {
                return; // 跳过重复操作
            }

            // 执行具体动作
            switch (actionDef.getActionType()) {
                case ActionDefinition.ActionTypes.DEVICE_CONTROL:
                    executeDeviceControl(targetDeviceId, actionDef, facts);
                    break;

                case ActionDefinition.ActionTypes.SEND_MESSAGE:
                    executeSendMessage(targetDeviceId, actionDef, facts);
                    break;

                case ActionDefinition.ActionTypes.CALL_API:
                    executeCallApi(targetDeviceId, actionDef, facts);
                    break;

                case ActionDefinition.ActionTypes.LOG_EVENT:
                    executeLogEvent(targetDeviceId, actionDef, facts);
                    break;

                default:
                    logger.warn("Unsupported action type: {} for device: {}",
                              actionDef.getActionType(), targetDeviceId);
                    return; // 不支持的动作类型，不记录
            }

            // 记录操作执行（只有成功执行才记录）
            recordOperation(ruleId, targetDeviceId, pointId, actionDef);

        } catch (Exception e) {
            logger.error("Error executing action {} for device {}: {}", actionDef.getActionType(), targetDeviceId, e.getMessage(), e);
        }
    }

    /**
     * 从Facts中提取点位ID
     */
    private String extractPointIdFromFacts(Facts facts, ActionDefinition actionDef) {
        // 优先从动作定义中获取点位ID
        String pointId = actionDef.getStringParam("pointId");
        if (pointId != null) {
            return pointId;
        }

        // 从Facts中查找可能的点位ID
        // 这里可以根据实际的Facts结构进行调整
        for (String key : facts.asMap().keySet()) {
            if (key.endsWith("_pointId") || key.equals("pointId")) {
                Object value = facts.get(key);
                if (value instanceof String) {
                    return (String) value;
                }
            }
        }

        return null; // 无法确定点位ID
    }

    /**
     * 清理过期的操作记录
     * 定期清理超过时间窗口的记录，防止内存泄漏
     */
    private void cleanupExpiredRecords() {
        try {
            long now = System.currentTimeMillis();
            int removedCount = 0;

            Iterator<Map.Entry<String, LastOperationRecord>> iterator = operationMemory.entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<String, LastOperationRecord> entry = iterator.next();
                LastOperationRecord record = entry.getValue();

                // 如果记录超过时间窗口，则删除
                if ((now - record.getTimestamp()) > DUPLICATE_CHECK_WINDOW_MS) {
                    iterator.remove();
                    removedCount++;
                }
            }

            if (removedCount > 0) {
                logger.debug("Cleaned up {} expired operation records, remaining: {}", removedCount, operationMemory.size());
            }

        } catch (Exception e) {
            logger.error("Error during operation memory cleanup: {}", e.getMessage(), e);
        }
    }

    /**
     * 执行设备控制动作
     */
    private void executeDeviceControl(String targetDeviceId, ActionDefinition actionDef, Facts facts) {
        logger.info("Executing device control: device={}, parames={}", targetDeviceId, actionDef.getParams());

        if(actionDef.getParams().isEmpty()){
            logger.warn("Device control params is empty for device: {}", targetDeviceId);
            return;
        }
        
        try {
            // 构造设备控制指令
            DeviceControlCommand command = new DeviceControlCommand();
            command.setDeviceId(targetDeviceId);
            command.setParams(actionDef.getParams());
            command.setTimestamp(System.currentTimeMillis());
            
            // 这里应该通过MQTT或HTTP发送控制指令到设备
            // mqttPublisher.publishDeviceControl(command);
            // 或者
            // httpDeviceControlService.sendCommand(command);
            
            // 模拟发送控制指令
            logger.info("Device control command sent: {}", command);
            
        } catch (Exception e) {
            logger.error("Failed to execute device control for device {}: {}", targetDeviceId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行发送消息动作
     */
    private void executeSendMessage(String targetDeviceId, ActionDefinition actionDef, Facts facts) {
        logger.info("Executing send message for device: {}", targetDeviceId);

        try {
            // 模拟发送消息
            //String messageContent = actionDef.getStringParam(ActionDefinition.ParamKeys.MESSAGE_CONTENT);
            //String messageReceiver = actionDef.getStringParam(ActionDefinition.ParamKeys.MESSAGE_RECEIVER);
            //String messageType = actionDef.getStringParam(ActionDefinition.ParamKeys.MESSAGE_TYPE);

            //if (messageContent == null || messageContent.trim().isEmpty()) {
            //     logger.warn("Message content is empty for device: {}", targetDeviceId);
            //    return;
            //}

            // 替换消息内容中的变量
            //messageContent = replaceMessageVariables(messageContent, targetDeviceId, facts);
            
            // 这里应该调用消息服务发送消息
            // messageService.sendMessage(messageReceiver, messageContent, messageType);
            
            // 模拟发送消息
            logger.info("Message sent - : TypeParams: {}, Params: {}",actionDef.getTypeParams(),actionDef.getParams());
            
        } catch (Exception e) {
            logger.error("Failed to send message for device {}: {}", targetDeviceId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行API调用动作
     */
    private void executeCallApi(String targetDeviceId, ActionDefinition actionDef, Facts facts) {
        logger.info("Executing API call for device: {}", targetDeviceId);
        
        try {
            String apiUrl = (String)actionDef.getTypeParam(ActionDefinition.TypeParamKeys.API_URL);
            String apiMethod = (String)actionDef.getTypeParam(ActionDefinition.TypeParamKeys.API_METHOD);
            Object apiBody = actionDef.getParam(ActionDefinition.TypeParamKeys.API_BODY);
            Object apiHeaders = actionDef.getParam(ActionDefinition.TypeParamKeys.API_HEADERS);
            
            if (apiUrl == null || apiUrl.trim().isEmpty()) {
                logger.warn("API URL is empty for device: {}", targetDeviceId);
                return;
            }
            
            HttpMethod method = HttpMethod.valueOf(apiMethod != null ? apiMethod.toUpperCase() : "POST");
            
            // 构造请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            if (apiHeaders instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, String> headerMap = (Map<String, String>) apiHeaders;
                headerMap.forEach(headers::set);
            }
            
            // 构造请求体
            HttpEntity<Object> requestEntity = new HttpEntity<>(apiBody, headers);
            
            // 发送HTTP请求
            ResponseEntity<String> response = restTemplate.exchange(apiUrl, method, requestEntity, String.class);
            
            logger.info("API call completed - URL: {}, Method: {}, Status: {}, Response: {}", apiUrl, method, response.getStatusCode(), response.getBody());
            
        } catch (Exception e) {
            logger.error("Failed to call API for device {}: {}", targetDeviceId, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 执行日志记录动作
     */
    private void executeLogEvent(String targetDeviceId, ActionDefinition actionDef, Facts facts) {
        try {
            String logLevel = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_LEVEL);
            String logMessage = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_MESSAGE);
            String logCategory = actionDef.getStringParam(ActionDefinition.TypeParamKeys.LOG_CATEGORY);
            
            if (logMessage == null || logMessage.trim().isEmpty()) {
                logMessage = "Rule action executed for device: " + targetDeviceId;
            }
            
            // 根据日志级别记录日志
            switch (logLevel != null ? logLevel.toUpperCase() : "INFO") {
                case "ERROR":
                    logger.error("[{}] {}", logCategory, logMessage);
                    break;
                case "WARN":
                    logger.warn("[{}] {}", logCategory, logMessage);
                    break;
                case "DEBUG":
                    logger.debug("[{}] {}", logCategory, logMessage);
                    break;
                case "INFO":
                default:
                    logger.info("[{}] {}", logCategory, logMessage);
                    break;
            }
            
        } catch (Exception e) {
            logger.error("Failed to log event for device {}: {}", targetDeviceId, e.getMessage(), e);
        }
    }

    /**
     * 设备控制指令模型
     */
    public static class DeviceControlCommand {
        private String deviceId;
        private long timestamp;
        /** 点位 和值 **/
        private Map<String, Object> params = new java.util.HashMap<>();

        // Getters and Setters
        public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }
        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

        public Map<String, Object> getParams() { return params; }
        public void setParams(Map<String, Object> params) { this.params = params; }

        public void addParam(String key, Object value) { this.params.put(key, value); }

        @Override
        public String toString() {
            return "DeviceControlCommand{" +
                    "deviceId='" + deviceId + '\'' +
                    ", timestamp=" + timestamp +
                    ", params=" + params +
                    '}';
        }
    }

    /**
     * 最后操作记录
     * 用于防止重复执行相同操作
     */
    public static class LastOperationRecord {
        private String ruleId;
        private String deviceId;
        private String pointId;
        private String actionType;
        private Map<String, Object> params;
        private LocalDate executeDate;
        private long timestamp;

        // Getters and Setters
        public String getRuleId() { return ruleId; }
        public void setRuleId(String ruleId) { this.ruleId = ruleId; }

        public String getDeviceId() { return deviceId; }
        public void setDeviceId(String deviceId) { this.deviceId = deviceId; }

        public String getPointId() { return pointId; }
        public void setPointId(String pointId) { this.pointId = pointId; }

        public String getActionType() { return actionType; }
        public void setActionType(String actionType) { this.actionType = actionType; }

        public Map<String, Object> getParams() { return params; }
        public void setParams(Map<String, Object> params) { this.params = params; }

        public LocalDate getExecuteDate() { return executeDate; }
        public void setExecuteDate(LocalDate executeDate) { this.executeDate = executeDate; }

        public long getTimestamp() { return timestamp; }
        public void setTimestamp(long timestamp) { this.timestamp = timestamp; }

        @Override
        public String toString() {
            return "LastOperationRecord{" +
                    "ruleId='" + ruleId + '\'' +
                    ", deviceId='" + deviceId + '\'' +
                    ", pointId='" + pointId + '\'' +
                    ", actionType='" + actionType + '\'' +
                    ", executeDate=" + executeDate +
                    ", timestamp=" + timestamp +
                    '}';
        }
    }
}
