package com.inxaiot.ruleengine.core.adapter;



import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.common.operator.ValueComparator;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.trigger.time.TimeConditionEvaluator;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.RuleBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 规则适配器服务
 * 将RuleDefinition转换为Easy Rules的Rule对象
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class RuleAdapterService {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleAdapterService.class);

    private final TimeConditionEvaluator timeConditionEvaluator;
    private final ActionExecutor actionExecutor;

    @Autowired
    public RuleAdapterService(TimeConditionEvaluator timeConditionEvaluator, ActionExecutor actionExecutor) {
        this.timeConditionEvaluator = timeConditionEvaluator;
        this.actionExecutor = actionExecutor;
    }

    /**
     * 将RuleDefinition适配为Easy Rules的Rule对象
     */
    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
                .name(ruleDefinition.getRuleId())
                .description(ruleDefinition.getRuleName())
                .priority(ruleDefinition.getPriority())
                .when(facts -> {
                    if (!ruleDefinition.isEnabled()) {
                        return false;
                    }
                    
                    try {
                        // 根据规则的触发类型，应用不同的条件判断逻辑
                        if (ruleDefinition.getTriggerType() == RuleDefinition.TriggerType.TIME_DRIVEN) {
                            // 对于时间驱动的规则，它的触发由TimeTriggerService发起，
                            // 这里只需检查是否存在特定的"时间触发事实"即可。
                            boolean isTimeTrigger = Boolean.TRUE.equals(facts.get("timeTriggerEvent")) && ruleDefinition.getRuleId().equals(facts.get("triggeredRuleId"));

                            if (isTimeTrigger) {
                                logger.debug("Time-driven rule {} triggered by TimeTriggerService", ruleDefinition.getRuleId());
                            }
                            return isTimeTrigger;
                        } else {
                            // 对于事件驱动的规则，它的触发由设备事件发起，
                            // 需要在这里同时检查"时间守卫条件"和"设备触发条件"。

                            // 1. 评估时间条件（作为守卫条件）
                            boolean timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
                            if (!timeMet) {
                                logger.trace("Event-driven rule {} time condition not met.", ruleDefinition.getRuleId());
                                return false;
                            }
                            logger.trace("Event-driven rule {} time condition met.", ruleDefinition.getRuleId());

                            // 2. 评估设备触发条件
                            boolean deviceConditionsMet = evaluateDeviceTriggerConditions(ruleDefinition, facts);
                            if (!deviceConditionsMet) {
                                logger.trace("Event-driven rule {} device conditions not met.", ruleDefinition.getRuleId());
                                return false;
                            }

                            logger.debug("Event-driven rule {} all conditions met.", ruleDefinition.getRuleId());
                            return true;
                        }
                        
                    } catch (Exception e) {
                        logger.error("Error evaluating conditions for rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
                        return false;
                    }
                })
                .then(facts -> {
                    try {
                        logger.info("Executing actions for rule {}.", ruleDefinition.getRuleId());

                        // 确保Facts中包含规则ID（用于动作执行去重）
                        if (!facts.asMap().containsKey("triggeredRuleId")) {
                            facts.put("triggeredRuleId", ruleDefinition.getRuleId());
                        }

                        if (ruleDefinition.getActions() != null) {
                            ruleDefinition.getActions().forEach(actionDef -> {
                                try {
                                    String targetDeviceId = actionDef.getTargetDeviceId() != null ? actionDef.getTargetDeviceId() : ruleDefinition.getTargetDeviceId();
                                    actionExecutor.executeAction(targetDeviceId, actionDef, facts);
                                } catch (Exception e) {
                                    logger.error("Error executing action for rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
                                }
                            });
                        }

                    } catch (Exception e) {
                        logger.error("Error executing actions for rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
                    }
                })
                .build();
    }

    /**
     * 评估设备触发条件
     */
    private boolean evaluateDeviceTriggerConditions(RuleDefinition ruleDefinition, Facts facts) {
        TriggerCondition triggerCondition = ruleDefinition.getTriggerCondition();
        if (triggerCondition == null || triggerCondition.isEmpty()) {
            return true; // 没有设备条件，视为满足
        }

        // 获取触发此规则评估的源头设备ID (如果存在)
        String triggeringDeviceId = facts.get("triggeringDeviceId");

        boolean overallResult;
        if (triggerCondition.getLogic() == TriggerCondition.MatchLogic.ANY) {
            overallResult = false; // 对于OR逻辑，默认false，只要有一个满足即为true
            for (DeviceCondition condition : triggerCondition.getConditions()) {
                if (!condition.isEnabled()) {
                    continue; // 跳过未启用的条件
                }
                
                // 可以添加设备过滤逻辑
                // String sourceDeviceId = condition.getSourceDeviceId() != null ? 
                //     condition.getSourceDeviceId() : ruleDefinition.getTargetDeviceId();
                // if (triggeringDeviceId != null && !triggeringDeviceId.equals(sourceDeviceId)) {
                //     continue; // 如果条件与触发设备不符，则跳过 (此逻辑需根据实际情况调整)
                // }
                
                if (evaluateSingleCondition(condition, facts)) {
                    overallResult = true;
                    break;
                }
            }
        } else { // ALL (AND) logic
            overallResult = true; // 对于AND逻辑，默认true，只要有一个不满足即为false
            for (DeviceCondition condition : triggerCondition.getConditions()) {
                if (!condition.isEnabled()) {
                    continue; // 跳过未启用的条件
                }
                
                // 同上的设备过滤逻辑
                
                if (!evaluateSingleCondition(condition, facts)) {
                    overallResult = false;
                    break;
                }
            }
        }
        return overallResult;
    }

    /**
     * 评估单个设备条件
     */
    private boolean evaluateSingleCondition(DeviceCondition condition, Facts facts) {
        try {
            // 对于 "STATES_KEEP_MINUTES"，检查设备状态是否持续指定时间
            if (Operators.Duration.STATES_KEEP_MINUTES.equals(condition.getOperator())) {
                return evaluateStateKeepMinutes(condition, facts);
            }

            // 对于其他常规传感器值比较
            // 从Facts中获取对应点位的值
            // 注意：facts中的key可能需要包含deviceId，如 "deviceId_pointId"
            // 或者facts本身就是针对单个设备的快照
            //Object factValue = facts.get(condition.getPointId());
            String factKey = condition.getSourceDeviceId() + "." + condition.getPointId();
            Object factValue = facts.get(factKey);

            if (factValue == null) {
                logger.warn("Fact for pointId '{}' not found for condition in rule.", condition.getPointId());
                return false; // 点位数据不存在，条件不满足
            }

            // 使用统一的值比较器
            return ValueComparator.compare(factValue, condition.getOperator(), condition.getValue(), condition.getUpperValue(), condition.getDataType());
            
        } catch (Exception e) {
            logger.error("Error evaluating condition for pointId '{}': {}", condition.getPointId(), e.getMessage(), e);
            return false;
        }
    }



    /**
     * 根据数据类型转换值
     */
    private Object convertValue(Object value, String dataType) {
        if (value == null || dataType == null) {
            return value;
        }
        
        switch (dataType.toUpperCase()) {
            case Operators.DataTypes.INTEGER:
                return Integer.valueOf(String.valueOf(value));
            case Operators.DataTypes.DOUBLE:
                return Double.valueOf(String.valueOf(value));
            case Operators.DataTypes.BOOLEAN:
                return Boolean.valueOf(String.valueOf(value));
            case Operators.DataTypes.STRING:
            default:
                return String.valueOf(value);
        }
    }

    /**
     * 转换为double类型
     */
    private double convertToDouble(Object value) {
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return Double.parseDouble(String.valueOf(value));
    }

    /**
     * 评估状态持续时间条件
     * 通用的状态持续时间检测，支持各种设备状态
     */
    private boolean evaluateStateKeepMinutes(DeviceCondition condition, Facts facts) {
        try {
            // 构造状态持续时间事实的键名
            String stateKeepFactKey = buildStateKeepFactKey(condition);

            // 从Facts中获取状态持续时间信息
            Object stateKeepFact = facts.get(stateKeepFactKey);

            if (stateKeepFact == null) {
                logger.debug("State keep fact '{}' not found", stateKeepFactKey);
                return false;
            }

            // 检查状态持续时间是否满足条件
            if (stateKeepFact instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> stateInfo = (Map<String, Object>) stateKeepFact;

                // 获取当前状态值
                Object currentState = stateInfo.get("currentState");
                // 获取持续时间（分钟）
                Object durationMinutes = stateInfo.get("durationMinutes");

                // 检查状态值是否匹配
                boolean stateMatches = checkStateMatches(condition, currentState);

                // 检查持续时间是否满足
                boolean durationMatches = checkDurationMatches(condition, durationMinutes);

                logger.debug("State keep evaluation: state={}, expectedState={}, stateMatches={}, duration={}, expectedDuration={}, durationMatches={}",
                    currentState, condition.getValue(), stateMatches, durationMinutes, condition.getDurationMinutes(), durationMatches);

                return stateMatches && durationMatches;
            }

            logger.warn("State keep fact '{}' is not a Map: {}", stateKeepFactKey, stateKeepFact.getClass());
            return false;

        } catch (Exception e) {
            logger.error("Error evaluating state keep minutes condition for device {} point {}: {}",
                condition.getSourceDeviceId(), condition.getPointId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 构造状态持续时间事实的键名
     */
    private String buildStateKeepFactKey(DeviceCondition condition) {
        return String.format("StateKeep_%s_%s", condition.getSourceDeviceId(), condition.getPointId());
    }

    /**
     * 检查状态值是否匹配
     */
    private boolean checkStateMatches(DeviceCondition condition, Object currentState) {
        // 使用统一的值比较器进行状态匹配（默认使用EQUALS操作符）
        return ValueComparator.compare(currentState, Operators.Basic.EQUALS, condition.getValue(), condition.getDataType());
    }

    /**
     * 检查持续时间是否满足
     */
    private boolean checkDurationMatches(DeviceCondition condition, Object durationMinutes) {
        if (durationMinutes == null) {
            return false;
        }

        try {
            double actualDuration = convertToDouble(durationMinutes);
            double expectedDuration = condition.getDurationMinutes();

            return actualDuration >= expectedDuration;
        } catch (Exception e) {
            logger.error("Error comparing duration: actual={}, expected={}", durationMinutes, condition.getDurationMinutes(), e);
            return false;
        }
    }


}
