package com.inxaiot.ruleengine.core.analyzer;

import com.inxaiot.ruleengine.device.state.DevicePointState;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.device.state.DevicePointRef;
import org.jeasy.rules.api.Facts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Map;
import java.util.Set;

/**
 * Facts构建器
 * 负责为规则评估构建包含所有相关设备状态的完整Facts对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class FactsBuilder {

    private static final Logger logger = LoggerFactory.getLogger(FactsBuilder.class);

    @Autowired
    private DependencyAnalyzer dependencyAnalyzer;

    @Autowired
    private StateManager stateManager;
    
    /**
     * 为规则构建完整的Facts对象
     * 包含规则需要的所有设备状态数据
     * 
     * @param rule 规则定义
     * @param triggerDeviceId 触发设备ID
     * @param triggerPointId 触发点位ID
     * @param triggerValue 触发值
     * @return 包含完整状态的Facts对象
     */
    public Facts buildCompleteFactsForRule(RuleDefinition rule, String triggerDeviceId, String triggerPointId, Object triggerValue) {
        Facts facts = new Facts();
        
        try {
            // 1. 添加触发信息
            addTriggerInfo(facts, triggerDeviceId, triggerPointId, triggerValue);
            
            // 2. 分析规则依赖
            Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);
            logger.debug("Rule {} requires {} device points", rule.getRuleId(), dependencies.size());
            
            // 3. 聚合所有相关设备状态（基于内存缓存）
            int addedStates = 0;
            int expiredStates = 0;
            int missingStates = 0;
            
            for (DevicePointRef ref : dependencies) {
                StateAddResult result = addDeviceStateToFacts(facts, ref);
                switch (result) {
                    case ADDED:
                        addedStates++;
                        break;
                    case EXPIRED:
                        expiredStates++;
                        break;
                    case MISSING:
                        missingStates++;
                        break;
                }
            }
            
            // 4. 添加全局上下文
            addGlobalContextToFacts(facts);
            
            // 5. 添加统计信息
            facts.put("_stats_addedStates", addedStates);
            facts.put("_stats_expiredStates", expiredStates);
            facts.put("_stats_missingStates", missingStates);
            facts.put("_stats_totalDependencies", dependencies.size());
            
            logger.debug("Facts built for rule {}: {} added, {} expired, {} missing", rule.getRuleId(), addedStates, expiredStates, missingStates);
            
        } catch (Exception e) {
            logger.error("Error building facts for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }
        
        return facts;
    }
    
    /**
     * 添加触发信息到Facts
     */
    private void addTriggerInfo(Facts facts, String triggerDeviceId, String triggerPointId, Object triggerValue) {
        facts.put("triggeringDeviceId", triggerDeviceId);
        facts.put("triggeringPointId", triggerPointId);
        facts.put("triggeringValue", triggerValue);
        facts.put("triggerTimestamp", System.currentTimeMillis());
        facts.put("triggerTime", LocalDateTime.now());
        
        // 添加触发设备的状态键
        if (triggerDeviceId != null && triggerPointId != null) {
            String triggerStateKey = triggerDeviceId + "." + triggerPointId;
            facts.put(triggerStateKey, triggerValue);
            facts.put("triggerStateKey", triggerStateKey);
        }
    }
    
    /**
     * 将设备状态添加到Facts中
     */
    private StateAddResult addDeviceStateToFacts(Facts facts, DevicePointRef ref) {
        String stateKey = ref.getStateKey();
        
        try {
            DevicePointState state = stateManager.getDevicePointState(ref.getDeviceId(), ref.getPointId());

            if (state == null) {
                // 状态不存在
                facts.put(stateKey + "_missing", true);
                if (ref.isRequired()) {
                    logger.warn("Required device state missing: {}", stateKey);
                }
                return StateAddResult.MISSING;
            }
            
            // 检查状态时效性
            Duration age = Duration.between(state.getLastUpdateTime(), LocalDateTime.now());
            if (age.compareTo(ref.getMaxAge()) > 0) {
                // 状态过期
                facts.put(stateKey + "_expired", true);
                facts.put(stateKey + "_age_minutes", age.toMinutes());
                logger.debug("Device state expired: {} (age: {} minutes, max: {} minutes)", stateKey, age.toMinutes(), ref.getMaxAge().toMinutes());
                return StateAddResult.EXPIRED;
            }
            
            // 添加有效状态数据
            facts.put(stateKey, state.getCurrentValue());
            facts.put(stateKey + "_timestamp", state.getLastUpdateTime());
            facts.put(stateKey + "_duration_minutes", state.getCurrentValueDurationMinutes());
            facts.put(stateKey + "_duration_seconds", state.getCurrentValueDurationSeconds());
            facts.put(stateKey + "_dataType", state.getDataType());
            facts.put(stateKey + "_age_minutes", age.toMinutes());
            
            // 添加前一个值（如果需要）
            if (state.getPreviousValue() != null) {
                facts.put(stateKey + "_previousValue", state.getPreviousValue());
            }
            
            logger.trace("Added device state to facts: {} = {} (age: {} minutes)", stateKey, state.getCurrentValue(), age.toMinutes());
            
            return StateAddResult.ADDED;
            
        } catch (Exception e) {
            logger.error("Error adding device state {} to facts: {}", stateKey, e.getMessage(), e);
            facts.put(stateKey + "_error", e.getMessage());
            return StateAddResult.MISSING;
        }
    }
    
    /**
     * 添加全局上下文信息到Facts
     */
    private void addGlobalContextToFacts(Facts facts) {
        try {
            LocalDateTime now = LocalDateTime.now();
            facts.put("currentTime", now);
            facts.put("currentTimestamp", System.currentTimeMillis());
            facts.put("currentHour", now.getHour());
            facts.put("currentMinute", now.getMinute());
            facts.put("currentDayOfWeek", now.getDayOfWeek());
            facts.put("currentDate", now.toLocalDate());
            
        } catch (Exception e) {
            logger.error("Error adding global context to facts: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 批量构建Facts（性能优化版本）
     * 当多个规则需要相同的设备状态时，可以批量获取以提高性能
     */
    public Facts buildFactsWithBatchOptimization(RuleDefinition rule, String triggerDeviceId, 
                                                String triggerPointId, Object triggerValue,
                                                Map<String, DevicePointState> preloadedStates) {
        Facts facts = new Facts();
        
        // 添加触发信息
        addTriggerInfo(facts, triggerDeviceId, triggerPointId, triggerValue);
        
        // 使用预加载的状态数据
        Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);
        for (DevicePointRef ref : dependencies) {
            String stateKey = ref.getStateKey();
            DevicePointState state = preloadedStates.get(stateKey);
            
            if (state != null) {
                addPreloadedStateToFacts(facts, ref, state);
            } else {
                facts.put(stateKey + "_missing", true);
            }
        }
        
        // 添加全局上下文
        addGlobalContextToFacts(facts);
        
        return facts;
    }
    
    /**
     * 添加预加载的状态到Facts
     */
    private void addPreloadedStateToFacts(Facts facts, DevicePointRef ref, DevicePointState state) {
        String stateKey = ref.getStateKey();
        
        // 检查时效性
        Duration age = Duration.between(state.getLastUpdateTime(), LocalDateTime.now());
        if (age.compareTo(ref.getMaxAge()) <= 0) {
            facts.put(stateKey, state.getCurrentValue());
            facts.put(stateKey + "_timestamp", state.getLastUpdateTime());
            facts.put(stateKey + "_duration_minutes", state.getCurrentValueDurationMinutes());
        } else {
            facts.put(stateKey + "_expired", true);
        }
    }
    
    /**
     * 状态添加结果枚举
     */
    private enum StateAddResult {
        ADDED,    // 成功添加
        EXPIRED,  // 状态过期
        MISSING   // 状态缺失
    }
}
