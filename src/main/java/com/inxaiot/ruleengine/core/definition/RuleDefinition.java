package com.inxaiot.ruleengine.core.definition;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 规则定义核心数据模型
 * 从业务服务端接收并存储在本地的原子化规则的核心数据结构
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class RuleDefinition {
    public static void main(String[] args) {
        System.out.println(TriggerType.EVENT_DRIVEN.toString());
    }

    /**
     * 规则触发类型枚举
     */
    public enum TriggerType {
        /**
         * 事件驱动：由设备事件驱动，时间作为守卫条件
         */
        EVENT_DRIVEN,

        /**
         * 时间驱动：由时间直接驱动
         */
        TIME_DRIVEN
    }
    
    /**
     * 规则触发类型，默认为事件驱动
     */
    @JsonProperty("triggerType")
    private TriggerType triggerType = TriggerType.EVENT_DRIVEN;

    /**
     * 规则唯一ID
     */
    @JsonProperty("ruleId")
    private String ruleId;
    
    /**
     * 规则名称 (可选, 便于理解)
     */
    @JsonProperty("ruleName")
    private String ruleName;
    
    /**
     * 主要目标设备ID (如果规则针对单个设备)
     */
    @JsonProperty("targetDeviceId")
    private String targetDeviceId;
    
    /**
     * 目标设备类型 (可选) 用于筛选或特定逻辑
     */
    @JsonProperty("targetDeviceType")
    private String targetDeviceType;
    
    /**
     * 规则所属外部业务唯一标识符
     */
    @JsonProperty("bizId")
    private String bizId;
    
    /**
     * 规则分组ID，方便业务批量处理规则
     */
    @JsonProperty("groupId")
    private String groupId;
    
    /**
     * 规则优先级 (Easy Rules支持)
     */
    @JsonProperty("priority")
    private int priority = 1;
    
    /**
     * 规则是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 时间条件列表
     */
    @JsonProperty("timeConditions")
    private List<TimeCondition> timeConditions;
    
    /**
     * 设备触发条件
     */
    @JsonProperty("triggerCondition")
    private TriggerCondition triggerCondition;
    
    /**
     * 动作列表（激活时执行的动作）
     */
    @JsonProperty("actions")
    private List<ActionDefinition> actions;

    /**
     * 失活时执行的动作（仅用于TIME_DRIVEN的RANGE模式）
     */
    @JsonProperty("deactivationActions")
    private List<ActionDefinition> deactivationActions;
    
    /**
     * 规则创建时间
     */
    @JsonProperty("createTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;
    
    /**
     * 规则更新时间
     */
    @JsonProperty("updateTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
    
    /**
     * 规则描述
     */
    @JsonProperty("description")
    private String description;

    // 构造函数
    public RuleDefinition() {
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    public RuleDefinition(String ruleId, String ruleName) {
        this();
        this.ruleId = ruleId;
        this.ruleName = ruleName;
    }

    // Getters and Setters
    public TriggerType getTriggerType() {
        return triggerType;
    }

    public void setTriggerType(TriggerType triggerType) {
        this.triggerType = triggerType;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getTargetDeviceId() {
        return targetDeviceId;
    }

    public void setTargetDeviceId(String targetDeviceId) {
        this.targetDeviceId = targetDeviceId;
    }

    public String getTargetDeviceType() {
        return targetDeviceType;
    }

    public void setTargetDeviceType(String targetDeviceType) {
        this.targetDeviceType = targetDeviceType;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public List<TimeCondition> getTimeConditions() {
        return timeConditions;
    }

    public void setTimeConditions(List<TimeCondition> timeConditions) {
        this.timeConditions = timeConditions;
    }

    public TriggerCondition getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(TriggerCondition triggerCondition) {
        this.triggerCondition = triggerCondition;
    }

    public List<ActionDefinition> getActions() {
        return actions;
    }

    public void setActions(List<ActionDefinition> actions) {
        this.actions = actions;
    }

    public List<ActionDefinition> getDeactivationActions() {
        return deactivationActions;
    }

    public void setDeactivationActions(List<ActionDefinition> deactivationActions) {
        this.deactivationActions = deactivationActions;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "RuleDefinition{" +
                "ruleId='" + ruleId + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", targetDeviceId='" + targetDeviceId + '\'' +
                ", bizId='" + bizId + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                '}';
    }
}
