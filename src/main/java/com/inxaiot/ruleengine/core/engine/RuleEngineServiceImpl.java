package com.inxaiot.ruleengine.core.engine;

import com.inxaiot.ruleengine.common.context.SystemContextService;
import com.inxaiot.ruleengine.core.action.ActionExecutor;
import com.inxaiot.ruleengine.core.adapter.RuleAdapterService;
import com.inxaiot.ruleengine.core.analyzer.DependencyAnalyzer;
import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.storage.RuleService;
import com.inxaiot.ruleengine.core.analyzer.FactsBuilder;
import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * 规则引擎服务实现
 * 此类负责编排规则的加载和执行
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class RuleEngineServiceImpl implements RuleEngineService {

    private static final Logger logger = LoggerFactory.getLogger(RuleEngineServiceImpl.class);

    private final DefaultRulesEngine easyRulesEngine;
    private final RuleService ruleService;
    private final RuleAdapterService ruleAdapterService;
    private final SystemContextService systemContextService;
    private final ThreadPoolTaskExecutor ruleEvaluationExecutor;
    private final ActionExecutor actionExecutor;
    private final DependencyAnalyzer dependencyAnalyzer;
    private final FactsBuilder factsBuilder;

    @Autowired
    public RuleEngineServiceImpl(RuleService ruleService,
                                 RuleAdapterService ruleAdapterService,
                                 SystemContextService systemContextService,
                                 @Qualifier("ruleEvaluationExecutor") ThreadPoolTaskExecutor ruleEvaluationExecutor,
                                 ActionExecutor actionExecutor,
                                 DependencyAnalyzer dependencyAnalyzer,
                                 FactsBuilder factsBuilder) {
        this.easyRulesEngine = new DefaultRulesEngine();
        // 配置规则引擎参数
        // this.easyRulesEngine.setSkipOnFirstAppliedRule(false); // 根据需要设置是否触发所有适用规则
        // this.easyRulesEngine.setRulePriorityThreshold(Integer.MAX_VALUE); // 根据需要设置优先级阈值

        this.ruleService = ruleService;
        this.ruleAdapterService = ruleAdapterService;
        this.systemContextService = systemContextService;
        this.ruleEvaluationExecutor = ruleEvaluationExecutor;
        this.actionExecutor = actionExecutor;
        this.dependencyAnalyzer = dependencyAnalyzer;
        this.factsBuilder = factsBuilder;

        logger.info("RuleEngineService initialized with enhanced multi-condition support");
    }

    /**
     * 监听设备状态变化事件
     * 使用事件驱动架构解耦StateManager和RuleEngineService
     * 根据事件类型分发到不同的处理方法
     */
    @EventListener
    @Async("ruleEvaluationExecutor")
    public void handleStateChangeEvent(StateChangeEvent event) {
        logger.debug("Received StateChangeEvent: {} - {}", event.getEventType(), event);

        switch (event.getEventType()) {
            case VALUE_CHANGED:
            case STATE_UPDATED:
                // 处理设备状态变化事件
                processDeviceEvent(event.getDeviceId(), event.getPointId(), event.getNewValue());
                break;

            case CONDITION_TIMEOUT:
                // 处理设备超时事件
                triggerRulesForDeviceTimeout(event.getDeviceId(), event.getPointId(),
                                           event.getDurationMinutes() != null ? event.getDurationMinutes() : 0L);
                break;

            case CONDITION_MET:
            case CONDITION_RESET:
                // 可以根据需要添加其他事件类型的处理
                logger.debug("Received condition event: {} for {}.{}",
                           event.getEventType(), event.getDeviceId(), event.getPointId());
                break;

            default:
                logger.debug("Unhandled event type: {} for {}.{}",
                           event.getEventType(), event.getDeviceId(), event.getPointId());
                break;
        }
    }

    /**
     * 处理设备事件
     * 由MQTT监听器或其他数据源调用，当有设备数据发生时
     */
    @Override
    public void processDeviceEvent(String deviceId, String pointId, Object value) {
        // 异步提交规则评估任务，立即返回，不阻塞事件源
        ruleEvaluationExecutor.execute(() -> {
            processDeviceEventInternal(deviceId, pointId, value);
        });
    }

    /**
     * 内部处理设备事件的方法（增强版：支持多条件规则）
     */
    private void processDeviceEventInternal(String deviceId, String pointId, Object value) {
        try {
            // 1. 查找与此设备相关的规则
            List<RuleDefinition> relevantRules = findRulesRelatedToDevice(deviceId, pointId);

            if (relevantRules.isEmpty()) {
                logger.trace("No relevant rules found for device event: {}.{}", deviceId, pointId);
                return;
            }

            logger.debug("Found {} relevant rules for device event: {}.{}", relevantRules.size(), deviceId, pointId);

            // 2. 为每个规则构建完整的Facts并评估
            for (RuleDefinition rule : relevantRules) {
                try {
                    // 使用增强的Facts构建器，聚合规则需要的所有设备状态
                    Facts facts = factsBuilder.buildCompleteFactsForRule(rule, deviceId, pointId, value);

                    // 添加事件类型标识
                    facts.put("eventType", RuleDefinition.TriggerType.EVENT_DRIVEN.toString());

                    // 评估单个规则
                    evaluateSingleRule(rule, facts);

                } catch (Exception e) {
                    logger.error("Error processing rule {} for device event {}.{}: {}",
                               rule.getRuleId(), deviceId, pointId, e.getMessage(), e);
                }
            }

        } catch (Exception e) {
            logger.error("Error processing device event {}.{}: {}", deviceId, pointId, e.getMessage(), e);
        }
    }

    /**
     * 查找与指定设备点位相关的规则
     */
    private List<RuleDefinition> findRulesRelatedToDevice(String deviceId, String pointId) {
        return ruleService.findAllEnabledRulesRelevantToDevice(deviceId)
                .stream()
                .filter(rule -> rule.getTriggerType() == RuleDefinition.TriggerType.EVENT_DRIVEN)
                .filter(rule -> dependencyAnalyzer.isRuleRelatedToDevice(rule, deviceId, pointId))
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 评估单个规则
     */
    private void evaluateSingleRule(RuleDefinition ruleDefinition, Facts facts) {
        try {
            Rule easyRule = ruleAdapterService.adapt(ruleDefinition);
            Rules rules = new Rules();
            rules.register(easyRule);

            logger.debug("Evaluating rule {} with {} facts", ruleDefinition.getRuleId(), facts.asMap().size());
            easyRulesEngine.fire(rules, facts);

        } catch (Exception e) {
            logger.error("Error evaluating rule {}: {}", ruleDefinition.getRuleId(), e.getMessage(), e);
        }
    }

    /**
     * 处理设备超时事件
     * 由DeviceStateManager在确认设置某一状态超时后调用（如15分钟无人）
     */
    @Override
    public void triggerRulesForDeviceTimeout(String deviceId, String pointId, long durationMinutes) {
        // 异步提交超时处理任务
        ruleEvaluationExecutor.execute(() -> {
            triggerRulesForDeviceTimeoutInternal(deviceId, pointId, durationMinutes);
        });
    }

    /**
     * 内部处理设备超时事件的方法
     */
    private void triggerRulesForDeviceTimeoutInternal(String deviceId, String pointId, long durationMinutes) {
        Facts facts = new Facts();
        facts.put("triggeringDeviceId", deviceId);
        facts.put("eventType", "TIMEOUT_EVENT");
        facts.put("timestamp", System.currentTimeMillis());

        // 约定一个事实名称，与RuleAdapterService.evaluateSingleCondition中对应
        String timeoutFactName = "UnoccupiedTimeoutFact_" + deviceId + "_" + pointId;
        facts.put(timeoutFactName, true);
        facts.put("timeoutDurationMinutes", durationMinutes); // 附加信息
        facts.put("timeoutPointId", pointId);

        addGlobalContextToFacts(facts);
        triggerRulesWithFacts(facts, deviceId); // 仍然可以基于deviceId筛选相关规则
    }

    /**
     * 触发基于时间的场景规则
     * 如果有纯粹基于时间的场景规则，由时间调度服务调用
     */
    @Override
    public void triggerTimeBasedScene(String sceneIdOrRuleGroupId) {
        // 异步提交时间场景处理任务
        ruleEvaluationExecutor.execute(() -> {
            triggerTimeBasedSceneInternal(sceneIdOrRuleGroupId);
        });
    }

    /**
     * 内部处理基于时间的场景规则的方法
     */
    private void triggerTimeBasedSceneInternal(String sceneIdOrRuleGroupId) {
        Facts facts = new Facts();
        facts.put("timeTriggeredSceneId", sceneIdOrRuleGroupId);
        facts.put("eventType", "TIME_SCENE_EVENT");
        facts.put("timestamp", System.currentTimeMillis());
        addGlobalContextToFacts(facts);

        // 加载与此场景/规则组相关的所有规则定义
        List<RuleDefinition> definitions = ruleService.findRulesByBizId(sceneIdOrRuleGroupId);
        if (definitions.isEmpty()) {
            logger.debug("No rules found for time-triggered bizId: {}", sceneIdOrRuleGroupId);
            return;
        }

        Rules easyRules = new Rules();
        definitions.stream()
            .filter(RuleDefinition::isEnabled) // 只处理启用的规则
            .map(ruleAdapterService::adapt)
            .forEach(easyRules::register);

        if (easyRules.isEmpty()) {
            logger.debug("No enabled rules adapted for time-triggered bizId: {}", sceneIdOrRuleGroupId);
            return;
        }

        logger.info("Firing rules for time-triggered bizId: {}, rule count: {}", sceneIdOrRuleGroupId, easyRules.size());
        easyRulesEngine.fire(easyRules, facts);
    }

    /**
     * 使用事实触发规则
     */
    private void triggerRulesWithFacts(Facts facts, String relevantDeviceId) {
        try {
            // 1. 从存储中加载可能与此设备相关的已启用的规则定义，只处理事件驱动的规则
            List<RuleDefinition> relevantDefinitions = ruleService.findAllEnabledRulesRelevantToDevice(relevantDeviceId)
                    .stream()
                    .filter(rule -> rule.getTriggerType() == RuleDefinition.TriggerType.EVENT_DRIVEN)
                    .collect(java.util.stream.Collectors.toList());

            if (relevantDefinitions.isEmpty()) {
                logger.trace("No relevant enabled event-driven rules found for deviceId: {} and facts: {}", relevantDeviceId, facts);
                return;
            }

            // 2. 将这些定义适配成Easy Rules对象
            Rules easyRules = new Rules();
            // adapt方法内部会处理规则的enabled状态
            relevantDefinitions.stream().map(ruleAdapterService::adapt).forEach(easyRules::register);

            if (easyRules.isEmpty()) {
                logger.trace("No event-driven rules adapted or all are disabled for deviceId: {}", relevantDeviceId);
                return;
            }

            // 3. 执行规则
            logger.debug("Firing event-driven rules for deviceId: {} with facts: {}, rule count: {}", relevantDeviceId, facts, easyRules.size());
            easyRulesEngine.fire(easyRules, facts);
            
        } catch (Exception e) {
            logger.error("Error occurred while triggering rules for device: {}", relevantDeviceId, e);
        }
    }

    /**
     * 添加全局上下文到事实中
     */
    private void addGlobalContextToFacts(Facts facts) {
        try {
            // 从SystemContextService获取所有上下文信息
            Map<String, Object> context = systemContextService.getAllContext();

            // 将上下文信息添加到Facts中
            context.forEach(facts::put);

            logger.trace("Added global context to facts: {}", context.keySet());

        } catch (Exception e) {
            logger.error("Error adding global context to facts", e);

            // 如果SystemContextService出错，添加基础信息作为fallback
            facts.put("currentTime", System.currentTimeMillis());
            facts.put("engineId", "rule-engine-fallback");
        }
    }



    /**
     * 刷新规则缓存
     * 暴露方法，供API调用以刷新规则缓存 (如果RuleStorageService有缓存机制)
     */
    @Override
    public void refreshRulesCache() {
        try {
            ruleService.reloadRules(); // 假设RuleStorageService有此方法
            logger.info("Rule cache refreshed on demand.");
        } catch (Exception e) {
            logger.error("Failed to refresh rule cache", e);
        }
    }

    /**
     * 触发规则激活（用于时间驱动规则）
     * 预留给未来的TimeTriggerService使用
     */
    @Override
    public void triggerRuleActivation(String ruleId) {
        ruleEvaluationExecutor.execute(() -> {
            triggerRuleActivationInternal(ruleId);
        });
    }

    /**
     * 内部处理规则激活的方法
     */
    private void triggerRuleActivationInternal(String ruleId) {
        try {
            RuleDefinition definition = ruleService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled()) {
                logger.debug("Rule not found or disabled: {}", ruleId);
                return;
            }

            Facts facts = new Facts();
            facts.put("timeTriggerEvent", true);
            facts.put("triggeredRuleId", ruleId);
            facts.put("eventType", "TIME_ACTIVATION_EVENT");
            facts.put("timestamp", System.currentTimeMillis());
            addGlobalContextToFacts(facts);

            Rule easyRule = ruleAdapterService.adapt(definition);
            Rules rules = new Rules();
            rules.register(easyRule);

            logger.info("Triggering rule activation for ruleId: {}", ruleId);
            easyRulesEngine.fire(rules, facts);

        } catch (Exception e) {
            logger.error("Error triggering rule activation for ruleId: {}", ruleId, e);
        }
    }

    /**
     * 触发规则失活（用于时间段规则）
     * 预留给未来的TimeTriggerService使用
     */
    @Override
    public void triggerRuleDeactivation(String ruleId) {
        ruleEvaluationExecutor.execute(() -> {
            triggerRuleDeactivationInternal(ruleId);
        });
    }

    /**
     * 内部处理规则失活的方法
     */
    private void triggerRuleDeactivationInternal(String ruleId) {
        try {
            RuleDefinition definition = ruleService.findRuleById(ruleId);
            if (definition == null || !definition.isEnabled()) {
                logger.debug("Rule not found or disabled for deactivation: {}", ruleId);
                return;
            }

            List<ActionDefinition> deactivationActions = definition.getDeactivationActions();
            if (deactivationActions == null || deactivationActions.isEmpty()) {
                logger.debug("No deactivation actions defined for rule: {}", ruleId);
                return;
            }

            Facts facts = new Facts();
            facts.put("timeDeactivationEvent", true);
            facts.put("deactivatedRuleId", ruleId);
            facts.put("triggeredRuleId", ruleId); // 用于动作执行去重
            facts.put("eventType", "TIME_DEACTIVATION_EVENT");
            facts.put("timestamp", System.currentTimeMillis());
            addGlobalContextToFacts(facts);

            // 直接执行失活动作，不经过Easy Rules条件判断
            String targetDeviceId = definition.getTargetDeviceId();
            for (ActionDefinition actionDef : deactivationActions) {
                try {
                    String actionTargetDeviceId = actionDef.getTargetDeviceId() != null ?
                        actionDef.getTargetDeviceId() : targetDeviceId;
                    actionExecutor.executeAction(actionTargetDeviceId, actionDef, facts);
                } catch (Exception e) {
                    logger.error("Error executing deactivation action for rule {}: {}", ruleId, e.getMessage(), e);
                }
            }

            logger.info("Rule deactivation completed for ruleId: {}, executed {} actions",
                       ruleId, deactivationActions.size());

        } catch (Exception e) {
            logger.error("Error triggering rule deactivation for ruleId: {}", ruleId, e);
        }
    }

    /**
     * 获取规则引擎统计信息
     */
    public RuleEngineStatistics getStatistics() {
        RuleEngineStatistics stats = new RuleEngineStatistics();
        stats.setTotalRules(ruleService.countAllRules());
        stats.setEnabledRules(ruleService.countEnabledRules());
        // 可以添加更多统计信息
        return stats;
    }

    /**
     * 规则引擎统计信息
     */
    public static class RuleEngineStatistics {
        private int totalRules;
        private int enabledRules;
        private long lastRefreshTime;

        // Getters and Setters
        public int getTotalRules() { return totalRules; }
        public void setTotalRules(int totalRules) { this.totalRules = totalRules; }

        public int getEnabledRules() { return enabledRules; }
        public void setEnabledRules(int enabledRules) { this.enabledRules = enabledRules; }

        public long getLastRefreshTime() { return lastRefreshTime; }
        public void setLastRefreshTime(long lastRefreshTime) { this.lastRefreshTime = lastRefreshTime; }

        @Override
        public String toString() {
            return "RuleEngineStatistics{" +
                    "totalRules=" + totalRules +
                    ", enabledRules=" + enabledRules +
                    ", lastRefreshTime=" + lastRefreshTime +
                    '}';
        }
    }
}
