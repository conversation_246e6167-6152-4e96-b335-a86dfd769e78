package com.inxaiot.ruleengine.device.state;


import com.inxaiot.ruleengine.common.operator.ValueComparator;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 设备点位状态模型
 * 记录设备某个点位的当前状态和历史变化
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class DevicePointState {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 点位ID
     */
    private String pointId;
    
    /**
     * 当前值
     */
    private Object currentValue;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 上次值
     */
    private Object previousValue;
    
    /**
     * 当前值开始时间
     */
    private LocalDateTime currentValueStartTime;
    
    /**
     * 上次更新时间
     */
    private LocalDateTime lastUpdateTime;
    
    /**
     * 值变化历史（最近N次变化）
     */
    private final Map<LocalDateTime, Object> valueHistory = new ConcurrentHashMap<>();
    
    /**
     * 扩展属性
     */
    private final Map<String, Object> properties = new ConcurrentHashMap<>();
    
    /**
     * 构造函数
     */
    public DevicePointState(String deviceId, String pointId) {
        this.deviceId = deviceId;
        this.pointId = pointId;
        this.lastUpdateTime = LocalDateTime.now();
    }
    
    /**
     * 更新点位值
     */
    public synchronized void updateValue(Object newValue, String dataType) {
        this.previousValue = this.currentValue;
        this.currentValue = newValue;
        this.dataType = dataType;
        
        LocalDateTime now = LocalDateTime.now();
        this.lastUpdateTime = now;
        
        // 如果值发生变化，更新开始时间
        if (!isValueEqual(previousValue, currentValue)) {
            this.currentValueStartTime = now;
            // 记录历史
            this.valueHistory.put(now, newValue);
            
            // 保持历史记录数量限制（最多保留100条）
            if (this.valueHistory.size() > 100) {
                this.valueHistory.keySet().stream().min(LocalDateTime::compareTo).ifPresent(this.valueHistory::remove);
            }
        }
    }
    
    /**
     * 获取当前值持续时间（分钟）
     */
    public long getCurrentValueDurationMinutes() {
        if (currentValueStartTime == null) {
            return 0;
        }
        return java.time.Duration.between(currentValueStartTime, LocalDateTime.now()).toMinutes();
    }
    
    /**
     * 获取当前值持续时间（秒）
     */
    public long getCurrentValueDurationSeconds() {
        if (currentValueStartTime == null) {
            return 0;
        }
        return java.time.Duration.between(currentValueStartTime, LocalDateTime.now()).getSeconds();
    }
    
    /**
     * 判断当前值是否满足指定条件
     */
    public boolean matchesCondition(String operator, Object targetValue) {
        return ValueComparator.compare(currentValue, operator, targetValue, dataType);
    }
    
    /**
     * 判断当前值是否满足指定条件并持续了指定时间
     */
    public boolean matchesConditionForDuration(String operator, Object targetValue, long durationMinutes) {
        return matchesCondition(operator, targetValue) && getCurrentValueDurationMinutes() >= durationMinutes;
    }
    
    /**
     * 判断值是否相等
     */
    private boolean isValueEqual(Object value1, Object value2) {
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            return false;
        }
        return value1.toString().equals(value2.toString());
    }
    
    /**
     * 重置日常计数器（跨天时调用）
     * 保留当前值，但重置时间相关信息
     */
    public void resetDailyCounters() {
        // 重置当前值的开始时间为当前时间
        this.currentValueStartTime = LocalDateTime.now();

        // 清理历史记录（可选，根据业务需要）
        this.valueHistory.clear();

        // 清理扩展属性中的日常统计信息
        this.properties.entrySet().removeIf(entry -> entry.getKey().startsWith("daily_") || entry.getKey().startsWith("count_"));
    }

    /**
     * 获取状态摘要
     */
    public Map<String, Object> getStateSummary() {
        Map<String, Object> summary = new ConcurrentHashMap<>();
        summary.put("deviceId", deviceId);
        summary.put("pointId", pointId);
        summary.put("currentValue", currentValue);
        summary.put("dataType", dataType);
        summary.put("previousValue", previousValue);
        summary.put("currentValueStartTime", currentValueStartTime);
        summary.put("lastUpdateTime", lastUpdateTime);
        summary.put("currentValueDurationMinutes", getCurrentValueDurationMinutes());
        summary.put("valueHistoryCount", valueHistory.size());
        return summary;
    }
    
    // Getters and Setters
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public Object getCurrentValue() {
        return currentValue;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public Object getPreviousValue() {
        return previousValue;
    }
    
    public LocalDateTime getCurrentValueStartTime() {
        return currentValueStartTime;
    }
    
    public LocalDateTime getLastUpdateTime() {
        return lastUpdateTime;
    }
    
    public Map<LocalDateTime, Object> getValueHistory() {
        return new ConcurrentHashMap<>(valueHistory);
    }
    
    public Map<String, Object> getProperties() {
        return properties;
    }
    
    public void setProperty(String key, Object value) {
        this.properties.put(key, value);
    }
    
    public Object getProperty(String key) {
        return this.properties.get(key);
    }
    
    @Override
    public String toString() {
        return "DevicePointState{" +
                "deviceId='" + deviceId + '\'' +
                ", pointId='" + pointId + '\'' +
                ", currentValue=" + currentValue +
                ", dataType='" + dataType + '\'' +
                ", currentValueDurationMinutes=" + getCurrentValueDurationMinutes() +
                '}';
    }
}
