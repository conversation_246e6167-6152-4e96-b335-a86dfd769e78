package com.inxaiot.ruleengine.device.state;

import com.inxaiot.ruleengine.device.event.StateChangeEvent;
import com.inxaiot.ruleengine.device.event.DeviceEventPublisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Set;
import java.util.List;
import java.util.ArrayList;

/**
 * 通用设备状态管理器
 * 管理设备点位的状态变化和持续时间判断
 * 支持任意条件的持续时间监控，如：
 * - 温度 > 30度 持续 10分钟
 * - 占用状态 = "UNOCCUPIED" 持续 15分钟
 * - 照度 < 100 持续 5分钟
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
public class StateManager {

    private static final Logger logger = LoggerFactory.getLogger(StateManager.class);
    
    // K: deviceId_pointId, V: 点位状态对象
    private final Map<String, DevicePointState> devicePointStates = new ConcurrentHashMap<>();
    
    // K: conditionId, V: 状态条件监控
    private final Map<String, StateConditionMonitor> stateConditionMonitors = new ConcurrentHashMap<>();

    // 记录上次检查的日期，用于检测跨天情况
    private volatile LocalDate lastCheckDate = LocalDate.now();

    private final ScheduledExecutorService scheduler;

    @Autowired
    private DeviceEventPublisher eventPublisher;

    @Autowired
    public StateManager(@Qualifier("deviceStateScheduler") ScheduledExecutorService scheduler) {
        this.scheduler = scheduler;
    }

    /**
     * 处理设备点位数据更新
     *
     * @param deviceId 设备ID
     * @param pointId 点位ID
     * @param value 点位值
     * @param dataType 数据类型
     */
    public void processDevicePointUpdate(String deviceId, String pointId, Object value, String dataType) {
        // 检查是否跨天，如果跨天则清理状态
        checkAndHandleDateChange();

        String stateKey = generateStateKey(deviceId, pointId);

        // 获取或创建设备点位状态
        DevicePointState pointState = devicePointStates.computeIfAbsent(stateKey, k -> new DevicePointState(deviceId, pointId));

        // 记录旧值
        Object oldValue = pointState.getCurrentValue();

        // 更新点位状态
        pointState.updateValue(value, dataType);

        // 创建状态变化事件
        StateChangeEvent changeEvent = StateChangeEvent.createValueChangeEvent(deviceId, pointId, oldValue, value, dataType);

        logger.debug("Device point updated: {}.{} = {} (was: {})", deviceId, pointId, value, oldValue);

        // 检查所有相关的状态条件
        checkStateConditions(deviceId, pointId, pointState, changeEvent);

        // 发布事件而不是直接调用规则引擎
        eventPublisher.publishDeviceStateChange(deviceId, pointId, value);
    }

    /**
     * 检查并处理日期变化（跨天处理）
     */
    private void checkAndHandleDateChange() {
        LocalDate today = LocalDate.now();
        if (!today.equals(lastCheckDate)) {
            logger.info("Date changed from {} to {}, clearing device states and condition monitors", lastCheckDate, today);

            // 清理设备状态（保留当前值，但重置时间相关信息）
            int deviceStateCount = devicePointStates.size();
            devicePointStates.values().forEach(DevicePointState::resetDailyCounters);

            // 取消所有状态条件监控任务
            int monitorCount = stateConditionMonitors.size();
            stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
            stateConditionMonitors.clear();

            // 更新检查日期
            lastCheckDate = today;

            logger.info("Date change handled: reset {} device states, cancelled {} condition monitors", deviceStateCount, monitorCount);
        }
    }

    /**
     * 注册状态条件监控
     * 
     * @param condition 状态条件
     */
    public void registerStateCondition(StateCondition condition) {
        if (!condition.isEnabled()) {
            logger.debug("Skipping disabled condition: {}", condition.getConditionId());
            return;
        }
        
        StateConditionMonitor monitor = new StateConditionMonitor(condition, scheduler, this);
        stateConditionMonitors.put(condition.getConditionId(), monitor);
        
        logger.info("Registered state condition: {}", condition.getConditionExpression());
        
        // 检查当前状态是否已经满足条件
        String stateKey = generateStateKey(condition.getDeviceId(), condition.getPointId());
        DevicePointState pointState = devicePointStates.get(stateKey);
        if (pointState != null) {
            monitor.checkCondition(pointState);
        }
    }

    /**
     * 注销状态条件监控
     * 
     * @param conditionId 条件ID
     */
    public void unregisterStateCondition(String conditionId) {
        StateConditionMonitor monitor = stateConditionMonitors.remove(conditionId);
        if (monitor != null) {
            monitor.cancel();
            logger.info("Unregistered state condition: {}", conditionId);
        }
    }

    /**
     * 检查状态条件
     */
    private void checkStateConditions(String deviceId, String pointId, DevicePointState pointState, StateChangeEvent changeEvent) {
        // 查找所有相关的状态条件监控
        stateConditionMonitors.values().stream().filter(monitor -> monitor.isRelevantTo(deviceId, pointId)).forEach(monitor -> monitor.checkCondition(pointState));
    }

    /**
     * 处理条件超时事件
     * 由StateConditionMonitor调用
     */
    public void handleConditionTimeout(StateCondition condition, DevicePointState pointState) {
        logger.info("Condition timeout: {} for device {}.{}", condition.getConditionExpression(), condition.getDeviceId(), condition.getPointId());
        
        // 创建条件超时事件
        StateChangeEvent timeoutEvent = StateChangeEvent.createConditionTimeoutEvent(condition.getDeviceId(), condition.getPointId(), condition.getConditionId(), condition.getDurationMinutes());

        // 发布事件而不是直接调用规则引擎
        eventPublisher.publishDeviceTimeout(condition.getDeviceId(), condition.getPointId(), condition.getDurationMinutes());
    }

    /**
     * 获取设备点位状态
     */
    public DevicePointState getDevicePointState(String deviceId, String pointId) {
        String stateKey = generateStateKey(deviceId, pointId);
        return devicePointStates.get(stateKey);
    }

    /**
     * 获取所有设备状态统计
     */
    public Map<String, Object> getStateStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();
        stats.put("totalDevicePoints", devicePointStates.size());
        stats.put("activeConditionMonitors", stateConditionMonitors.size());
        
        long enabledMonitors = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.isEnabled() ? 1 : 0)
            .sum();
        stats.put("enabledConditionMonitors", enabledMonitors);
        
        long activeTimeoutTasks = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.hasActiveTimeoutTask() ? 1 : 0)
            .sum();
        stats.put("activeTimeoutTasks", activeTimeoutTasks);
        
        return stats;
    }

    /**
     * 获取设备状态摘要
     */
    public List<Map<String, Object>> getDeviceStateSummary() {
        List<Map<String, Object>> summaries = new ArrayList<>();
        devicePointStates.values().forEach(state -> {
            summaries.add(state.getStateSummary());
        });
        return summaries;
    }

    /**
     * 清理过期状态
     */
    public void cleanupExpiredStates(long maxAgeHours) {
        long cutoffTime = System.currentTimeMillis() - (maxAgeHours * 60 * 60 * 1000);
        
        devicePointStates.entrySet().removeIf(entry -> {
            DevicePointState state = entry.getValue();
            if (state.getLastUpdateTime() != null) {
                long lastUpdateMillis = state.getLastUpdateTime().atZone(java.time.ZoneId.systemDefault()).toInstant().toEpochMilli();
                return lastUpdateMillis < cutoffTime;
            }
            return false;
        });
        
        logger.info("Cleaned up expired device states older than {} hours", maxAgeHours);
    }



    /**
     * 批量获取多个设备状态（减少Map查找次数，提高性能）
     *
     * @param refs 设备点位引用集合
     * @return 状态键到设备状态的映射
     */
    public Map<String, DevicePointState> getMultipleDeviceStates(Set<DevicePointRef> refs) {
        Map<String, DevicePointState> result = new HashMap<>();

        for (DevicePointRef ref : refs) {
            String stateKey = generateStateKey(ref.getDeviceId(), ref.getPointId());
            DevicePointState state = devicePointStates.get(stateKey);
            if (state != null) {
                result.put(stateKey, state);
            }
        }

        logger.debug("Batch retrieved {} device states from {} requests", result.size(), refs.size());
        return result;
    }

    /**
     * 获取所有设备状态的快照（用于调试和监控）
     *
     * @return 所有设备状态的副本
     */
    public Map<String, DevicePointState> getAllDeviceStatesSnapshot() {
        return new HashMap<>(devicePointStates);
    }



    /**
     * 生成状态键
     */
    private String generateStateKey(String deviceId, String pointId) {
        return deviceId + "_" + pointId;
    }

    /**
     * 清除所有状态条件监控
     * 用于重新加载规则配置时清理现有条件
     */
    public void clearAllStateConditions() {
        try {
            int cancelledCount = stateConditionMonitors.size();

            // 取消所有状态条件监控任务
            stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
            stateConditionMonitors.clear();

            logger.info("Cleared {} state condition monitors", cancelledCount);

        } catch (Exception e) {
            logger.error("Error clearing state conditions: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取所有状态条件的统计信息
     *
     * @return 状态条件统计信息
     */
    public Map<String, Object> getStateConditionStatistics() {
        Map<String, Object> stats = new HashMap<>();

        stats.put("totalConditions", stateConditionMonitors.size());

        long enabledConditions = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.isEnabled() ? 1 : 0)
            .sum();
        stats.put("enabledConditions", enabledConditions);

        long activeTimeoutTasks = stateConditionMonitors.values().stream()
            .mapToLong(monitor -> monitor.hasActiveTimeoutTask() ? 1 : 0)
            .sum();
        stats.put("activeTimeoutTasks", activeTimeoutTasks);

        // 按规则ID分组统计
        Map<String, Long> conditionsByRule = new HashMap<>();
        stateConditionMonitors.values().forEach(monitor -> {
            String ruleId = monitor.condition.getRuleId();
            if (ruleId != null) {
                conditionsByRule.put(ruleId, conditionsByRule.getOrDefault(ruleId, 0L) + 1);
            }
        });
        stats.put("conditionsByRule", conditionsByRule);

        return stats;
    }

    /**
     * 获取指定规则的状态条件列表
     *
     * @param ruleId 规则ID
     * @return 状态条件列表
     */
    public List<StateCondition> getStateConditionsByRule(String ruleId) {
        if (ruleId == null) {
            return new ArrayList<>();
        }

        return stateConditionMonitors.values().stream()
            .map(monitor -> monitor.condition)
            .filter(condition -> ruleId.equals(condition.getRuleId()))
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 检查指定的状态条件是否已注册
     *
     * @param conditionId 条件ID
     * @return 是否已注册
     */
    public boolean isStateConditionRegistered(String conditionId) {
        return stateConditionMonitors.containsKey(conditionId);
    }

    /**
     * 关闭时清理资源
     */
    @PreDestroy
    public void shutdown() {
        logger.info("Shutting down StateManager...");

        // 取消所有状态条件监控
        stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
        stateConditionMonitors.clear();

        // 清理设备状态
        devicePointStates.clear();

        logger.info("StateManager shutdown completed");
    }

    /**
     * 状态条件监控内部类
     */
    private static class StateConditionMonitor {
        final StateCondition condition; // 改为包级别访问，供外部类访问
        private final ScheduledExecutorService scheduler;
        private final StateManager stateManager;
        private ScheduledFuture<?> timeoutTask;
        private boolean conditionCurrentlyMet = false;
        
        public StateConditionMonitor(StateCondition condition, ScheduledExecutorService scheduler, StateManager stateManager) {
            this.condition = condition;
            this.scheduler = scheduler;
            this.stateManager = stateManager;
        }
        
        public void checkCondition(DevicePointState pointState) {
            boolean conditionMet = condition.matches(pointState.getCurrentValue(), pointState.getDataType());
            
            if (conditionMet && !conditionCurrentlyMet) {
                // 条件开始满足，启动超时任务
                startTimeoutTask(pointState);
                conditionCurrentlyMet = true;
            } else if (!conditionMet && conditionCurrentlyMet) {
                // 条件不再满足，取消超时任务
                cancelTimeoutTask();
                conditionCurrentlyMet = false;
            }
        }
        
        private void startTimeoutTask(DevicePointState pointState) {
            cancelTimeoutTask(); // 先取消之前的任务
            
            timeoutTask = scheduler.schedule(() -> {
                // 再次检查条件是否仍然满足
                if (condition.matches(pointState.getCurrentValue(), pointState.getDataType()) &&
                    pointState.getCurrentValueDurationMinutes() >= condition.getDurationMinutes()) {
                    stateManager.handleConditionTimeout(condition, pointState);
                }
            }, condition.getDurationMinutes(), TimeUnit.MINUTES);
        }
        
        private void cancelTimeoutTask() {
            if (timeoutTask != null && !timeoutTask.isDone()) {
                timeoutTask.cancel(false);
            }
            timeoutTask = null;
        }
        
        public void cancel() {
            cancelTimeoutTask();
        }
        
        public boolean isRelevantTo(String deviceId, String pointId) {
            return condition.getDeviceId().equals(deviceId) && condition.getPointId().equals(pointId);
        }
        
        public boolean isEnabled() {
            return condition.isEnabled();
        }
        
        public boolean hasActiveTimeoutTask() {
            return timeoutTask != null && !timeoutTask.isDone();
        }
    }
}
