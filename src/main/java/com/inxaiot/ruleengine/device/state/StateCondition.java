package com.inxaiot.ruleengine.device.state;


import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.common.operator.ValueComparator;

/**
 * 状态条件模型
 * 定义需要监控的状态条件，如：
 * - 温度 > 30度 持续 10分钟
 * - 占用状态 = "UNOCCUPIED" 持续 15分钟
 * - 照度 < 100 持续 5分钟
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class StateCondition {
    
    /**
     * 条件ID（唯一标识）
     */
    private String conditionId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 点位ID
     */
    private String pointId;
    
    /**
     * 操作符
     * EQUALS, NOT_EQUALS, GREATER_THAN, GREATER_THAN_OR_EQUAL, 
     * LESS_THAN, LESS_THAN_OR_EQUAL, BETWEEN, IN, NOT_IN, etc.
     */
    private String operator;
    
    /**
     * 目标值（用于比较）
     */
    private Object value;
    
    /**
     * 第二个目标值（用于BETWEEN操作符）
     */
    private Object value2;
    
    /**
     * 持续时间（分钟）
     */
    private long durationMinutes;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 条件描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private boolean enabled = true;
    
    /**
     * 关联的规则ID（可选）
     */
    private String ruleId;
    
    /**
     * 优先级
     */
    private int priority = 5;
    
    /**
     * 构造函数
     */
    public StateCondition() {
    }
    
    /**
     * 构造函数
     */
    public StateCondition(String conditionId, String deviceId, String pointId, 
                         String operator, Object targetValue, long durationMinutes) {
        this.conditionId = conditionId;
        this.deviceId = deviceId;
        this.pointId = pointId;
        this.operator = operator;
        this.value = targetValue;
        this.durationMinutes = durationMinutes;
    }
    
    /**
     * 构造函数（用于BETWEEN操作符）
     */
    public StateCondition(String conditionId, String deviceId, String pointId, 
                         String operator, Object targetValue, Object targetValue2, long durationMinutes) {
        this(conditionId, deviceId, pointId, operator, targetValue, durationMinutes);
        this.value2 = targetValue2;
    }
    
    /**
     * 生成条件ID
     */
    public static String generateConditionId(String deviceId, String pointId, String operator, Object targetValue, long durationMinutes) {
        return String.format("%s_%s_%s_%s_%d", deviceId, pointId, operator, String.valueOf(targetValue), durationMinutes);
    }
    
    /**
     * 检查条件是否匹配指定的设备点位值
     */
    public boolean matches(Object currentValue, String currentDataType) {
        return ValueComparator.compare(currentValue, operator, value, value2, currentDataType != null ? currentDataType : dataType);
    }
    
    /**
     * 获取条件的字符串表示
     */
    public String getConditionExpression() {
        StringBuilder sb = new StringBuilder();
        sb.append(deviceId).append(".").append(pointId);
        sb.append(" ").append(operator).append(" ");
        if (Operators.Range.BETWEEN.equalsIgnoreCase(operator) && value2 != null) {
            sb.append(value).append(" AND ").append(value2);
        } else {
            sb.append(value);
        }
        
        sb.append(" FOR ").append(durationMinutes).append(" MINUTES");
        
        return sb.toString();
    }
    
    // Getters and Setters
    
    public String getConditionId() {
        return conditionId;
    }
    
    public void setConditionId(String conditionId) {
        this.conditionId = conditionId;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public String getOperator() {
        return operator;
    }
    
    public void setOperator(String operator) {
        this.operator = operator;
    }
    
    public Object getValue() {
        return value;
    }
    
    public void setValue(Object value) {
        this.value = value;
    }
    
    public Object getValue2() {
        return value2;
    }
    
    public void setValue2(Object value2) {
        this.value2 = value2;
    }
    
    public long getDurationMinutes() {
        return durationMinutes;
    }
    
    public void setDurationMinutes(long durationMinutes) {
        this.durationMinutes = durationMinutes;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public String getRuleId() {
        return ruleId;
    }
    
    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }
    
    public int getPriority() {
        return priority;
    }
    
    public void setPriority(int priority) {
        this.priority = priority;
    }
    
    @Override
    public String toString() {
        return "StateCondition{" +
                "conditionId='" + conditionId + '\'' +
                ", expression='" + getConditionExpression() + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
