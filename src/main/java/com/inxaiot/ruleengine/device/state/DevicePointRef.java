package com.inxaiot.ruleengine.device.state;

import java.time.Duration;
import java.util.Objects;

/**
 * 设备点位引用
 * 用于表示规则中引用的设备点位及其时效性要求
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class DevicePointRef {
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 点位ID
     */
    private String pointId;
    
    /**
     * 状态最大有效期，超过此时间的状态被认为过期
     * 默认5分钟
     */
    private Duration maxAge = Duration.ofMinutes(5);
    
    /**
     * 是否必需的点位（如果缺失是否影响规则评估）
     */
    private boolean required = true;
    
    public DevicePointRef() {
    }
    
    public DevicePointRef(String deviceId, String pointId) {
        this.deviceId = deviceId;
        this.pointId = pointId;
    }
    
    public DevicePointRef(String deviceId, String pointId, Duration maxAge) {
        this.deviceId = deviceId;
        this.pointId = pointId;
        this.maxAge = maxAge;
    }
    
    public DevicePointRef(String deviceId, String pointId, Duration maxAge, boolean required) {
        this.deviceId = deviceId;
        this.pointId = pointId;
        this.maxAge = maxAge;
        this.required = required;
    }
    
    /**
     * 生成状态键
     */
    public String getStateKey() {
        return deviceId + "." + pointId;
    }
    
    /**
     * 创建设备点位引用的便捷方法
     */
    public static DevicePointRef of(String deviceId, String pointId) {
        return new DevicePointRef(deviceId, pointId);
    }
    
    public static DevicePointRef of(String deviceId, String pointId, Duration maxAge) {
        return new DevicePointRef(deviceId, pointId, maxAge);
    }
    
    public static DevicePointRef optional(String deviceId, String pointId) {
        return new DevicePointRef(deviceId, pointId, Duration.ofMinutes(5), false);
    }
    
    // Getters and Setters
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public Duration getMaxAge() {
        return maxAge;
    }
    
    public void setMaxAge(Duration maxAge) {
        this.maxAge = maxAge;
    }
    
    public boolean isRequired() {
        return required;
    }
    
    public void setRequired(boolean required) {
        this.required = required;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        DevicePointRef that = (DevicePointRef) o;
        return Objects.equals(deviceId, that.deviceId) && Objects.equals(pointId, that.pointId);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(deviceId, pointId);
    }
    
    @Override
    public String toString() {
        return "DevicePointRef{" +
                "deviceId='" + deviceId + '\'' +
                ", pointId='" + pointId + '\'' +
                ", maxAge=" + maxAge +
                ", required=" + required +
                '}';
    }
}
