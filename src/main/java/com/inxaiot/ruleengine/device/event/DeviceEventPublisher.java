package com.inxaiot.ruleengine.device.event;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

/**
 * 设备事件发布器
 * 用于解耦StateManager和RuleEngineService之间的循环依赖
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Component
public class DeviceEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布设备状态变化事件
     * 
     * @param deviceId 设备ID
     * @param pointId 点位ID
     * @param value 新值
     */
    public void publishDeviceStateChange(String deviceId, String pointId, Object value) {
        StateChangeEvent event = new StateChangeEvent();
        event.setDeviceId(deviceId);
        event.setPointId(pointId);
        event.setEventType(StateChangeEvent.EventType.VALUE_CHANGED);
        event.setNewValue(value);
        eventPublisher.publishEvent(event);
    }
    
    /**
     * 发布设备超时事件
     * 
     * @param deviceId 设备ID
     * @param pointId 点位ID
     * @param durationMinutes 持续时间（分钟）
     */
    public void publishDeviceTimeout(String deviceId, String pointId, long durationMinutes) {
        StateChangeEvent event = new StateChangeEvent();
        event.setDeviceId(deviceId);
        event.setPointId(pointId);
        event.setEventType(StateChangeEvent.EventType.CONDITION_TIMEOUT);
        event.setDurationMinutes(durationMinutes);
        eventPublisher.publishEvent(event);
    }
}
