package com.inxaiot.ruleengine.device.event;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.Optional;

/**
 * 状态变化事件模型
 * 记录设备点位状态的变化事件
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class StateChangeEvent {
    
    /**
     * 事件ID
     */
    private String eventId;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 点位ID
     */
    private String pointId;
    
    /**
     * 事件类型
     */
    private EventType eventType;
    
    /**
     * 旧值
     */
    private Object oldValue;
    
    /**
     * 新值
     */
    private Object newValue;
    
    /**
     * 数据类型
     */
    private String dataType;
    
    /**
     * 事件时间
     */
    private LocalDateTime eventTime;
    
    /**
     * 条件ID（如果是条件满足事件）
     */
    private String conditionId;
    
    /**
     * 持续时间（分钟）
     */
    private Long durationMinutes;
    
    /**
     * 事件描述
     */
    private String description;
    
    /**
     * 扩展属性
     */
    private Map<String, Object> properties;
    
    /**
     * 事件类型枚举
     */
    public enum EventType {
        VALUE_CHANGED,           // 值变化
        CONDITION_MET,           // 条件满足
        CONDITION_TIMEOUT,       // 条件超时
        CONDITION_RESET,         // 条件重置
        STATE_INITIALIZED,       // 状态初始化
        STATE_UPDATED           // 状态更新
    }
    
    /**
     * 构造函数
     */
    public StateChangeEvent() {
        this.eventTime = LocalDateTime.now();
        this.eventId = generateEventId();
        this.properties = new HashMap<>();
    }
    
    /**
     * 构造函数
     */
    public StateChangeEvent(String deviceId, String pointId, EventType eventType) {
        this();
        this.deviceId = deviceId;
        this.pointId = pointId;
        this.eventType = eventType;
    }
    
    /**
     * 构造函数（值变化事件）
     */
    public StateChangeEvent(String deviceId, String pointId, Object oldValue, Object newValue, String dataType) {
        this(deviceId, pointId, EventType.VALUE_CHANGED);
        this.oldValue = oldValue;
        this.newValue = newValue;
        this.dataType = dataType;
    }
    
    /**
     * 构造函数（条件事件）
     */
    public StateChangeEvent(String deviceId, String pointId, EventType eventType, 
                           String conditionId, Long durationMinutes) {
        this(deviceId, pointId, eventType);
        this.conditionId = conditionId;
        this.durationMinutes = durationMinutes;
    }
    
    /**
     * 生成事件ID
     */
    private String generateEventId() {
        return "EVENT_" + System.currentTimeMillis() + "_" + (int)(Math.random() * 1000);
    }
    
    /**
     * 创建值变化事件
     */
    public static StateChangeEvent createValueChangeEvent(String deviceId, String pointId, Object oldValue, Object newValue, String dataType) {
        return new StateChangeEvent(deviceId, pointId, oldValue, newValue, dataType);
    }
    
    /**
     * 创建条件满足事件
     */
    public static StateChangeEvent createConditionMetEvent(String deviceId, String pointId, 
                                                          String conditionId, Long durationMinutes) {
        return new StateChangeEvent(deviceId, pointId, EventType.CONDITION_MET, conditionId, durationMinutes);
    }
    
    /**
     * 创建条件超时事件
     */
    public static StateChangeEvent createConditionTimeoutEvent(String deviceId, String pointId, 
                                                              String conditionId, Long durationMinutes) {
        return new StateChangeEvent(deviceId, pointId, EventType.CONDITION_TIMEOUT, conditionId, durationMinutes);
    }
    
    /**
     * 创建条件重置事件
     */
    public static StateChangeEvent createConditionResetEvent(String deviceId, String pointId, String conditionId) {
        return new StateChangeEvent(deviceId, pointId, Optional.of(EventType.CONDITION_RESET), conditionId, null);
    }
    
    /**
     * 添加属性
     */
    public void addProperty(String key, Object value) {
        if (this.properties == null) {
            this.properties = new HashMap<>();
        }
        this.properties.put(key, value);
    }
    
    /**
     * 获取属性
     */
    public Object getProperty(String key) {
        return this.properties != null ? this.properties.get(key) : null;
    }
    
    /**
     * 判断是否为值变化事件
     */
    public boolean isValueChangeEvent() {
        return EventType.VALUE_CHANGED.equals(this.eventType);
    }
    
    /**
     * 判断是否为条件事件
     */
    public boolean isConditionEvent() {
        return EventType.CONDITION_MET.equals(this.eventType) ||
               EventType.CONDITION_TIMEOUT.equals(this.eventType) ||
               EventType.CONDITION_RESET.equals(this.eventType);
    }
    
    // Getters and Setters
    
    public String getEventId() {
        return eventId;
    }
    
    public void setEventId(String eventId) {
        this.eventId = eventId;
    }
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public EventType getEventType() {
        return eventType;
    }
    
    public void setEventType(EventType eventType) {
        this.eventType = eventType;
    }
    
    public Object getOldValue() {
        return oldValue;
    }
    
    public void setOldValue(Object oldValue) {
        this.oldValue = oldValue;
    }
    
    public Object getNewValue() {
        return newValue;
    }
    
    public void setNewValue(Object newValue) {
        this.newValue = newValue;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public LocalDateTime getEventTime() {
        return eventTime;
    }
    
    public void setEventTime(LocalDateTime eventTime) {
        this.eventTime = eventTime;
    }
    
    public String getConditionId() {
        return conditionId;
    }
    
    public void setConditionId(String conditionId) {
        this.conditionId = conditionId;
    }
    
    public Long getDurationMinutes() {
        return durationMinutes;
    }
    
    public void setDurationMinutes(Long durationMinutes) {
        this.durationMinutes = durationMinutes;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Map<String, Object> getProperties() {
        return properties;
    }
    
    public void setProperties(Map<String, Object> properties) {
        this.properties = properties;
    }
    
    @Override
    public String toString() {
        return "StateChangeEvent{" +
                "eventId='" + eventId + '\'' +
                ", deviceId='" + deviceId + '\'' +
                ", pointId='" + pointId + '\'' +
                ", eventType=" + eventType +
                ", eventTime=" + eventTime +
                ", conditionId='" + conditionId + '\'' +
                ", durationMinutes=" + durationMinutes +
                '}';
    }
}
