package com.inxaiot.ruleengine.transport.mqtt;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 设备数据消息模型
 * 用于MQTT消息的序列化和反序列化
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class DeviceDataMessage {
    
    /**
     * 设备ID
     */
    @JsonProperty("deviceId")
    private String deviceId;
    
    /**
     * 点位ID
     */
    @JsonProperty("pointId")
    private String pointId;
    
    /**
     * 点位值
     */
    @JsonProperty("value")
    private Object value;
    
    /**
     * 数据类型
     */
    @JsonProperty("dataType")
    private String dataType;
    
    /**
     * 时间戳
     */
    @JsonProperty("timestamp")
    private Long timestamp;
    
    /**
     * 持续时间（分钟）
     * 用于占用传感器等需要持续时间判断的场景
     */
    @JsonProperty("durationMinutes")
    private Long durationMinutes;
    
    /**
     * 消息ID
     */
    @JsonProperty("messageId")
    private String messageId;
    
    /**
     * 消息来源
     */
    @JsonProperty("source")
    private String source;
    
    /**
     * 质量标识
     * GOOD, BAD, UNCERTAIN
     */
    @JsonProperty("quality")
    private String quality;
    
    /**
     * 扩展属性
     */
    @JsonProperty("properties")
    private java.util.Map<String, Object> properties;
    
    /**
     * 默认构造函数
     */
    public DeviceDataMessage() {
        this.timestamp = System.currentTimeMillis();
        this.quality = "GOOD";
    }
    
    /**
     * 构造函数
     */
    public DeviceDataMessage(String deviceId, String pointId, Object value) {
        this();
        this.deviceId = deviceId;
        this.pointId = pointId;
        this.value = value;
    }
    
    /**
     * 构造函数
     */
    public DeviceDataMessage(String deviceId, String pointId, Object value, String dataType) {
        this(deviceId, pointId, value);
        this.dataType = dataType;
    }
    
    // Getters and Setters
    
    public String getDeviceId() {
        return deviceId;
    }
    
    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }
    
    public String getPointId() {
        return pointId;
    }
    
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    public Object getValue() {
        return value;
    }
    
    public void setValue(Object value) {
        this.value = value;
    }
    
    public String getDataType() {
        return dataType;
    }
    
    public void setDataType(String dataType) {
        this.dataType = dataType;
    }
    
    public Long getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Long timestamp) {
        this.timestamp = timestamp;
    }
    
    public Long getDurationMinutes() {
        return durationMinutes;
    }
    
    public void setDurationMinutes(Long durationMinutes) {
        this.durationMinutes = durationMinutes;
    }
    
    public String getMessageId() {
        return messageId;
    }
    
    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }
    
    public String getSource() {
        return source;
    }
    
    public void setSource(String source) {
        this.source = source;
    }
    
    public String getQuality() {
        return quality;
    }
    
    public void setQuality(String quality) {
        this.quality = quality;
    }
    
    public java.util.Map<String, Object> getProperties() {
        return properties;
    }
    
    public void setProperties(java.util.Map<String, Object> properties) {
        this.properties = properties;
    }
    
    /**
     * 添加扩展属性
     */
    public void addProperty(String key, Object value) {
        if (this.properties == null) {
            this.properties = new java.util.HashMap<>();
        }
        this.properties.put(key, value);
    }
    
    /**
     * 获取扩展属性
     */
    public Object getProperty(String key) {
        return this.properties != null ? this.properties.get(key) : null;
    }
    
    /**
     * 判断数据质量是否良好
     */
    public boolean isGoodQuality() {
        return "GOOD".equalsIgnoreCase(this.quality);
    }
    
    /**
     * 获取类型化的值
     */
    @SuppressWarnings("unchecked")
    public <T> T getTypedValue(Class<T> type) {
        if (value == null) {
            return null;
        }
        
        if (type.isInstance(value)) {
            return (T) value;
        }
        
        // 简单的类型转换
        if (type == String.class) {
            return (T) String.valueOf(value);
        } else if (type == Boolean.class || type == boolean.class) {
            if (value instanceof Boolean) {
                return (T) value;
            } else {
                return (T) Boolean.valueOf(String.valueOf(value));
            }
        } else if (type == Integer.class || type == int.class) {
            if (value instanceof Number) {
                return (T) Integer.valueOf(((Number) value).intValue());
            } else {
                return (T) Integer.valueOf(String.valueOf(value));
            }
        } else if (type == Long.class || type == long.class) {
            if (value instanceof Number) {
                return (T) Long.valueOf(((Number) value).longValue());
            } else {
                return (T) Long.valueOf(String.valueOf(value));
            }
        } else if (type == Double.class || type == double.class) {
            if (value instanceof Number) {
                return (T) Double.valueOf(((Number) value).doubleValue());
            } else {
                return (T) Double.valueOf(String.valueOf(value));
            }
        }
        
        return null;
    }
    
    @Override
    public String toString() {
        return "DeviceDataMessage{" +
                "deviceId='" + deviceId + '\'' +
                ", pointId='" + pointId + '\'' +
                ", value=" + value +
                ", dataType='" + dataType + '\'' +
                ", timestamp=" + timestamp +
                ", durationMinutes=" + durationMinutes +
                ", quality='" + quality + '\'' +
                '}';
    }
}
