package com.inxaiot.ruleengine.transport.mqtt;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.inxaiot.ruleengine.device.state.StateManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * MQTT监听器
 * 负责接收设备数据并进行通用化处理
 * 不再针对特定设备类型，而是通用处理所有设备点位数据
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Component
public class MqttListener {

    private static final Logger logger = LoggerFactory.getLogger(MqttListener.class);

    private final StateManager stateManager;
    private final ObjectMapper objectMapper;

    @Autowired
    public MqttListener(StateManager stateManager, ObjectMapper objectMapper) {
        this.stateManager = stateManager;
        this.objectMapper = objectMapper;
    }
    
    /**
     * 处理MQTT消息
     * 
     * @param topic MQTT主题
     * @param payload 消息内容
     */
    public void handleMessage(String topic, String payload) {
        logger.debug("Received MQTT message. Topic: {}, Payload: {}", topic, payload);
        
        try {
            // 解析主题，提取设备ID和点位ID
            DeviceTopicInfo topicInfo = parseTopicInfo(topic);
            if (topicInfo == null) {
                logger.warn("Failed to parse topic: {}", topic);
                return;
            }
            
            // 解析消息内容
            DeviceDataMessage dataMessage = parsePayload(payload);
            if (dataMessage == null) {
                logger.warn("Failed to parse payload: {}", payload);
                return;
            }
            
            // 使用主题中的设备ID和点位ID，如果消息中没有的话
            String deviceId = dataMessage.getDeviceId() != null ? dataMessage.getDeviceId() : topicInfo.getDeviceId();
            String pointId = dataMessage.getPointId() != null ? dataMessage.getPointId() : topicInfo.getPointId();
            Object value = dataMessage.getValue();
            String dataType = dataMessage.getDataType();
            
            if (deviceId == null || pointId == null || value == null) {
                logger.warn("Missing required fields. DeviceId: {}, PointId: {}, Value: {}", deviceId, pointId, value);
                return;
            }
            
            // 推断数据类型（如果未提供）
            if (dataType == null) {
                dataType = inferDataType(value);
            }
            
            // 通用处理：更新设备状态管理器
            stateManager.processDevicePointUpdate(deviceId, pointId, value, dataType);
            
            // 记录处理结果
            logger.debug("Processed device data: {}.{} = {} ({})", deviceId, pointId, value, dataType);
            
        } catch (Exception e) {
            logger.error("Error processing MQTT message. Topic: {}, Payload: {}", topic, payload, e);
        }
    }
    
    /**
     * 解析MQTT主题信息
     * 支持的主题格式：
     * - /device/{deviceId}/{pointId}
     * - /data/{deviceId}/{pointId}
     * - /{deviceId}/{pointId}
     * - /sensor/{deviceId}/{pointId}
     * - /iot/{deviceId}/{pointId}
     */
    private DeviceTopicInfo parseTopicInfo(String topic) {
        if (topic == null || topic.trim().isEmpty()) {
            return null;
        }
        
        String[] parts = topic.split("/");
        if (parts.length < 3) {
            return null;
        }
        
        try {
            // 根据不同的主题格式解析
            if (parts.length >= 4 && isValidTopicPrefix(parts[1])) {
                // 格式: /prefix/{deviceId}/{pointId}
                return new DeviceTopicInfo(parts[2], parts[3]);
            } else if (parts.length >= 3) {
                // 格式: /{deviceId}/{pointId}
                return new DeviceTopicInfo(parts[1], parts[2]);
            }
        } catch (Exception e) {
            logger.error("Error parsing topic: {}", topic, e);
        }
        
        return null;
    }
    
    /**
     * 检查是否为有效的主题前缀
     */
    private boolean isValidTopicPrefix(String prefix) {
        return "device".equals(prefix) || 
               "data".equals(prefix) || 
               "sensor".equals(prefix) || 
               "iot".equals(prefix) ||
               "telemetry".equals(prefix);
    }
    
    /**
     * 解析消息载荷
     */
    private DeviceDataMessage parsePayload(String payload) {
        if (payload == null || payload.trim().isEmpty()) {
            return null;
        }
        
        try {
            // 尝试解析为JSON格式
            if (payload.trim().startsWith("{")) {
                return objectMapper.readValue(payload, DeviceDataMessage.class);
            } else {
                // 简单值格式，创建基本的消息对象
                DeviceDataMessage message = new DeviceDataMessage();
                message.setValue(parseSimpleValue(payload.trim()));
                message.setTimestamp(System.currentTimeMillis());
                return message;
            }
        } catch (Exception e) {
            logger.error("Error parsing payload: {}", payload, e);
            return null;
        }
    }
    
    /**
     * 解析简单值
     */
    private Object parseSimpleValue(String value) {
        // 尝试解析为不同类型
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }
        
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Long.parseLong(value);
            }
        } catch (NumberFormatException e) {
            // 返回原始字符串
            return value;
        }
    }
    
    /**
     * 推断数据类型
     */
    private String inferDataType(Object value) {
        if (value == null) {
            return "STRING";
        }
        
        if (value instanceof Boolean) {
            return "BOOLEAN";
        } else if (value instanceof Integer || value instanceof Long) {
            return "INTEGER";
        } else if (value instanceof Double || value instanceof Float) {
            return "DOUBLE";
        } else if (value instanceof String) {
            String strValue = (String) value;
            // 尝试推断字符串的实际类型
            if ("true".equalsIgnoreCase(strValue) || "false".equalsIgnoreCase(strValue)) {
                return "BOOLEAN";
            }
            try {
                if (strValue.contains(".")) {
                    Double.parseDouble(strValue);
                    return "DOUBLE";
                } else {
                    Long.parseLong(strValue);
                    return "INTEGER";
                }
            } catch (NumberFormatException e) {
                return "STRING";
            }
        } else if (value instanceof Map || value.toString().startsWith("{")) {
            return "JSON";
        } else {
            return "STRING";
        }
    }
    
    /**
     * 批量处理消息（用于批量数据上报）
     */
    public void handleBatchMessages(String topic, String payload) {
        try {
            // 尝试解析为批量消息格式
            if (payload.trim().startsWith("[")) {
                DeviceDataMessage[] messages = objectMapper.readValue(payload, DeviceDataMessage[].class);
                if (messages != null) {
                    for (DeviceDataMessage message : messages) {
                        if (message.getDeviceId() != null && message.getPointId() != null && message.getValue() != null) {
                            stateManager.processDevicePointUpdate(
                                message.getDeviceId(),
                                message.getPointId(),
                                message.getValue(),
                                message.getDataType() != null ? message.getDataType() : inferDataType(message.getValue())
                            );
                        }
                    }
                    logger.debug("Processed batch of {} messages from topic: {}", messages.length, topic);
                }
            } else {
                // 单条消息，使用普通处理方式
                handleMessage(topic, payload);
            }
        } catch (Exception e) {
            logger.error("Error processing batch messages. Topic: {}, Payload: {}", topic, payload, e);
        }
    }
    
    /**
     * 主题信息内部类
     */
    private static class DeviceTopicInfo {
        private final String deviceId;
        private final String pointId;
        
        public DeviceTopicInfo(String deviceId, String pointId) {
            this.deviceId = deviceId;
            this.pointId = pointId;
        }
        
        public String getDeviceId() {
            return deviceId;
        }
        
        public String getPointId() {
            return pointId;
        }
        
        @Override
        public String toString() {
            return "DeviceTopicInfo{" +
                    "deviceId='" + deviceId + '\'' +
                    ", pointId='" + pointId + '\'' +
                    '}';
        }
    }
}
