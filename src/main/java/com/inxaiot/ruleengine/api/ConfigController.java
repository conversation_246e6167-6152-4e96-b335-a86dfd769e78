package com.inxaiot.ruleengine.api;


import com.inxaiot.ruleengine.storage.GlobalConfigStorageService;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 配置管理API控制器
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@RestController
@RequestMapping("/api/engine/config")
public class ConfigController {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigController.class);
    
    private final GlobalConfigStorageService globalConfigStorageService;
    
    @Autowired
    public ConfigController(GlobalConfigStorageService globalConfigStorageService) {
        this.globalConfigStorageService = globalConfigStorageService;
    }
    
    /**
     * 获取全局日历
     */
    @GetMapping("/calendar")
    public ResponseEntity<GlobalCalendar> getGlobalCalendar() {
        try {
            GlobalCalendar calendar = globalConfigStorageService.loadGlobalCalendar();
            return ResponseEntity.ok(calendar);
        } catch (Exception e) {
            logger.error("Error getting global calendar", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 更新全局日历
     */
    @PutMapping("/calendar")
    public ResponseEntity<String> updateGlobalCalendar(@RequestBody GlobalCalendar calendar) {
        try {
            globalConfigStorageService.saveGlobalCalendar(calendar);
            return ResponseEntity.ok("Global calendar updated successfully");
        } catch (Exception e) {
            logger.error("Error updating global calendar", e);
            return ResponseEntity.status(500).body("Error updating global calendar: " + e.getMessage());
        }
    }
    
    /**
     * 添加日历条目
     */
    @PostMapping("/calendar/entry")
    public ResponseEntity<String> addCalendarEntry(@RequestParam LocalDate date,
                                                  @RequestParam String type,
                                                  @RequestParam(required = false) String description) {
        try {
            globalConfigStorageService.addCalendarEntry(date, type, description);
            return ResponseEntity.ok("Calendar entry added successfully");
        } catch (Exception e) {
            logger.error("Error adding calendar entry", e);
            return ResponseEntity.status(500).body("Error adding calendar entry: " + e.getMessage());
        }
    }
    
    /**
     * 删除日历条目
     */
    @DeleteMapping("/calendar/entry")
    public ResponseEntity<String> removeCalendarEntry(@RequestParam LocalDate date,
                                                     @RequestParam(required = false) String type) {
        try {
            if (type != null) {
                globalConfigStorageService.removeCalendarEntry(date, type);
            } else {
                globalConfigStorageService.removeCalendarEntry(date);
            }
            return ResponseEntity.ok("Calendar entry removed successfully");
        } catch (Exception e) {
            logger.error("Error removing calendar entry", e);
            return ResponseEntity.status(500).body("Error removing calendar entry: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定日期的日历条目
     */
    @GetMapping("/calendar/entry")
    public ResponseEntity<List<Map<String, Object>>> getCalendarEntries(@RequestParam LocalDate date) {
        try {
            List<Map<String, Object>> entries = globalConfigStorageService.getCalendarEntries(date);
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            logger.error("Error getting calendar entries for date: {}", date, e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 获取指定日期范围的日历条目
     */
    @GetMapping("/calendar/entries")
    public ResponseEntity<List<Map<String, Object>>> getCalendarEntriesInRange(
            @RequestParam LocalDate startDate,
            @RequestParam LocalDate endDate,
            @RequestParam(required = false) String type) {
        try {
            List<Map<String, Object>> entries;
            if (type != null) {
                entries = globalConfigStorageService.getCalendarEntriesInRange(startDate, endDate, type);
            } else {
                entries = globalConfigStorageService.getCalendarEntriesInRange(startDate, endDate);
            }
            return ResponseEntity.ok(entries);
        } catch (Exception e) {
            logger.error("Error getting calendar entries in range: {} to {}", startDate, endDate, e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 获取所有日历类型
     */
    @GetMapping("/calendar/types")
    public ResponseEntity<List<String>> getCalendarTypes() {
        try {
            List<String> types = globalConfigStorageService.getAllCalendarTypes();
            return ResponseEntity.ok(types);
        } catch (Exception e) {
            logger.error("Error getting calendar types", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 批量导入日历数据
     */
    @PostMapping("/calendar/import")
    public ResponseEntity<String> importCalendarData(@RequestBody List<Map<String, Object>> calendarData) {
        try {
            globalConfigStorageService.batchImportCalendarData(calendarData);
            return ResponseEntity.ok("Successfully imported " + calendarData.size() + " calendar entries");
        } catch (Exception e) {
            logger.error("Error importing calendar data", e);
            return ResponseEntity.status(500).body("Error importing calendar data: " + e.getMessage());
        }
    }
    
    /**
     * 导出日历数据
     */
    @GetMapping("/calendar/export")
    public ResponseEntity<List<Map<String, Object>>> exportCalendarData(
            @RequestParam(required = false) LocalDate startDate,
            @RequestParam(required = false) LocalDate endDate,
            @RequestParam(required = false) String type) {
        try {
            List<Map<String, Object>> data = globalConfigStorageService.exportCalendarData(startDate, endDate, type);
            return ResponseEntity.ok(data);
        } catch (Exception e) {
            logger.error("Error exporting calendar data", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 刷新全局日历缓存
     */
    @PostMapping("/calendar/refresh")
    public ResponseEntity<String> refreshCalendarCache() {
        try {
            globalConfigStorageService.refreshCalendarCache();
            return ResponseEntity.ok("Calendar cache refreshed successfully");
        } catch (Exception e) {
            logger.error("Error refreshing calendar cache", e);
            return ResponseEntity.status(500).body("Error refreshing calendar cache: " + e.getMessage());
        }
    }
    
    /**
     * 获取系统配置
     */
    @GetMapping("/system")
    public ResponseEntity<Map<String, Object>> getSystemConfig() {
        try {
            Map<String, Object> config = globalConfigStorageService.getSystemConfig();
            return ResponseEntity.ok(config);
        } catch (Exception e) {
            logger.error("Error getting system config", e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 更新系统配置
     */
    @PutMapping("/system")
    public ResponseEntity<String> updateSystemConfig(@RequestBody Map<String, Object> config) {
        try {
            globalConfigStorageService.updateSystemConfig(config);
            return ResponseEntity.ok("System config updated successfully");
        } catch (Exception e) {
            logger.error("Error updating system config", e);
            return ResponseEntity.status(500).body("Error updating system config: " + e.getMessage());
        }
    }
    
    /**
     * 获取指定配置项
     */
    @GetMapping("/system/{key}")
    public ResponseEntity<Object> getSystemConfigValue(@PathVariable String key) {
        try {
            Object value = globalConfigStorageService.getSystemConfigValue(key);
            if (value != null) {
                return ResponseEntity.ok(value);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("Error getting system config value for key: {}", key, e);
            return ResponseEntity.status(500).build();
        }
    }
    
    /**
     * 设置指定配置项
     */
    @PutMapping("/system/{key}")
    public ResponseEntity<String> setSystemConfigValue(@PathVariable String key, 
                                                      @RequestBody Object value) {
        try {
            globalConfigStorageService.setSystemConfigValue(key, value);
            return ResponseEntity.ok("System config value updated successfully");
        } catch (Exception e) {
            logger.error("Error setting system config value for key: {}", key, e);
            return ResponseEntity.status(500).body("Error setting system config value: " + e.getMessage());
        }
    }
}
