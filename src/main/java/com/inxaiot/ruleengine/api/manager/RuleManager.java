package com.inxaiot.ruleengine.api.manager;

import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.device.state.StateManager;
import com.inxaiot.ruleengine.device.state.StateCondition;
import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.storage.RuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;
import java.util.List;

@Component
public class RuleManager {

    private static final Logger logger = LoggerFactory.getLogger(RuleManager.class);

    @Autowired
    private RuleService ruleService;
    @Autowired
    private StateManager stateManager;

    @PostConstruct
    public void initializeStateConditions() {
        try {
            // 从所有启用的规则中提取状态条件并注册
            List<RuleDefinition> enabledRules = ruleService.findAllEnabledRules();
            logger.info("Initializing state conditions for {} enabled rules", enabledRules.size());

            int registeredConditions = 0;
            for (RuleDefinition rule : enabledRules) {
                int conditionsForRule = registerStateConditionsForRule(rule);
                registeredConditions += conditionsForRule;
            }

            logger.info("Successfully registered {} state conditions from {} rules",
                       registeredConditions, enabledRules.size());

        } catch (Exception e) {
            logger.error("Error initializing state conditions: {}", e.getMessage(), e);
        }
    }

    /**
     * 从规则定义中提取并注册状态条件
     *
     * @param rule 规则定义
     * @return 注册的状态条件数量
     */
    private int registerStateConditionsForRule(RuleDefinition rule) {
        if (rule == null || !rule.isEnabled()) {
            logger.debug("Skipping null or disabled rule: {}", rule != null ? rule.getRuleId() : "null");
            return 0;
        }

        int registeredCount = 0;

        try {
            // 检查规则是否有触发条件
            TriggerCondition triggerCondition = rule.getTriggerCondition();
            if (triggerCondition == null || !triggerCondition.isEnabled() || triggerCondition.isEmpty()) {
                logger.debug("Rule {} has no enabled trigger conditions", rule.getRuleId());
                return 0;
            }

            // 遍历所有设备条件，查找持续时间条件
            List<DeviceCondition> deviceConditions = triggerCondition.getConditions();
            if (deviceConditions != null) {
                for (DeviceCondition deviceCondition : deviceConditions) {
                    if (isDurationCondition(deviceCondition)) {
                        StateCondition stateCondition = convertToStateCondition(rule, deviceCondition);
                        if (stateCondition != null) {
                            stateManager.registerStateCondition(stateCondition);
                            registeredCount++;
                            logger.debug("Registered state condition: {} for rule {}", stateCondition.getConditionExpression(), rule.getRuleId());
                        }
                    }
                }
            }

            if (registeredCount > 0) {
                logger.info("Registered {} state conditions for rule {}", registeredCount, rule.getRuleId());
            }

        } catch (Exception e) {
            logger.error("Error registering state conditions for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }

        return registeredCount;
    }

    /**
     * 判断设备条件是否为持续时间条件
     *
     * @param deviceCondition 设备条件
     * @return 是否为持续时间条件
     */
    private boolean isDurationCondition(DeviceCondition deviceCondition) {
        if (deviceCondition == null || !deviceCondition.isEnabled()) {
            return false;
        }

        String operator = deviceCondition.getOperator();
        if (operator == null) {
            return false;
        }

        // 检查是否为持续时间操作符
        return Operators.Duration.STATES_KEEP_MINUTES.equals(operator.toUpperCase().trim());
    }

    /**
     * 将设备条件转换为状态条件
     *
     * @param rule 规则定义
     * @param deviceCondition 设备条件
     * @return 状态条件对象
     */
    private StateCondition convertToStateCondition(RuleDefinition rule, DeviceCondition deviceCondition) {
        try {
            // 确定设备ID：优先使用sourceDeviceId，否则使用规则的targetDeviceId
            String deviceId = deviceCondition.getSourceDeviceId() != null ? deviceCondition.getSourceDeviceId() : rule.getTargetDeviceId();

            if (deviceId == null || deviceCondition.getPointId() == null) {
                logger.warn("Missing deviceId or pointId in condition for rule {}", rule.getRuleId());
                return null;
            }

            // 生成条件ID
            String conditionId = StateCondition.generateConditionId(
                deviceId,
                deviceCondition.getPointId(),
                deviceCondition.getOperator(),
                deviceCondition.getValue(),
                deviceCondition.getDurationMinutes()
            );

            // 创建状态条件对象
            StateCondition stateCondition = new StateCondition(
                conditionId,
                deviceId,
                deviceCondition.getPointId(),
                deviceCondition.getOperator(),
                deviceCondition.getValue(),
                deviceCondition.getDurationMinutes()
            );

            // 设置其他属性
            stateCondition.setDataType(deviceCondition.getDataType());
            stateCondition.setRuleId(rule.getRuleId());
            stateCondition.setEnabled(deviceCondition.isEnabled());
            stateCondition.setDescription(String.format("Auto-generated from rule %s: %s", rule.getRuleId(), deviceCondition.getDescription()));

            // 处理BETWEEN操作符的第二个值
            if (deviceCondition.getUpperValue() != null) {
                stateCondition.setTargetValue2(deviceCondition.getUpperValue());
            }

            return stateCondition;

        } catch (Exception e) {
            logger.error("Error converting device condition to state condition for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * 为单个规则注册状态条件（用于动态添加规则时调用）
     *
     * @param rule 规则定义
     */
    public void registerStateConditionsForNewRule(RuleDefinition rule) {
        int registeredCount = registerStateConditionsForRule(rule);
        if (registeredCount > 0) {
            logger.info("Registered {} state conditions for new rule {}", registeredCount, rule.getRuleId());
        }
    }

    /**
     * 注销规则的状态条件（用于删除或禁用规则时调用）
     *
     * @param rule 规则定义
     */
    public void unregisterStateConditionsForRule(RuleDefinition rule) {
        if (rule == null) {
            return;
        }

        try {
            TriggerCondition triggerCondition = rule.getTriggerCondition();
            if (triggerCondition == null || triggerCondition.isEmpty()) {
                return;
            }

            int unregisteredCount = 0;
            List<DeviceCondition> deviceConditions = triggerCondition.getConditions();
            if (deviceConditions != null) {
                for (DeviceCondition deviceCondition : deviceConditions) {
                    if (isDurationCondition(deviceCondition)) {
                        String deviceId = deviceCondition.getSourceDeviceId() != null ? deviceCondition.getSourceDeviceId() : rule.getTargetDeviceId();

                        if (deviceId != null && deviceCondition.getPointId() != null) {
                            String conditionId = StateCondition.generateConditionId(
                                deviceId,
                                deviceCondition.getPointId(),
                                deviceCondition.getOperator(),
                                deviceCondition.getValue(),
                                deviceCondition.getDurationMinutes()
                            );

                            stateManager.unregisterStateCondition(conditionId);
                            unregisteredCount++;
                        }
                    }
                }
            }

            if (unregisteredCount > 0) {
                logger.info("Unregistered {} state conditions for rule {}", unregisteredCount, rule.getRuleId());
            }

        } catch (Exception e) {
            logger.error("Error unregistering state conditions for rule {}: {}",
                       rule.getRuleId(), e.getMessage(), e);
        }
    }

    /**
     * 重新加载所有状态条件（用于规则配置变更后的刷新）
     */
    public void reloadAllStateConditions() {
        try {
            logger.info("Reloading all state conditions...");

            // 清除现有的状态条件监控
            stateManager.clearAllStateConditions();

            // 重新初始化
            initializeStateConditions();

        } catch (Exception e) {
            logger.error("Error reloading state conditions: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取状态条件统计信息
     *
     * @return 状态条件统计信息
     */
    public java.util.Map<String, Object> getStateConditionStatistics() {
        return stateManager.getStateConditionStatistics();
    }

    /**
     * 获取指定规则的状态条件
     *
     * @param ruleId 规则ID
     * @return 状态条件列表
     */
    public List<com.inxaiot.ruleengine.device.state.StateCondition> getStateConditionsByRule(String ruleId) {
        return stateManager.getStateConditionsByRule(ruleId);
    }

    /**
     * 检查指定的状态条件是否已注册
     *
     * @param conditionId 条件ID
     * @return 是否已注册
     */
    public boolean isStateConditionRegistered(String conditionId) {
        return stateManager.isStateConditionRegistered(conditionId);
    }

}
