package com.inxaiot.ruleengine.storage;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

import com.inxaiot.ruleengine.core.definition.ActionDefinition;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TimeCondition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity;
import com.inxaiot.ruleengine.storage.mapper.RuleDefinitionMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 规则存储服务
 * 提供规则定义的存储、查询、更新等功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
@Transactional
public class RuleStorageService {
    
    private static final Logger logger = LoggerFactory.getLogger(RuleStorageService.class);
    
    private final RuleDefinitionMapper ruleDefinitionMapper;
    private final ObjectMapper objectMapper;

    @Autowired
    public RuleStorageService(RuleDefinitionMapper ruleDefinitionMapper, ObjectMapper objectMapper) {
        this.ruleDefinitionMapper = ruleDefinitionMapper;
        this.objectMapper = objectMapper;
    }

    /**
     * 保存规则定义
     */
    public void saveRule(RuleDefinition ruleDefinition) {
        try {
            RuleDefinitionEntity entity = convertToEntity(ruleDefinition);
            
            if (ruleDefinitionMapper.existsRuleByRuleId(ruleDefinition.getRuleId())) {
                entity.setUpdateTime(LocalDateTime.now());
                int updated = ruleDefinitionMapper.updateRuleByRuleId(entity);
                logger.info("Updated rule: {}, affected rows: {}", ruleDefinition.getRuleId(), updated);
            } else {
                int inserted = ruleDefinitionMapper.insertRule(entity);
                logger.info("Inserted rule: {}, affected rows: {}", ruleDefinition.getRuleId(), inserted);
            }
        } catch (Exception e) {
            logger.error("Failed to save rule: {}", ruleDefinition.getRuleId(), e);
            throw new RuntimeException("Failed to save rule: " + ruleDefinition.getRuleId(), e);
        }
    }

    /**
     * 根据规则ID查询规则定义
     */
    public RuleDefinition findRuleById(String ruleId) {
        try {
            RuleDefinitionEntity entity = ruleDefinitionMapper.findRuleByRuleId(ruleId);
            return entity != null ? convertToRuleDefinition(entity) : null;
        } catch (Exception e) {
            logger.error("Failed to find rule by id: {}", ruleId, e);
            throw new RuntimeException("Failed to find rule by id: " + ruleId, e);
        }
    }

    /**
     * 查询所有规则定义
     */
    public List<RuleDefinition> findAllRules() {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findAllRules();
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find all rules", e);
            throw new RuntimeException("Failed to find all rules", e);
        }
    }

    /**
     * 查询所有启用的规则定义
     */
    public List<RuleDefinition> findAllEnabledRules() {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findAllEnabledRules();
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find all enabled rules", e);
            throw new RuntimeException("Failed to find all enabled rules", e);
        }
    }

    /**
     * 根据目标设备ID查询启用的规则定义
     */
    public List<RuleDefinition> findEnabledRulesByTargetDeviceId(String targetDeviceId) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findEnabledRulesByTargetDeviceId(targetDeviceId);
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find enabled rules by target device id: {}", targetDeviceId, e);
            throw new RuntimeException("Failed to find enabled rules by target device id: " + targetDeviceId, e);
        }
    }

    /**
     * 查询与指定设备相关的所有启用规则
     */
    public List<RuleDefinition> findAllEnabledRulesRelevantToDevice(String deviceId) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findAllEnabledRulesRelevantToDevice(deviceId);
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find enabled rules relevant to device: {}", deviceId, e);
            throw new RuntimeException("Failed to find enabled rules relevant to device: " + deviceId, e);
        }
    }

    /**
     * 根据业务ID查询规则定义
     */
    public List<RuleDefinition> findRulesByBizId(String bizId) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findRulesByBizId(bizId);
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find rules by biz id: {}", bizId, e);
            throw new RuntimeException("Failed to find rules by biz id: " + bizId, e);
        }
    }

    /**
     * 根据分组ID查询规则定义
     */
    public List<RuleDefinition> findRulesByGroupId(String groupId) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitionMapper.findRulesByGroupId(groupId);
            return entities.stream()
                    .map(this::convertToRuleDefinition)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to find rules by group id: {}", groupId, e);
            throw new RuntimeException("Failed to find rules by group id: " + groupId, e);
        }
    }

    /**
     * 删除规则定义
     */
    public void deleteRuleById(String ruleId) {
        try {
            int deleted = ruleDefinitionMapper.deleteRuleByRuleId(ruleId);
            logger.info("Deleted rule: {}, affected rows: {}", ruleId, deleted);
        } catch (Exception e) {
            logger.error("Failed to delete rule: {}", ruleId, e);
            throw new RuntimeException("Failed to delete rule: " + ruleId, e);
        }
    }

    /**
     * 批量保存规则定义
     */
    public void batchSaveRules(List<RuleDefinition> ruleDefinitions) {
        try {
            List<RuleDefinitionEntity> entities = ruleDefinitions.stream()
                    .map(this::convertToEntity)
                    .collect(Collectors.toList());
            
            int inserted = ruleDefinitionMapper.batchInsertRules(entities);
            logger.info("Batch inserted rules, count: {}, affected rows: {}", entities.size(), inserted);
        } catch (Exception e) {
            logger.error("Failed to batch save rules", e);
            throw new RuntimeException("Failed to batch save rules", e);
        }
    }

    /**
     * 批量更新规则启用状态
     */
    public void batchUpdateRuleEnabled(List<String> ruleIds, boolean enabled) {
        try {
            int updated = ruleDefinitionMapper.batchUpdateRuleEnabled(ruleIds, enabled);
            logger.info("Batch updated rule enabled status, count: {}, enabled: {}, affected rows: {}", 
                       ruleIds.size(), enabled, updated);
        } catch (Exception e) {
            logger.error("Failed to batch update rule enabled status", e);
            throw new RuntimeException("Failed to batch update rule enabled status", e);
        }
    }

    /**
     * 批量删除规则定义
     */
    public void batchDeleteRules(List<String> ruleIds) {
        try {
            int deleted = ruleDefinitionMapper.batchDeleteRulesByRuleIds(ruleIds);
            logger.info("Batch deleted rules, count: {}, affected rows: {}", ruleIds.size(), deleted);
        } catch (Exception e) {
            logger.error("Failed to batch delete rules", e);
            throw new RuntimeException("Failed to batch delete rules", e);
        }
    }

    /**
     * 统计规则数量
     */
    public int countAllRules() {
        return ruleDefinitionMapper.countAllRules();
    }

    /**
     * 统计启用的规则数量
     */
    public int countEnabledRules() {
        return ruleDefinitionMapper.countEnabledRules();
    }

    /**
     * 检查规则是否存在
     */
    public boolean existsRule(String ruleId) {
        return ruleDefinitionMapper.existsRuleByRuleId(ruleId);
    }

    /**
     * 重新加载规则缓存（预留接口）
     */
    public void reloadRules() {
        logger.info("Rule cache reloaded");
        // 这里可以实现规则缓存的重新加载逻辑
    }

    /**
     * 将RuleDefinition转换为RuleDefinitionEntity
     */
    private RuleDefinitionEntity convertToEntity(RuleDefinition ruleDefinition) {
        try {
            RuleDefinitionEntity entity = new RuleDefinitionEntity();
            entity.setRuleId(ruleDefinition.getRuleId());
            entity.setRuleName(ruleDefinition.getRuleName());
            entity.setTargetDeviceId(ruleDefinition.getTargetDeviceId());
            entity.setTargetDeviceType(ruleDefinition.getTargetDeviceType());
            entity.setBizId(ruleDefinition.getBizId());
            entity.setGroupId(ruleDefinition.getGroupId());
            entity.setPriority(ruleDefinition.getPriority());
            entity.setEnabled(ruleDefinition.isEnabled());
            entity.setDescription(ruleDefinition.getDescription());
            entity.setCreateTime(ruleDefinition.getCreateTime());
            entity.setUpdateTime(ruleDefinition.getUpdateTime());
            
            // 将复杂对象序列化为JSON
            if (ruleDefinition.getTimeConditions() != null) {
                entity.setTimeConditions(objectMapper.writeValueAsString(ruleDefinition.getTimeConditions()));
            }
            if (ruleDefinition.getTriggerCondition() != null) {
                entity.setTriggerCondition(objectMapper.writeValueAsString(ruleDefinition.getTriggerCondition()));
            }
            if (ruleDefinition.getActions() != null) {
                entity.setActions(objectMapper.writeValueAsString(ruleDefinition.getActions()));
            }
            
            return entity;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert RuleDefinition to Entity", e);
        }
    }

    /**
     * 将RuleDefinitionEntity转换为RuleDefinition
     */
    private RuleDefinition convertToRuleDefinition(RuleDefinitionEntity entity) {
        try {
            RuleDefinition ruleDefinition = new RuleDefinition();
            ruleDefinition.setRuleId(entity.getRuleId());
            ruleDefinition.setRuleName(entity.getRuleName());
            ruleDefinition.setTargetDeviceId(entity.getTargetDeviceId());
            ruleDefinition.setTargetDeviceType(entity.getTargetDeviceType());
            ruleDefinition.setBizId(entity.getBizId());
            ruleDefinition.setGroupId(entity.getGroupId());
            ruleDefinition.setPriority(entity.getPriority());
            ruleDefinition.setEnabled(entity.getEnabled());
            ruleDefinition.setDescription(entity.getDescription());
            ruleDefinition.setCreateTime(entity.getCreateTime());
            ruleDefinition.setUpdateTime(entity.getUpdateTime());
            
            // 将JSON反序列化为复杂对象
            if (entity.getTimeConditions() != null && !entity.getTimeConditions().trim().isEmpty()) {
                ruleDefinition.setTimeConditions(objectMapper.readValue(entity.getTimeConditions(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, TimeCondition.class)));
            }
            if (entity.getTriggerCondition() != null && !entity.getTriggerCondition().trim().isEmpty()) {
                ruleDefinition.setTriggerCondition(objectMapper.readValue(entity.getTriggerCondition(),
                    TriggerCondition.class));
            }
            if (entity.getActions() != null && !entity.getActions().trim().isEmpty()) {
                ruleDefinition.setActions(objectMapper.readValue(entity.getActions(), 
                    objectMapper.getTypeFactory().constructCollectionType(List.class, ActionDefinition.class)));
            }
            
            return ruleDefinition;
        } catch (JsonProcessingException e) {
            throw new RuntimeException("Failed to convert Entity to RuleDefinition", e);
        }
    }
}
