package com.inxaiot.ruleengine.storage;

import com.inxaiot.ruleengine.storage.entity.GlobalCalendarEntity;
import com.inxaiot.ruleengine.storage.mapper.GlobalCalendarMapper;
import com.inxaiot.ruleengine.trigger.time.GlobalCalendar;
import com.inxaiot.ruleengine.trigger.time.TimeRange;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 全局配置存储服务
 * 提供全局日历等配置信息的存储和查询功能
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Service
@Transactional
public class GlobalConfigStorageService {
    
    private static final Logger logger = LoggerFactory.getLogger(GlobalConfigStorageService.class);
    
    private final GlobalCalendarMapper globalCalendarMapper;

    @Autowired
    public GlobalConfigStorageService(GlobalCalendarMapper globalCalendarMapper) {
        this.globalCalendarMapper = globalCalendarMapper;
    }

    /**
     * 加载全局日历
     */
    public GlobalCalendar loadGlobalCalendar() {
        try {
            List<GlobalCalendarEntity> entities = globalCalendarMapper.findAllEnabledCalendars();
            return convertToGlobalCalendar(entities);
        } catch (Exception e) {
            logger.error("Failed to load global calendar", e);
            throw new RuntimeException("Failed to load global calendar", e);
        }
    }

    /**
     * 保存日历记录
     */
    public void saveCalendarRecord(LocalDate date, String type, String description) {
        try {
            GlobalCalendarEntity entity = new GlobalCalendarEntity(date, type, description);
            entity.setEnabled(true);
            
            if (globalCalendarMapper.existsCalendarByDateAndType(date, type)) {
                globalCalendarMapper.updateCalendarById(entity);
                logger.info("Updated calendar record: {} - {}", date, type);
            } else {
                globalCalendarMapper.insertCalendar(entity);
                logger.info("Inserted calendar record: {} - {}", date, type);
            }
        } catch (Exception e) {
            logger.error("Failed to save calendar record: {} - {}", date, type, e);
            throw new RuntimeException("Failed to save calendar record", e);
        }
    }

    /**
     * 批量保存日历记录
     */
    public void batchSaveCalendarRecords(List<GlobalCalendarEntity> calendars) {
        try {
            int inserted = globalCalendarMapper.batchInsertCalendars(calendars);
            logger.info("Batch inserted calendar records, count: {}, affected rows: {}", calendars.size(), inserted);
        } catch (Exception e) {
            logger.error("Failed to batch save calendar records", e);
            throw new RuntimeException("Failed to batch save calendar records", e);
        }
    }

    /**
     * 根据类型查询日历记录
     */
    public List<GlobalCalendarEntity> findCalendarsByType(String type) {
        try {
            return globalCalendarMapper.findEnabledCalendarsByType(type);
        } catch (Exception e) {
            logger.error("Failed to find calendars by type: {}", type, e);
            throw new RuntimeException("Failed to find calendars by type: " + type, e);
        }
    }

    /**
     * 根据日期范围查询日历记录
     */
    public List<GlobalCalendarEntity> findCalendarsByDateRange(LocalDate startDate, LocalDate endDate) {
        try {
            return globalCalendarMapper.findCalendarsByDateRange(startDate, endDate);
        } catch (Exception e) {
            logger.error("Failed to find calendars by date range: {} - {}", startDate, endDate, e);
            throw new RuntimeException("Failed to find calendars by date range", e);
        }
    }

    /**
     * 根据年份查询日历记录
     */
    public List<GlobalCalendarEntity> findCalendarsByYear(int year) {
        try {
            return globalCalendarMapper.findCalendarsByYear(year);
        } catch (Exception e) {
            logger.error("Failed to find calendars by year: {}", year, e);
            throw new RuntimeException("Failed to find calendars by year: " + year, e);
        }
    }

    /**
     * 检查指定日期是否为特定类型
     */
    public boolean isDateOfType(LocalDate date, String type) {
        try {
            return globalCalendarMapper.existsCalendarByDateAndType(date, type);
        } catch (Exception e) {
            logger.error("Failed to check date type: {} - {}", date, type, e);
            return false;
        }
    }

    /**
     * 删除日历记录
     */
    public void deleteCalendarRecord(LocalDate date, String type) {
        try {
            GlobalCalendarEntity entity = globalCalendarMapper.findCalendarByDateAndType(date, type);
            if (entity != null) {
                globalCalendarMapper.deleteCalendarById(entity.getId());
                logger.info("Deleted calendar record: {} - {}", date, type);
            }
        } catch (Exception e) {
            logger.error("Failed to delete calendar record: {} - {}", date, type, e);
            throw new RuntimeException("Failed to delete calendar record", e);
        }
    }

    /**
     * 批量删除日历记录
     */
    public void batchDeleteCalendarsByType(String type) {
        try {
            int deleted = globalCalendarMapper.batchDeleteCalendarsByType(type);
            logger.info("Batch deleted calendar records by type: {}, affected rows: {}", type, deleted);
        } catch (Exception e) {
            logger.error("Failed to batch delete calendars by type: {}", type, e);
            throw new RuntimeException("Failed to batch delete calendars by type: " + type, e);
        }
    }

    /**
     * 获取所有日历类型
     */
    public List<String> getAllCalendarTypes() {
        try {
            return globalCalendarMapper.findAllCalendarTypes();
        } catch (Exception e) {
            logger.error("Failed to get all calendar types", e);
            throw new RuntimeException("Failed to get all calendar types", e);
        }
    }

    /**
     * 统计日历记录数量
     */
    public int countCalendarsByType(String type) {
        try {
            return globalCalendarMapper.countCalendarsByType(type);
        } catch (Exception e) {
            logger.error("Failed to count calendars by type: {}", type, e);
            return 0;
        }
    }

    /**
     * 将日历实体列表转换为全局日历对象
     */
    private GlobalCalendar convertToGlobalCalendar(List<GlobalCalendarEntity> entities) {
        GlobalCalendar globalCalendar = new GlobalCalendar();
        
        // 按类型分组
        Map<String, List<LocalDate>> calendarMap = entities.stream()
                .collect(Collectors.groupingBy(
                    GlobalCalendarEntity::getCalendarType,
                    Collectors.mapping(GlobalCalendarEntity::getCalendarDate, Collectors.toList())
                ));
        
        // 设置节假日
        globalCalendar.setHolidays(calendarMap.getOrDefault(GlobalCalendarEntity.CalendarTypes.HOLIDAY, new ArrayList<>()));

        // 设置夏令时间和冬令时间（如果有的话）
        List<LocalDate> summerDates = calendarMap.getOrDefault(GlobalCalendarEntity.CalendarTypes.SUMMER, new ArrayList<>());
        if (!summerDates.isEmpty()) {
            // 假设第一个日期是开始日期，最后一个日期是结束日期
            LocalDate summerStart = summerDates.get(0);
            LocalDate summerEnd = summerDates.get(summerDates.size() - 1);
            globalCalendar.setSummerTime(summerStart, summerEnd);
        }

        List<LocalDate> winterDates = calendarMap.getOrDefault(GlobalCalendarEntity.CalendarTypes.WINTER, new ArrayList<>());
        if (!winterDates.isEmpty()) {
            // 假设第一个日期是开始日期，最后一个日期是结束日期
            LocalDate winterStart = winterDates.get(0);
            LocalDate winterEnd = winterDates.get(winterDates.size() - 1);
            globalCalendar.setWinterTime(winterStart, winterEnd);
        }
        
        logger.debug("Loaded global calendar with {} records", entities.size());
        return globalCalendar;
    }

    /**
     * 保存全局日历
     */
    public void saveGlobalCalendar(GlobalCalendar globalCalendar) {
        try {
            // 清空现有数据
            globalCalendarMapper.deleteAllCalendars();

            // 保存新数据
            List<GlobalCalendarEntity> entities = new ArrayList<>();

            // 添加节假日
            if (globalCalendar.getHolidays() != null) {
                globalCalendar.getHolidays().forEach(date ->
                    entities.add(new GlobalCalendarEntity(date, GlobalCalendarEntity.CalendarTypes.HOLIDAY, "节假日")));
            }

            // 添加夏令时间
            if (globalCalendar.getSummerTime() != null && globalCalendar.getSummerTime().isValid()) {
                TimeRange summerTime = globalCalendar.getSummerTime();
                entities.add(new GlobalCalendarEntity(summerTime.getStartDate(), GlobalCalendarEntity.CalendarTypes.SUMMER, "夏令时间开始"));
                entities.add(new GlobalCalendarEntity(summerTime.getEndDate(), GlobalCalendarEntity.CalendarTypes.SUMMER, "夏令时间结束"));
            }

            // 添加冬令时间
            if (globalCalendar.getWinterTime() != null && globalCalendar.getWinterTime().isValid()) {
                TimeRange winterTime = globalCalendar.getWinterTime();
                entities.add(new GlobalCalendarEntity(winterTime.getStartDate(), GlobalCalendarEntity.CalendarTypes.WINTER, "冬令时间开始"));
                entities.add(new GlobalCalendarEntity(winterTime.getEndDate(), GlobalCalendarEntity.CalendarTypes.WINTER, "冬令时间结束"));
            }

            entities.forEach(entity -> entity.setEnabled(true));
            if (!entities.isEmpty()) {
                batchSaveCalendarRecords(entities);
            }

            logger.info("Saved global calendar with {} entries", entities.size());
        } catch (Exception e) {
            logger.error("Failed to save global calendar", e);
            throw new RuntimeException("Failed to save global calendar", e);
        }
    }

    /**
     * 添加日历条目
     */
    public void addCalendarEntry(LocalDate date, String type, String description) {
        saveCalendarRecord(date, type, description);
    }

    /**
     * 删除日历条目（按日期和类型）
     */
    public void removeCalendarEntry(LocalDate date, String type) {
        deleteCalendarRecord(date, type);
    }

    /**
     * 删除日历条目（按日期，删除该日期的所有类型）
     */
    public void removeCalendarEntry(LocalDate date) {
        try {
            List<GlobalCalendarEntity> entities = globalCalendarMapper.findCalendarsByDate(date);
            for (GlobalCalendarEntity entity : entities) {
                globalCalendarMapper.deleteCalendarById(entity.getId());
            }
            logger.info("Deleted all calendar entries for date: {}", date);
        } catch (Exception e) {
            logger.error("Failed to delete calendar entries for date: {}", date, e);
            throw new RuntimeException("Failed to delete calendar entries", e);
        }
    }

    /**
     * 获取指定日期的日历条目
     */
    public List<Map<String, Object>> getCalendarEntries(LocalDate date) {
        try {
            List<GlobalCalendarEntity> entities = globalCalendarMapper.findCalendarsByDate(date);
            return entities.stream()
                .map(entity -> {
                    Map<String, Object> entry = new java.util.HashMap<>();
                    entry.put("date", entity.getCalendarDate());
                    entry.put("type", entity.getCalendarType());
                    entry.put("description", entity.getDescription());
                    entry.put("enabled", entity.getEnabled());
                    return entry;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to get calendar entries for date: {}", date, e);
            throw new RuntimeException("Failed to get calendar entries", e);
        }
    }

    /**
     * 获取日期范围内的日历条目（按类型过滤）
     */
    public List<Map<String, Object>> getCalendarEntriesInRange(LocalDate startDate, LocalDate endDate, String type) {
        try {
            List<GlobalCalendarEntity> entities = globalCalendarMapper.findCalendarsByDateRangeAndType(startDate, endDate, type);
            return entities.stream()
                .map(entity -> {
                    Map<String, Object> entry = new java.util.HashMap<>();
                    entry.put("date", entity.getCalendarDate());
                    entry.put("type", entity.getCalendarType());
                    entry.put("description", entity.getDescription());
                    entry.put("enabled", entity.getEnabled());
                    return entry;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to get calendar entries in range: {} - {}, type: {}", startDate, endDate, type, e);
            throw new RuntimeException("Failed to get calendar entries", e);
        }
    }

    /**
     * 获取日期范围内的日历条目（所有类型）
     */
    public List<Map<String, Object>> getCalendarEntriesInRange(LocalDate startDate, LocalDate endDate) {
        try {
            List<GlobalCalendarEntity> entities = globalCalendarMapper.findCalendarsByDateRange(startDate, endDate);
            return entities.stream()
                .map(entity -> {
                    Map<String, Object> entry = new java.util.HashMap<>();
                    entry.put("date", entity.getCalendarDate());
                    entry.put("type", entity.getCalendarType());
                    entry.put("description", entity.getDescription());
                    entry.put("enabled", entity.getEnabled());
                    return entry;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to get calendar entries in range: {} - {}", startDate, endDate, e);
            throw new RuntimeException("Failed to get calendar entries", e);
        }
    }

    /**
     * 初始化默认日历数据
     */
    public void initializeDefaultCalendar(int year) {
        try {
            // 检查是否已有该年份的数据
            List<GlobalCalendarEntity> existingRecords = globalCalendarMapper.findCalendarsByYear(year);
            if (!existingRecords.isEmpty()) {
                logger.info("Calendar data for year {} already exists, skipping initialization", year);
                return;
            }

            // 创建默认的季节数据
            List<GlobalCalendarEntity> defaultCalendars = new ArrayList<>();
            defaultCalendars.add(new GlobalCalendarEntity(LocalDate.of(year, 3, 20), GlobalCalendarEntity.CalendarTypes.SPRING, "春季开始"));
            defaultCalendars.add(new GlobalCalendarEntity(LocalDate.of(year, 6, 21), GlobalCalendarEntity.CalendarTypes.SUMMER, "夏季开始"));
            defaultCalendars.add(new GlobalCalendarEntity(LocalDate.of(year, 9, 23), GlobalCalendarEntity.CalendarTypes.AUTUMN, "秋季开始"));
            defaultCalendars.add(new GlobalCalendarEntity(LocalDate.of(year, 12, 21), GlobalCalendarEntity.CalendarTypes.WINTER, "冬季开始"));

            defaultCalendars.forEach(calendar -> calendar.setEnabled(true));
            batchSaveCalendarRecords(defaultCalendars);

            logger.info("Initialized default calendar data for year {}", year);
        } catch (Exception e) {
            logger.error("Failed to initialize default calendar for year: {}", year, e);
            throw new RuntimeException("Failed to initialize default calendar", e);
        }
    }

    /**
     * 批量导入日历数据
     */
    public void batchImportCalendarData(List<Map<String, Object>> calendarData) {
        try {
            List<GlobalCalendarEntity> entities = new ArrayList<>();

            for (Map<String, Object> data : calendarData) {
                LocalDate date = LocalDate.parse(data.get("date").toString());
                String type = data.get("type").toString();
                String description = data.getOrDefault("description", "").toString();

                GlobalCalendarEntity entity = new GlobalCalendarEntity(date, type, description);
                entity.setEnabled(true);
                entities.add(entity);
            }

            if (!entities.isEmpty()) {
                batchSaveCalendarRecords(entities);
            }

            logger.info("Batch imported {} calendar entries", entities.size());
        } catch (Exception e) {
            logger.error("Failed to batch import calendar data", e);
            throw new RuntimeException("Failed to batch import calendar data", e);
        }
    }

    /**
     * 导出日历数据
     */
    public List<Map<String, Object>> exportCalendarData(LocalDate startDate, LocalDate endDate, String type) {
        try {
            List<GlobalCalendarEntity> entities;
            if (type != null && !type.trim().isEmpty()) {
                entities = globalCalendarMapper.findCalendarsByDateRangeAndType(startDate, endDate, type);
            } else {
                entities = globalCalendarMapper.findCalendarsByDateRange(startDate, endDate);
            }

            return entities.stream()
                .map(entity -> {
                    Map<String, Object> data = new java.util.HashMap<>();
                    data.put("date", entity.getCalendarDate().toString());
                    data.put("type", entity.getCalendarType());
                    data.put("description", entity.getDescription());
                    data.put("enabled", entity.getEnabled());
                    return data;
                })
                .collect(Collectors.toList());
        } catch (Exception e) {
            logger.error("Failed to export calendar data: {} - {}, type: {}", startDate, endDate, type, e);
            throw new RuntimeException("Failed to export calendar data", e);
        }
    }

    /**
     * 刷新日历缓存
     */
    public void refreshCalendarCache() {
        try {
            // 这里可以实现缓存刷新逻辑
            // 目前只是记录日志
            logger.info("Calendar cache refreshed");
        } catch (Exception e) {
            logger.error("Failed to refresh calendar cache", e);
            throw new RuntimeException("Failed to refresh calendar cache", e);
        }
    }

    /**
     * 获取系统配置
     */
    public Map<String, Object> getSystemConfig() {
        try {
            // 这里可以实现系统配置的获取逻辑
            // 目前返回一个模拟的配置
            Map<String, Object> config = new java.util.HashMap<>();
            config.put("timezone", "Asia/Shanghai");
            config.put("locale", "zh_CN");
            config.put("dateFormat", "yyyy-MM-dd");
            config.put("timeFormat", "HH:mm:ss");

            logger.debug("Retrieved system config");
            return config;
        } catch (Exception e) {
            logger.error("Failed to get system config", e);
            throw new RuntimeException("Failed to get system config", e);
        }
    }

    /**
     * 更新系统配置
     */
    public void updateSystemConfig(Map<String, Object> config) {
        try {
            // 这里可以实现系统配置的更新逻辑
            // 目前只是记录日志
            logger.info("System config updated: {}", config);
        } catch (Exception e) {
            logger.error("Failed to update system config", e);
            throw new RuntimeException("Failed to update system config", e);
        }
    }

    /**
     * 获取系统配置值
     */
    public Object getSystemConfigValue(String key) {
        try {
            Map<String, Object> config = getSystemConfig();
            return config.get(key);
        } catch (Exception e) {
            logger.error("Failed to get system config value for key: {}", key, e);
            throw new RuntimeException("Failed to get system config value", e);
        }
    }

    /**
     * 设置系统配置值
     */
    public void setSystemConfigValue(String key, Object value) {
        try {
            Map<String, Object> config = new java.util.HashMap<>();
            config.put(key, value);
            updateSystemConfig(config);
        } catch (Exception e) {
            logger.error("Failed to set system config value for key: {}", key, e);
            throw new RuntimeException("Failed to set system config value", e);
        }
    }
}
