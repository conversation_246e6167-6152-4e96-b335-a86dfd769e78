package com.inxaiot.ruleengine.storage.entity;

import java.time.LocalDateTime;

/**
 * 规则定义数据库实体类
 * 对应rule_definition表
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class RuleDefinitionEntity {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 规则唯一ID
     */
    private String ruleId;
    
    /**
     * 规则名称
     */
    private String ruleName;
    
    /**
     * 目标设备ID
     */
    private String targetDeviceId;
    
    /**
     * 目标设备类型
     */
    private String targetDeviceType;
    
    /**
     * 业务ID
     */
    private String bizId;
    
    /**
     * 分组ID
     */
    private String groupId;
    
    /**
     * 优先级
     */
    private Integer priority;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 时间条件 (JSON格式)
     */
    private String timeConditions;
    
    /**
     * 触发条件 (JSON格式)
     */
    private String triggerCondition;
    
    /**
     * 动作列表 (JSON格式)
     */
    private String actions;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    // 构造函数
    public RuleDefinitionEntity() {
    }

    public RuleDefinitionEntity(String ruleId, String ruleName) {
        this.ruleId = ruleId;
        this.ruleName = ruleName;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRuleId() {
        return ruleId;
    }

    public void setRuleId(String ruleId) {
        this.ruleId = ruleId;
    }

    public String getRuleName() {
        return ruleName;
    }

    public void setRuleName(String ruleName) {
        this.ruleName = ruleName;
    }

    public String getTargetDeviceId() {
        return targetDeviceId;
    }

    public void setTargetDeviceId(String targetDeviceId) {
        this.targetDeviceId = targetDeviceId;
    }

    public String getTargetDeviceType() {
        return targetDeviceType;
    }

    public void setTargetDeviceType(String targetDeviceType) {
        this.targetDeviceType = targetDeviceType;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public Integer getPriority() {
        return priority;
    }

    public void setPriority(Integer priority) {
        this.priority = priority;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public String getTimeConditions() {
        return timeConditions;
    }

    public void setTimeConditions(String timeConditions) {
        this.timeConditions = timeConditions;
    }

    public String getTriggerCondition() {
        return triggerCondition;
    }

    public void setTriggerCondition(String triggerConditions) {
        this.triggerCondition = triggerCondition;
    }

    public String getActions() {
        return actions;
    }

    public void setActions(String actions) {
        this.actions = actions;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "RuleDefinitionEntity{" +
                "id=" + id +
                ", ruleId='" + ruleId + '\'' +
                ", ruleName='" + ruleName + '\'' +
                ", targetDeviceId='" + targetDeviceId + '\'' +
                ", bizId='" + bizId + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", createTime=" + createTime +
                '}';
    }
}
