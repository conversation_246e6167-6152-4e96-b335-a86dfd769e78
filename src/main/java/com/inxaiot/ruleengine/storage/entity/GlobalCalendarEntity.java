package com.inxaiot.ruleengine.storage.entity;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 全局日历数据库实体类
 * 对应global_calendar表
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class GlobalCalendarEntity {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 日历日期
     */
    private LocalDate calendarDate;
    
    /**
     * 日历类型
     * WORKDAY, HOLIDAY, WEEKEND, SPRING, SUMMER, AUTUMN, WINTER
     */
    private String calendarType;
    
    /**
     * 描述
     */
    private String description;
    
    /**
     * 是否启用
     */
    private Boolean enabled;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 日历类型常量
     */
    public static class CalendarTypes {
        public static final String WORKDAY = "WORKDAY";
        public static final String HOLIDAY = "HOLIDAY";
        public static final String WEEKEND = "WEEKEND";
        public static final String SPRING = "SPRING";
        public static final String SUMMER = "SUMMER";
        public static final String AUTUMN = "AUTUMN";
        public static final String WINTER = "WINTER";
    }

    // 构造函数
    public GlobalCalendarEntity() {
    }

    public GlobalCalendarEntity(LocalDate calendarDate, String calendarType) {
        this.calendarDate = calendarDate;
        this.calendarType = calendarType;
    }

    public GlobalCalendarEntity(LocalDate calendarDate, String calendarType, String description) {
        this(calendarDate, calendarType);
        this.description = description;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public LocalDate getCalendarDate() {
        return calendarDate;
    }

    public void setCalendarDate(LocalDate calendarDate) {
        this.calendarDate = calendarDate;
    }

    public String getCalendarType() {
        return calendarType;
    }

    public void setCalendarType(String calendarType) {
        this.calendarType = calendarType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Boolean getEnabled() {
        return enabled;
    }

    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    public LocalDateTime getCreateTime() {
        return createTime;
    }

    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }

    public LocalDateTime getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        return "GlobalCalendarEntity{" +
                "id=" + id +
                ", calendarDate=" + calendarDate +
                ", calendarType='" + calendarType + '\'' +
                ", description='" + description + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
