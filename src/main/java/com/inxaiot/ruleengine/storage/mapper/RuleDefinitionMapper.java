package com.inxaiot.ruleengine.storage.mapper;

import com.inxaiot.ruleengine.storage.entity.RuleDefinitionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 规则定义Mapper接口
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
@Mapper
public interface RuleDefinitionMapper {
    
    /**
     * 插入规则定义
     */
    int insertRule(RuleDefinitionEntity rule);
    
    /**
     * 根据ID更新规则定义
     */
    int updateRuleById(RuleDefinitionEntity rule);
    
    /**
     * 根据ruleId更新规则定义
     */
    int updateRuleByRuleId(RuleDefinitionEntity rule);
    
    /**
     * 根据ID删除规则定义
     */
    int deleteRuleById(@Param("id") Long id);
    
    /**
     * 根据ruleId删除规则定义
     */
    int deleteRuleByRuleId(@Param("ruleId") String ruleId);
    
    /**
     * 根据ID查询规则定义
     */
    RuleDefinitionEntity findRuleById(@Param("id") Long id);
    
    /**
     * 根据ruleId查询规则定义
     */
    RuleDefinitionEntity findRuleByRuleId(@Param("ruleId") String ruleId);
    
    /**
     * 查询所有规则定义
     */
    List<RuleDefinitionEntity> findAllRules();
    
    /**
     * 查询所有启用的规则定义
     */
    List<RuleDefinitionEntity> findAllEnabledRules();
    
    /**
     * 根据目标设备ID查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByTargetDeviceId(@Param("targetDeviceId") String targetDeviceId);
    
    /**
     * 根据目标设备ID查询启用的规则定义
     */
    List<RuleDefinitionEntity> findEnabledRulesByTargetDeviceId(@Param("targetDeviceId") String targetDeviceId);
    
    /**
     * 根据业务ID查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByBizId(@Param("bizId") String bizId);
    
    /**
     * 根据分组ID查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByGroupId(@Param("groupId") String groupId);
    
    /**
     * 根据设备类型查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByDeviceType(@Param("deviceType") String deviceType);
    
    /**
     * 查询与指定设备相关的所有启用规则
     * 包括目标设备和触发条件中涉及的设备
     */
    List<RuleDefinitionEntity> findAllEnabledRulesRelevantToDevice(@Param("deviceId") String deviceId);
    
    /**
     * 根据优先级范围查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByPriorityRange(@Param("minPriority") Integer minPriority, @Param("maxPriority") Integer maxPriority);
    
    /**
     * 批量插入规则定义
     */
    int batchInsertRules(@Param("rules") List<RuleDefinitionEntity> rules);
    
    /**
     * 批量更新规则启用状态
     */
    int batchUpdateRuleEnabled(@Param("ruleIds") List<String> ruleIds, @Param("enabled") Boolean enabled);
    
    /**
     * 批量删除规则定义
     */
    int batchDeleteRulesByRuleIds(@Param("ruleIds") List<String> ruleIds);
    
    /**
     * 根据业务ID批量删除规则定义
     */
    int batchDeleteRulesByBizId(@Param("bizId") String bizId);
    
    /**
     * 根据分组ID批量删除规则定义
     */
    int batchDeleteRulesByGroupId(@Param("groupId") String groupId);
    
    /**
     * 统计规则总数
     */
    int countAllRules();
    
    /**
     * 统计启用的规则数
     */
    int countEnabledRules();
    
    /**
     * 根据设备ID统计相关规则数
     */
    int countRulesByDeviceId(@Param("deviceId") String deviceId);
    
    /**
     * 根据业务ID统计规则数
     */
    int countRulesByBizId(@Param("bizId") String bizId);
    
    /**
     * 检查ruleId是否存在
     */
    boolean existsRuleByRuleId(@Param("ruleId") String ruleId);
    
    /**
     * 分页查询规则定义
     */
    List<RuleDefinitionEntity> findRulesWithPagination(@Param("offset") Integer offset, @Param("limit") Integer limit);
    
    /**
     * 根据条件分页查询规则定义
     */
    List<RuleDefinitionEntity> findRulesByConditionWithPagination(@Param("targetDeviceId") String targetDeviceId,
                                                                  @Param("bizId") String bizId,
                                                                  @Param("groupId") String groupId,
                                                                  @Param("enabled") Boolean enabled,
                                                                  @Param("offset") Integer offset,
                                                                  @Param("limit") Integer limit);
}
