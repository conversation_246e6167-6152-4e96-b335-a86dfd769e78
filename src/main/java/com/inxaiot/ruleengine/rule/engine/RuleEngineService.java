package com.inxaiot.ruleengine.rule.engine;

/**
 * 规则引擎服务接口
 * 用于解耦设备状态管理和规则引擎核心
 *
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public interface RuleEngineService {
    
    /**
     * 处理设备事件
     *
     * @param deviceId 设备ID
     * @param pointId 点位ID
     * @param value 点位值
     */
    void processDeviceEvent(String deviceId, String pointId, Object value);

    /**
     * 触发设备超时相关的规则
     *
     * @param deviceId 设备ID
     * @param pointId 点位ID
     * @param durationMinutes 持续时间（分钟）
     */
    void triggerRulesForDeviceTimeout(String deviceId, String pointId, long durationMinutes);

    
    /**
     * 触发基于时间的场景规则
     * 
     * @param sceneIdOrRuleGroupId 场景ID或规则组ID
     */
    void triggerTimeBasedScene(String sceneIdOrRuleGroupId);
    
    /**
     * 刷新规则缓存
     */
    void refreshRulesCache();

    /**
     * 触发规则激活（用于时间驱动规则）
     * 预留给未来的TimeTriggerService使用
     *
     * @param ruleId 规则ID
     */
    void triggerRuleActivation(String ruleId);

    /**
     * 触发规则失活（用于时间段规则）
     * 预留给未来的TimeTriggerService使用
     *
     * @param ruleId 规则ID
     */
    void triggerRuleDeactivation(String ruleId);
}
