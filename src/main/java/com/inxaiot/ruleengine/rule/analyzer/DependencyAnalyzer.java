package com.inxaiot.ruleengine.rule.analyzer;

import com.inxaiot.ruleengine.common.operator.Operators;
import com.inxaiot.ruleengine.rule.definition.DeviceCondition;
import com.inxaiot.ruleengine.rule.definition.RuleDefinition;
import com.inxaiot.ruleengine.rule.definition.TriggerCondition;
import com.inxaiot.ruleengine.device.state.DevicePointRef;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashSet;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 依赖分析器
 * 用于分析规则定义中引用的设备点位依赖关系
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class DependencyAnalyzer {

    private static final Logger logger = LoggerFactory.getLogger(DependencyAnalyzer.class);
    
    // 匹配设备点位引用的正则表达式，如：device123.temperature, sensor_01.humidity
    private static final Pattern DEVICE_POINT_PATTERN = Pattern.compile("([a-zA-Z0-9_-]+)\\.([a-zA-Z0-9_-]+)");
    
    /**
     * 从规则定义中提取所有需要的设备点位依赖
     * 
     * @param rule 规则定义
     * @return 设备点位引用集合
     */
    public Set<DevicePointRef> extractRequiredDevicePoints(RuleDefinition rule) {
        Set<DevicePointRef> dependencies = new HashSet<>();
        
        try {
            // 分析触发条件中的设备引用
            if (rule.getTriggerCondition() != null) {
                dependencies.addAll(analyzeTriggerCondition(rule.getTriggerCondition()));
            }

            // 分析时间条件中可能的设备引用（某些时间条件可能依赖设备状态）
            // 注：大部分时间条件不依赖设备状态，这里预留扩展

            logger.debug("Extracted {} device point dependencies for rule {}", dependencies.size(), rule.getRuleId());

        } catch (Exception e) {
            logger.error("Error extracting dependencies for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }
        
        return dependencies;
    }
    
    /**
     * 分析触发条件中的设备点位依赖
     */
    private Set<DevicePointRef> analyzeTriggerCondition(TriggerCondition triggerCondition) {
        Set<DevicePointRef> dependencies = new HashSet<>();

        if (triggerCondition.getConditions() != null) {
            for (DeviceCondition deviceCondition : triggerCondition.getConditions()) {
                dependencies.addAll(analyzeDeviceCondition(deviceCondition));
            }
        }

        return dependencies;
    }
    
    /**
     * 分析单个设备条件中的设备点位依赖
     */
    private Set<DevicePointRef> analyzeDeviceCondition(DeviceCondition condition) {
        Set<DevicePointRef> dependencies = new HashSet<>();

        try {
            // 从设备ID和点位ID直接提取
            if (condition.getSourceDeviceId() != null && condition.getPointId() != null) {
                Duration maxAge = determineMaxAge(condition);
                DevicePointRef ref = new DevicePointRef(condition.getSourceDeviceId(), condition.getPointId(), maxAge);
                dependencies.add(ref);

                logger.trace("Found direct device reference: {}.{}", condition.getSourceDeviceId(), condition.getPointId());
            }

            // 从目标值中提取可能的设备引用（如果目标值是另一个设备的值）
            if (condition.getValue() instanceof String) {
                String targetValue = (String) condition.getValue();
                dependencies.addAll(extractFromExpression(targetValue));
            }

            // 从上限值中提取可能的设备引用（用于BETWEEN操作符）
            if (condition.getUpperValue() instanceof String) {
                String upperValue = (String) condition.getUpperValue();
                dependencies.addAll(extractFromExpression(upperValue));
            }

        } catch (Exception e) {
            logger.warn("Error analyzing device condition {}: {}", condition, e.getMessage());
        }

        return dependencies;
    }
    
    /**
     * 从表达式字符串中提取设备点位引用
     * 支持格式：device123.temperature, sensor_01.humidity
     */
    private Set<DevicePointRef> extractFromExpression(String expression) {
        Set<DevicePointRef> dependencies = new HashSet<>();
        
        if (expression == null || expression.trim().isEmpty()) {
            return dependencies;
        }
        
        Matcher matcher = DEVICE_POINT_PATTERN.matcher(expression);
        while (matcher.find()) {
            String deviceId = matcher.group(1);
            String pointId = matcher.group(2);
            
            DevicePointRef ref = new DevicePointRef(deviceId, pointId);
            dependencies.add(ref);
            
            logger.trace("Found device reference in expression '{}': {}.{}", 
                        expression, deviceId, pointId);
        }
        
        return dependencies;
    }
    
    /**
     * 根据设备条件类型确定状态的最大有效期
     */
    private Duration determineMaxAge(DeviceCondition condition) {
        // 根据操作符类型确定合适的有效期
        String operator = condition.getOperator();

        if (operator != null) {
            switch (operator.toUpperCase()) {
                case Operators.Duration.STATES_KEEP_MINUTES:
                    // 持续时间类操作符，状态需要更长的有效期
                    // 考虑持续时间要求，设置为持续时间的1.5倍或至少10分钟
                    long durationMinutes = condition.getDurationMinutes();
                    long maxAgeMinutes = Math.max(10, (long)(durationMinutes * 1.5));
                    return Duration.ofMinutes(maxAgeMinutes);

                case Operators.Basic.EQUALS:
                case Operators.Basic.NOT_EQUALS:
                case Operators.Basic.GREATER_THAN:
                case Operators.Basic.LESS_THAN:
                case Operators.Basic.GREATER_THAN_OR_EQUAL:
                case Operators.Basic.LESS_THAN_OR_EQUAL:
                case Operators.Range.BETWEEN:
                case Operators.Range.NOT_BETWEEN:
                case Operators.Text.CONTAINS:
                case Operators.Text.NOT_CONTAINS:
                    // 数值比较操作符，使用默认有效期
                    return Duration.ofMinutes(5);

                default:
                    // 其他操作符，使用默认有效期
                    return Duration.ofMinutes(5);
            }
        }

        return Duration.ofMinutes(5); // 默认5分钟
    }
    
    /**
     * 检查规则是否依赖特定的设备点位
     */
    public boolean isDependentOn(RuleDefinition rule, String deviceId, String pointId) {
        Set<DevicePointRef> dependencies = extractRequiredDevicePoints(rule);
        DevicePointRef target = new DevicePointRef(deviceId, pointId);
        return dependencies.contains(target);
    }
    
    /**
     * 获取与指定设备点位相关的所有规则
     */
    public boolean isRuleRelatedToDevice(RuleDefinition rule, String deviceId, String pointId) {
        return isDependentOn(rule, deviceId, pointId);
    }
}
