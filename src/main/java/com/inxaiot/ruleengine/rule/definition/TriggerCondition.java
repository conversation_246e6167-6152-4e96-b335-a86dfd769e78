package com.inxaiot.ruleengine.rule.definition;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 设备触发条件定义
 * 包含多个设备条件的组合逻辑
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class TriggerCondition {
    
    /**
     * 匹配逻辑枚举
     */
    public enum MatchLogic {
        /**
         * 所有条件都必须满足 (AND逻辑)
         */
        ALL,
        /**
         * 任意条件满足即可 (OR逻辑)
         */
        ANY
    }
    
    /**
     * 条件匹配逻辑，默认为ALL(AND)
     */
    @JsonProperty("logic")
    private MatchLogic logic = MatchLogic.ALL;
    
    /**
     * 设备条件列表
     */
    @JsonProperty("conditions")
    private List<DeviceCondition> conditions;
    
    /**
     * 触发条件是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 触发条件描述
     */
    @JsonProperty("description")
    private String description;

    // 构造函数
    public TriggerCondition() {
    }

    public TriggerCondition(MatchLogic logic, List<DeviceCondition> conditions) {
        this.logic = logic;
        this.conditions = conditions;
    }

    // Getters and Setters
    public MatchLogic getLogic() {
        return logic;
    }

    public void setLogic(MatchLogic logic) {
        this.logic = logic;
    }

    public List<DeviceCondition> getConditions() {
        return conditions;
    }

    public void setConditions(List<DeviceCondition> conditions) {
        this.conditions = conditions;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 检查触发条件是否为空
     */
    public boolean isEmpty() {
        return conditions == null || conditions.isEmpty();
    }

    /**
     * 获取条件数量
     */
    public int getConditionCount() {
        return conditions == null ? 0 : conditions.size();
    }

    /**
     * 添加设备条件
     */
    public void addCondition(DeviceCondition condition) {
        if (conditions == null) {
            conditions = new java.util.ArrayList<>();
        }
        conditions.add(condition);
    }

    /**
     * 移除设备条件
     */
    public boolean removeCondition(DeviceCondition condition) {
        return conditions != null && conditions.remove(condition);
    }

    /**
     * 根据索引移除设备条件
     */
    public DeviceCondition removeCondition(int index) {
        if (conditions != null && index >= 0 && index < conditions.size()) {
            return conditions.remove(index);
        }
        return null;
    }

    /**
     * 清空所有条件
     */
    public void clearConditions() {
        if (conditions != null) {
            conditions.clear();
        }
    }

    @Override
    public String toString() {
        return "TriggerConditions{" +
                "logic=" + logic +
                ", conditions=" + conditions +
                ", enabled=" + enabled +
                ", conditionCount=" + getConditionCount() +
                '}';
    }
}
