package com.inxaiot.ruleengine.rule.definition;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Map;

/**
 * 动作定义
 * 定义规则触发后要执行的动作，包括设备控制、消息发送、API调用等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class ActionDefinition {
    
    /**
     * 动作类型
     * 支持的动作类型：
     * - DEVICE_CONTROL: 设备控制
     * - SEND_MESSAGE: 发送消息
     * - CALL_API: 调用API
     * - LOG_EVENT: 记录事件日志
     */
    @JsonProperty("actionType")
    private String actionType;
    /**
     * 动作执行的目标设备ID
     * 可能与规则的targetDeviceId相同或不同
     */
    @JsonProperty("targetDeviceId")
    private String targetDeviceId;
    /**
     * 目标参数
     * 用于其他动作类型的参数，如：
     * - 消息内容、接收者
     * - API地址、请求参数
     * - 空调详细设置（温度、风速、模式等）
     */
    @JsonProperty("params")
    private Map<String, Object> params;
    /**
     * 动作类型所需要的环境参数，如URL地址
     */
    @JsonProperty("typeParams")
    private Map<String, Object> typeParams;
    
    /**
     * 动作是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 动作描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 动作执行优先级
     * 数值越小优先级越高
     */
    @JsonProperty("priority")
    private int priority = 1;
    
    /**
     * 动作执行超时时间(秒)
     */
    @JsonProperty("timeoutSeconds")
    private int timeoutSeconds = 30;

    /**
     * 支持的动作类型常量
     */
    public static class ActionTypes {
        public static final String DEVICE_CONTROL = "DEVICE_CONTROL";
        public static final String SEND_MESSAGE = "SEND_MESSAGE";
        public static final String CALL_API = "CALL_API";
        public static final String LOG_EVENT = "LOG_EVENT";
    }

    /**
     * 常用参数键名
     */
    public static class TypeParamKeys {
        // 消息相关
        public static final String MESSAGE_CONTENT = "messageContent";
        public static final String MESSAGE_RECEIVER = "messageReceiver";
        public static final String MESSAGE_TYPE = "messageType";
        
        // API相关
        public static final String API_URL = "apiUrl";
        public static final String API_METHOD = "apiMethod";
        public static final String API_HEADERS = "apiHeaders";
        public static final String API_BODY = "apiBody";

        
        // 日志相关
        public static final String LOG_LEVEL = "logLevel";
        public static final String LOG_MESSAGE = "logMessage";
        public static final String LOG_CATEGORY = "logCategory";
    }

    // 构造函数
    public ActionDefinition() {
    }

    public ActionDefinition(String actionType, String targetDeviceId) {
        this.actionType = actionType;
        this.targetDeviceId = targetDeviceId;
    }

    // Getters and Setters
    public String getActionType() {
        return actionType;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public String getTargetDeviceId() {
        return targetDeviceId;
    }

    public void setTargetDeviceId(String targetDeviceId) {
        this.targetDeviceId = targetDeviceId;
    }


    public Map<String, Object> getParams() {
        return params;
    }

    public void setParams(Map<String, Object> params) {
        this.params = params;
    }

    public Map<String, Object> getTypeParams() {
        return typeParams;
    }

    public void setTypeParams(Map<String, Object> typeParams) {
        this.typeParams = typeParams;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public int getPriority() {
        return priority;
    }

    public void setPriority(int priority) {
        this.priority = priority;
    }

    public int getTimeoutSeconds() {
        return timeoutSeconds;
    }

    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }

    /**
     * 添加参数
     */
    public void addParam(String key, Object value) {
        if (params == null) {
            params = new java.util.HashMap<>();
        }
        params.put(key, value);
    }

    /**
     * 获取参数
     */
    public Object getParam(String key) {
        return params != null ? params.get(key) : null;
    }

    /**
     * 获取字符串类型参数
     */
    public String getStringParam(String key) {
        Object value = getParam(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 添加类型参数
     */
    public void addTypeParam(String key, Object value) {
        if (typeParams == null) {
            typeParams = new java.util.HashMap<>();
        }
        typeParams.put(key, value);
    }

    /**
     * 获取类型参数
     */
    public Object getTypeParam(String key) {
        return typeParams != null ? typeParams.get(key) : null;
    }

    /**
     * 获取字符串类型的类型参数
     */
    public String getStringTypeParam(String key) {
        Object value = getTypeParam(key);
        return value != null ? value.toString() : null;
    }

    /**
     * 检查动作是否有效
     */
    public boolean isValid() {
        return actionType != null && !actionType.trim().isEmpty() &&
               targetDeviceId != null && !targetDeviceId.trim().isEmpty();
    }

    /**
     * 检查是否为设备控制动作
     */
    public boolean isDeviceControlAction() {
        return ActionTypes.DEVICE_CONTROL.equals(actionType);
    }

    @Override
    public String toString() {
        return "ActionDefinition{" +
                "actionType='" + actionType + '\'' +
                ", targetDeviceId='" + targetDeviceId + '\'' +
                ", priority=" + priority +
                ", enabled=" + enabled +
                ", paramsCount=" + (params != null ? params.size() : 0) +
                ", typeParamsCount=" + (typeParams != null ? typeParams.size() : 0) +
                '}';
    }
}
