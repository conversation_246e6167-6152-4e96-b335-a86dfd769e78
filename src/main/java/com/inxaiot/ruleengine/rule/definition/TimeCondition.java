package com.inxaiot.ruleengine.rule.definition;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.LocalDate;
import java.util.List;

/**
 * 时间条件定义
 * 支持复杂的时间条件组合，包括Cron表达式、工作日、季节、个性化日历等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class TimeCondition {
    
    /**
     * Cron表达式列表，用于定义具体的时间段
     * 例如: ["0 0 8-10 ? * MON-FRI", "0 0 14-16 ? * MON-FRI"]
     * 表示工作日的8-10点和14-16点
     */
    @JsonProperty("timeCronExpressions")
    private List<String> timeCronExpressions;
    
    /**
     * 工作日列表
     * 例如：["MON", "TUE", "WED", "THU", "FRI"]
     * 或者：["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"]
     */
    @JsonProperty("workDays")
    private List<String> workDays;
    
    /**
     * 时令标识
     * 如 "SUMMER", "WINTER", "ALL"
     * 由全局日历表定义起止时间
     */
    @JsonProperty("season")
    private String season;
    
    /**
     * 个性化日历: 强制生效的日期列表
     * 即使其他条件不满足，这些日期也会使规则生效
     */
    @JsonProperty("includeDates")
    private List<LocalDate> includeDates;
    
    /**
     * 个性化日历: 强制不生效的日期列表
     * 即使其他条件满足，这些日期也不会使规则生效
     */
    @JsonProperty("excludeDates")
    private List<LocalDate> excludeDates;
    
    /**
     * 时间条件的逻辑关系
     * AND: 所有时间条件都必须满足
     * OR: 任意一个时间条件满足即可
     */
    @JsonProperty("logic")
    private TimeLogic logic = TimeLogic.AND;
    
    /**
     * 时间条件是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 时间条件描述
     */
    @JsonProperty("description")
    private String description;

    /**
     * 时间逻辑枚举
     */
    public enum TimeLogic {
        /**
         * 所有条件都必须满足
         */
        AND,
        /**
         * 任意条件满足即可
         */
        OR
    }

    // 构造函数
    public TimeCondition() {
    }

    public TimeCondition(List<String> timeCronExpressions) {
        this.timeCronExpressions = timeCronExpressions;
    }

    // Getters and Setters
    public List<String> getTimeCronExpressions() {
        return timeCronExpressions;
    }

    public void setTimeCronExpressions(List<String> timeCronExpressions) {
        this.timeCronExpressions = timeCronExpressions;
    }

    public List<String> getWorkDays() {
        return workDays;
    }

    public void setWorkDays(List<String> workDays) {
        this.workDays = workDays;
    }

    public String getSeason() {
        return season;
    }

    public void setSeason(String season) {
        this.season = season;
    }

    public List<LocalDate> getIncludeDates() {
        return includeDates;
    }

    public void setIncludeDates(List<LocalDate> includeDates) {
        this.includeDates = includeDates;
    }

    public List<LocalDate> getExcludeDates() {
        return excludeDates;
    }

    public void setExcludeDates(List<LocalDate> excludeDates) {
        this.excludeDates = excludeDates;
    }

    public TimeLogic getLogic() {
        return logic;
    }

    public void setLogic(TimeLogic logic) {
        this.logic = logic;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 检查时间条件是否为空
     */
    public boolean isEmpty() {
        return (timeCronExpressions == null || timeCronExpressions.isEmpty()) &&
               (workDays == null || workDays.isEmpty()) &&
               (season == null || season.trim().isEmpty()) &&
               (includeDates == null || includeDates.isEmpty()) &&
               (excludeDates == null || excludeDates.isEmpty());
    }

    @Override
    public String toString() {
        return "TimeCondition{" +
                "timeCronExpressions=" + timeCronExpressions +
                ", workDays=" + workDays +
                ", season='" + season + '\'' +
                ", includeDates=" + includeDates +
                ", excludeDates=" + excludeDates +
                ", logic=" + logic +
                ", enabled=" + enabled +
                '}';
    }
}
