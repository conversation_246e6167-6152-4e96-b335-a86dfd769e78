package com.inxaiot.ruleengine.rule.definition;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.inxaiot.ruleengine.common.operator.Operators;

/**
 * 设备条件定义
 * 定义单个设备的触发条件，包括设备ID、点位、操作符、比较值等
 * 
 * <AUTHOR> Assistant
 * @version 1.0.0
 * @since 2024-12-19
 */
public class DeviceCondition {
    
    /**
     * 条件数据来源设备ID (可能与targetDeviceId不同)
     */
    @JsonProperty("sourceDeviceId")
    private String sourceDeviceId;
    
    /**
     * 监控的设备点位ID
     * 如 "illuminance"(照度), "occupancy_status"(占用状态), "temperature"(温度)
     */
    @JsonProperty("pointId")
    private String pointId;
    
    /**
     * 操作符
     * 支持的操作符：
     * - EQUALS: 等于
     * - NOT_EQUALS: 不等于
     * - GREATER_THAN: 大于
     * - GREATER_THAN_OR_EQUAL: 大于等于
     * - LESS_THAN: 小于
     * - LESS_THAN_OR_EQUAL: 小于等于
     * - CONTAINS: 包含
     * - NOT_CONTAINS: 不包含
     * - BETWEEN: 介于两个值之间
     * - NOT_BETWEEN: 不在两个值之间
     * - STATES_KEEP_MINUTES: 状态持续指定分钟数
     */
    @JsonProperty("operator")
    private String operator;
    
    /**
     * 比较值
     * 如 "OCCUPIED", 500, true, "ON", "OFF" 等
     * 对于BETWEEN操作符，这是下限值
     */
    @JsonProperty("value")
    private Object value;

    /**
     * 上限值
     * 用于 BETWEEN 和 NOT_BETWEEN 操作符
     */
    @JsonProperty("upperValue")
    private Object upperValue;

    /**
     * 持续时间 (分钟)
     * 用于 STATES_KEEP_MINUTES 操作符
     */
    @JsonProperty("durationMinutes")
    private long durationMinutes;
    
    /**
     * 条件是否启用
     */
    @JsonProperty("enabled")
    private boolean enabled = true;
    
    /**
     * 条件描述
     */
    @JsonProperty("description")
    private String description;
    
    /**
     * 数据类型
     * 用于类型转换和比较
     * 支持：STRING, INTEGER, DOUBLE, BOOLEAN
     */
    @JsonProperty("dataType")
    private String dataType = "STRING";



    /**
     * 支持的数据类型常量
     */
    public static class DataTypes {
        public static final String STRING = "STRING";
        public static final String INTEGER = "INTEGER";
        public static final String DOUBLE = "DOUBLE";
        public static final String BOOLEAN = "BOOLEAN";
    }

    // 构造函数
    public DeviceCondition() {
    }

    public DeviceCondition(String sourceDeviceId, String pointId, String operator, Object value) {
        this.sourceDeviceId = sourceDeviceId;
        this.pointId = pointId;
        this.operator = operator;
        this.value = value;
    }

    public DeviceCondition(String sourceDeviceId, String pointId, String operator, Object value, long durationMinutes) {
        this(sourceDeviceId, pointId, operator, value);
        this.durationMinutes = durationMinutes;
    }

    // Getters and Setters
    public String getSourceDeviceId() {
        return sourceDeviceId;
    }

    public void setSourceDeviceId(String sourceDeviceId) {
        this.sourceDeviceId = sourceDeviceId;
    }

    public String getPointId() {
        return pointId;
    }

    public void setPointId(String pointId) {
        this.pointId = pointId;
    }

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public Object getValue() {
        return value;
    }

    public void setValue(Object value) {
        this.value = value;
    }

    public long getDurationMinutes() {
        return durationMinutes;
    }

    public void setDurationMinutes(long durationMinutes) {
        this.durationMinutes = durationMinutes;
    }

    public Object getUpperValue() {
        return upperValue;
    }

    public void setUpperValue(Object upperValue) {
        this.upperValue = upperValue;
    }

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    /**
     * 检查是否为持续时间类型的操作符
     */
    public boolean isDurationOperator() {
        return Operators.Duration.STATES_KEEP_MINUTES.equals(operator);
    }

    /**
     * 检查是否为范围类型的操作符
     */
    public boolean isRangeOperator() {
        return Operators.Range.BETWEEN.equals(operator) ||
               Operators.Range.NOT_BETWEEN.equals(operator);
    }

    /**
     * 检查条件是否有效
     */
    public boolean isValid() {
        return sourceDeviceId != null && !sourceDeviceId.trim().isEmpty() &&
               pointId != null && !pointId.trim().isEmpty() &&
               operator != null && !operator.trim().isEmpty() &&
               (value != null || isDurationOperator());
    }

    @Override
    public String toString() {
        return "DeviceCondition{" +
                "sourceDeviceId='" + sourceDeviceId + '\'' +
                ", pointId='" + pointId + '\'' +
                ", operator='" + operator + '\'' +
                ", value=" + value +
                ", durationMinutes=" + durationMinutes +
                ", dataType='" + dataType + '\'' +
                ", enabled=" + enabled +
                '}';
    }
}
