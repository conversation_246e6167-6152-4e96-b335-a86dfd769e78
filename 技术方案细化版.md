好的，我们来对整体需求和至今的讨论进行一次梳理和解读，并在此基础上，提供一个更细化的、侧重于边缘规则引擎实现的技术方案。

**I. 需求背景与对话上下文解读**

1. **核心业务目标：** 您旨在为楼宇自动化（医院、办公楼等）构建一个物联网智能场景控制系统。核心功能是通过边缘计算节点（一体箱服务器）执行联动规则，以实现对照明、空调等设备的智能化控制。未来计划扩展到设备告警和更广泛的业务联动（如发消息、调用API）。
2. **用户交互与系统架构：**
   - **屏端：** 10寸触摸屏是规则的主要配置界面，用户通过矩阵式操作（时间点 vs. 区域）为不同场景（如白班、夜班模式下的护士站、办公室）配置详细的设备控制规则。
   - **业务服务端：** 作为中心枢纽，负责存储来自所有屏端的原始配置，并承担关键的“规则分解与翻译”任务，将屏端的高层语义化配置（包含复杂的日历描述和设备控制逻辑）转换为边缘引擎能够执行的、细粒度的“原子化规则”。随后，将这些原子化规则推送到相应的边缘引擎。
   - **边缘规则引擎：** 部署在约50个边缘一体箱服务器上，每个引擎负责一个区域。它接收业务服务端下发的原子化规则，本地存储并执行这些规则，响应实时的设备传感器数据和时间条件。
3. **规则特性与复杂度：**
   - **原子化规则：** 虽然屏端配置看起来是“一个设备一条规则”，但业务服务端会进行拆解。边缘引擎处理的是这些分解后的细粒度规则。
   - **时间条件：** 这是核心复杂点之一。规则的生效时间由屏端的“横纵坐标交接点”决定，可以选择日历概念（如工作日、夏季）、具体时分秒，允许多个时间段“或”逻辑。至关重要的是，用户可以在标准日历基础上进行**个性化剔除或增补日期**，形成独特的“规则生效日历”。 **我们已达成共识：这些复杂的时间条件判断将在边缘侧执行。**
   - **设备条件：** 包括“全部/任意 (AND/OR)”匹配逻辑、具体设备点位状态（如照度、温度阈值）、以及**状态持续时间判断**（如“无人15分钟关”）。
   - **动作：** 主要为设备控制指令（开关、参数设置），未来扩展到消息发送和API调用。
   - **规则依赖：** 目前规则间无直接的执行链依赖，主要为条件内部的AND/OR。
4. **关键技术与非功能性需求：**
   - **技术栈：** Java为主。
   - **动态更新：** 规则需要支持动态下发和更新，不重启边缘引擎服务。
   - **本地存储与离线运行：** 边缘引擎需本地持久化规则，确保重启后能快速恢复，并具备一定程度的离线运行能力（基于本地规则和设备数据）。
   - **资源限制：** 边缘服务器资源相对有限（4-8核 CPU, 8-16GB RAM，共享环境），要求规则引擎方案相对轻量。
   - **团队能力与时间：** 团队对规则引擎技术不熟悉，项目时间紧张，希望短期内实现核心功能。
   - **API需求：** 边缘引擎需提供API给业务服务端，用于推送规则、查询运行规则、获取日志等。
5. **已探讨并倾向的技术选型：**
   - **规则执行核心：** 选用轻量级的 **Easy Rules** Java库。
   - **周边自定义组件：** 大量的辅助逻辑（如复杂时间条件评估、持续状态管理、规则加载与适配、API服务）将围绕Easy Rules在Java中自定义实现。
   - **本地存储：** 选用 **SQLite** 数据库。
   - **数据持久层：** 选用 **MyBatis** 框架。
   - **工程结构：** 倾向于在边缘引擎侧采用**单一Java工程、内部通过清晰的包（Package）结构**来组织代码，以简化初期管理。

**II. 相关技术选型原因**

- **Java:**
  - **团队熟悉度:** 是团队的主要开发语言，降低学习成本，提高开发效率。
  - **生态系统:** 成熟、庞大，拥有海量库和工具支持，性能优良。
  - **平台特性:** Spring Boot等框架可以快速构建健壮的应用程序。
- **Easy Rules (作为规则执行核心):**
  - **轻量级:** 非常适合资源相对受限的边缘部署。
  - **API简洁:** 学习曲线平缓，便于对规则引擎不熟悉的团队快速上手。
  - **纯Java实现:** 与现有技术栈无缝集成。
  - **专注核心:** 它提供了规则（条件-动作）的基本抽象和执行机制，允许我们在此基础上灵活构建高度定制化的周边功能（如复杂时间判断、状态管理），而不是被一个庞大而固定的BRMS所束缚。
  - **动态规则构建:** 支持通过Java代码动态创建和注册规则，完美契合从业务服务端接收并应用规则的需求。
- **围绕Easy Rules的自定义Java组件 (时间条件评估、状态管理等):**
  - **必要性:** Easy Rules本身不提供复杂的CEP（复杂事件处理）、高级时间逻辑或内置的状态管理机制。而您的需求中，“无人15分钟”、个性化日历下的复杂时间判断等，都属于这类范畴。
  - **可控性与定制性:** 用Java自行实现这些组件，可以精确匹配业务需求，提供最大的灵活性和控制力。
- **SQLite (本地存储):**
  - **嵌入式、无服务器:** 非常适合边缘应用，无需单独部署和管理数据库服务进程。
  - **资源占用小:** 对CPU、内存占用极低。
  - **事务支持 (ACID):** 保证规则、配置等关键数据在本地存储时的完整性和一致性。
  - **单文件存储:** 管理和备份简单。
  - **满足离线需求:** 确保边缘引擎在与业务服务端断开连接时，仍能基于本地规则运行。
  - **性能:** 对于单个应用进程的本地读写，性能完全满足存储几千条规则定义和少量设备信息的需求。
- **MyBatis (数据持久层):**
  - **SQL控制力:** 允许开发人员完全掌控SQL语句，可以针对SQLite的特性进行优化，并灵活处理数据映射。
  - **与Java良好集成:** 方便地将数据库记录映射到Java对象（POJO）。
  - **轻量级:** 相比JPA/Hibernate等全功能ORM，MyBatis更为轻量，配置和使用相对简单。
  - **支持SQLite:** 通过SQLite的JDBC驱动，MyBatis可以无缝操作SQLite数据库。

**III. 更细化的技术方案 (侧重边缘规则引擎)**

我们将边缘规则引擎设计为一个基于Spring Boot的Java应用程序，内部模块化（包结构）。

**1. 项目总体包结构 (单一工程内):**

```
com.inxaiot.ruleengine
├── app                   // Spring Boot主应用、全局配置、启动类
├── storage               // 数据持久化层 (SQLite + MyBatis)
│   ├── entity            // 数据库实体/POJO (对应规则定义、全局日历、设备信息等)
│   ├── mapper            // MyBatis Mapper接口
│   └── service           // 数据访问服务 (封装Repository/Mapper逻辑)
├── core                  // 规则引擎核心逻辑
│   ├── definition        // 边缘规则定义模型 (RuleDefinition POJO)
│   ├── adapter           // 将RuleDefinition适配为Easy Rules的Rule对象
│   ├── engine            // 封装Easy Rules引擎实例及执行逻辑
│   └── action            // 规则动作的执行器
├── scheduler             // 时间条件处理与相关调度
│   ├── model             // 时间条件相关的模型 (如TimeConditions POJO的细化)
│   └── service           // TimeConditionEvaluator, GlobalCalendarService
├── contextstate          // 复杂状态与上下文管理
│   ├── model             // 状态对象模型 (如OccupancyState)
│   └── service           // DeviceStateManager, SystemContextService
├── transport             // 外部通信与数据转换 (如MQTT/HTTP)
│   ├── listener          // 消息监听器
│   └── publisher         // 消息/指令发布器
└── api                   // 对业务服务端提供的REST API接口
    └── controller        // Spring MVC Controllers
```

**2. 线程模型与异步处理设计:**

### 2.1 异步处理架构

规则引擎采用分层异步处理架构，确保高性能和响应性：

```
事件源 (MQTT/定时器) → 快速返回
    ↓ [异步边界1: 规则评估线程池]
规则条件判断 (CPU密集型)
    ↓ [异步边界2: 动作执行线程池]
动作执行 (IO密集型: 设备控制/API调用)
```

### 2.2 线程池配置

```java
@Configuration
public class RuleEngineConfig {

    /**
     * 规则评估线程池 - CPU密集型任务
     * 用于规则条件判断和规则引擎核心处理
     */
    @Bean("ruleEvaluationExecutor")
    public ThreadPoolTaskExecutor ruleEvaluationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(200);
        executor.setThreadNamePrefix("RuleEval-");
        return executor;
    }

    /**
     * 动作执行线程池 - IO密集型任务
     * 用于执行规则动作（设备控制、API调用、消息发送等）
     */
    @Bean("actionExecutionExecutor")
    public ThreadPoolTaskExecutor actionExecutionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("ActionExec-");
        return executor;
    }

    /**
     * 设备状态管理定时任务执行器
     */
    @Bean("deviceStateScheduler")
    public ScheduledExecutorService deviceStateScheduler() {
        return new ScheduledThreadPoolExecutor(2, r -> {
            Thread thread = new Thread(r);
            thread.setName("DeviceState-Scheduler-" + thread.getId());
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 时间触发器定时任务执行器（预留）
     */
    @Bean("timeTriggerScheduler")
    public ScheduledExecutorService timeTriggerScheduler() {
        return new ScheduledThreadPoolExecutor(1, r -> {
            Thread thread = new Thread(r);
            thread.setName("TimeTrigger-Scheduler-" + thread.getId());
            thread.setDaemon(true);
            return thread;
        });
    }
}
```

### 2.3 异步处理流程

1. **事件接收阶段**：MQTT消息监听器快速接收事件，立即返回
2. **规则评估阶段**：异步提交到规则评估线程池，进行条件判断
3. **动作执行阶段**：异步提交到动作执行线程池，执行具体动作

**3. 核心组件细化设计:**

**3.1. `com.inxaiot.ruleengine.core.definition.RuleDefinition` POJO**

这是从业务服务端接收并存储在本地的原子化规则的核心数据结构。

Java

```
package com.inxaiot.ruleengine.core.definition;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

public class RuleDefinition {
    private String ruleId;          // 规则唯一ID
    private String ruleName;        // 规则名称 (可选, 便于理解)
    private String targetDeviceId;  // 主要目标设备ID (如果规则针对单个设备)
    private String targetDeviceType; // (可选) 用于筛选或特定逻辑
    private String bizId            // 规则所属外部业务唯一标识符
    private String groupId          // 规则分组ID，方便业务批量处理规则
    private int priority = 1;       // 规则优先级 (Easy Rules支持)
    private boolean enabled = true; // 规则是否启用

    private List<TimeCondition> timeConditions;         // 时间条件
    private TriggerConditions triggerCondition;   // 设备触发条件
    private List<ActionDefinition> actions;        // 动作列表

    // Getters and Setters
    // ...
}

// --- 时间条件 ---
class TimeCondition {
    private List<String> timeCronExpressions; // 例如: ["0 0 8-10 ? *", "0 0 14-16 ? *"]
    private List<String> workDays;            // 例如：Mon,Tue、Wed
    private String seanson                    // 如 summer、winter、all，由全局日历表定义起止时间
    private List<LocalDate> includeDates; // 个性化日历: 强制生效的日期列表
    private List<LocalDate> excludeDates; // 个性化日历: 强制不生效的日期列表

    // Getters and Setters
    // ...
}

// --- 设备触发条件 ---
class TriggerConditions {
    public enum MatchLogic { ALL, ANY } // AND, OR
    private MatchLogic logic = MatchLogic.ALL;
    private List<DeviceCondition> conditions;

    // Getters and Setters
    // ...
}

class DeviceCondition {
    private String sourceDeviceId;  // 条件数据来源设备ID (可能与targetDeviceId不同)
    private String pointId;         // 监控的设备点位ID (如 "illuminance", "occupancy_status")
    private String operator;        // 操作符 (如 "EQUALS", "GREATER_THAN", "LESS_THAN", "NOT_EQUALS", "UNOCCUPIED_FOR_MINUTES")
    private Object value;           // 比较值 (如 "UNOCCUPIED", 500, true)
    private long durationMinutes;   // 持续时间 (用于 "UNOCCUPIED_FOR_MINUTES" 等操作符)

    // Getters and Setters
    // ...
}

// --- 动作定义 ---
class ActionDefinition {
    private String actionType;      // 动作类型 (如 "DEVICE_CONTROL", "SEND_MESSAGE", "CALL_API")
    private String targetDeviceId;  // 动作执行的目标设备ID (可能与规则的targetDeviceId相同或不同)
    private String pointIdToActOn;  // (用于DEVICE_CONTROL) 执行动作的点位
    private Object actionValue;     // (用于DEVICE_CONTROL) 设定的值
    private Map<String, Object> params; // (用于其他动作类型) 其他参数，如消息内容、API地址、空调详细设置等

    // Getters and Setters
    // ...
}

// --- 全局日志配置 --- 
```

**2.2. `com.inxaiot.ruleengine.storage` (SQLite + MyBatis)**

- **`entity`**: `RuleDefinitionEntity.java` (可以将`RuleDefinition`直接或间接映射到表，对于复杂内部对象如`TimeCondition`, `TriggerConditions`, `actions`可以序列化为JSON字符串存入TEXT类型的列，或设计关联表)。`GlobalCalendarEntity.java` (`LocalDate date`)。

- **`mapper`**:
  
  - `RuleDefinitionMapper.xml` / `RuleDefinitionMapper.java` (接口): 定义CRUD操作，如 `insertRule`, `updateRule`, `deleteRuleById`, `findRuleById`, `findAllEnabledRulesByTargetDevice` (或更通用的查询)。
  - `GlobalCalendarMapper.xml` / `GlobalCalendarMapper.java`: `insertCalendar`, `deleteCalendar`, `getCalendar`。

- **`service.RuleStorageService.java`**: 封装Mapper，提供业务友好的数据存取方法。

- **MyBatis配置 (`mybatis-config.xml`)**: 配置SQLite数据源、事务管理器、Mapper路径。
  
  XML
  
  ```
  <dataSource type="POOLED">
      <property name="driver" value="org.sqlite.JDBC"/>
      <property name="url" value="***************************"/> </dataSource>
  ```

- **依赖:** `org.mybatis:mybatis`, `org.mybatis:mybatis-spring` (如果用Spring集成), `org.xerial:sqlite-jdbc`。

**2.3. `com.inxaiot.ruleengine.scheduler.service.TimeConditionEvaluator.java`**

此类**按需**评估给定规则的时间条件是否满足，不主动轮询。

Java

```
package com.inxaiot.ruleengine.scheduler.service;

import com.inxaiot.ruleengine.core.definition.TimeConditions;
import com.inxaiot.ruleengine.storage.GlobalCalendarService; // 用于获取全局日历
import org.quartz.CronExpression; // 引入Quartz Core库进行Cron表达式解析
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Set;

@Service
public class TimeConditionEvaluator {
    private static final Logger logger = LoggerFactory.getLogger(TimeConditionEvaluator.class);
    private final GlobalConfigStorageService globalConfigStorageService;
    private GlobalCalendar globalCalendar;

    @Autowired
    public TimeConditionEvaluator(GlobalConfigStorageService globalConfigStorageService) {
        this.globalConfigStorageService = globalConfigStorageService;
        refreshGlobalCalendar(); // 初始化加载
    }

    public void refreshGlobalCalendar() {
        this.globalCalendar= globalConfigStorageService.loadGlobalCalendar(); // 从存储层加载
        logger.info("Global calendar refreshed, count: {}", globalCalendar.toJson();
    }

    public boolean isTimeConditionMet(List<TimeCondition> conditions, LocalDateTime evaluationTime) {
        if (conditions == null || conditions.isEmpty() {
            return true; // 没有时间条件，视为满足
        }
        LocalDate evaluationDate = evaluationTime.toLocalDate();

        for(TimeCondition condition:conditions)

        // 1. 检查强制包含的日期 (优先级最高)
        if (condition.getIncludeDates() != null && condition.getIncludeDates().contains(evaluationDate)) {
            return checkCronExpressions(condition.getTimeCronExpressions(), evaluationTime); // 仍需满足当天的时间段
        }

        // 2. 检查强制排除的日期 (优先级次高)
        if (condition.getExcludeDates() != null && condition.getExcludeDates().contains(evaluationDate)) {
            return false;
        }

        // 3. 检查季节性/特定日期范围,需要拿全局日历globalCalendar判断
        // TODO

        // 4. 检查工作日
        // TODO

        // 5. 检查Cron表达式定义的时间段
        if (!checkCronExpressions(conditions.getCronExpressions(), evaluationTime)) {
            return false;
        }
        return true; // 所有时间条件均满足
    }

    private boolean checkCronExpressions(List<String> cronExpressions, LocalDateTime evaluationTime) {
        if (cronExpressions == null || cronExpressions.isEmpty()) {
            return true; // 没有指定具体时间段，则视为全天满足（如果日期已满足）
        }
        Date dateToTest = Date.from(evaluationTime.atZone(ZoneId.systemDefault()).toInstant());
        for (String cronStr : cronExpressions) {
            try {
                CronExpression cron = new CronExpression(cronStr);
                if (cron.isSatisfiedBy(dateToTest)) {
                    return true; // 满足任意一个Cron表达式即可
                }
            } catch (ParseException e) {
                logger.error("Failed to parse cron expression: {}", cronStr, e);
                // 根据策略，可以选择忽略错误表达式或直接返回false
            }
        }
        return false; // 所有Cron表达式都不满足
    }
}
```

**2.4. `com.inxaiot.ruleengine.contextstate.service.DeviceStateManager.java`**

管理如“无人15分钟”这样的持续状态。

Java

```
package com.inxaiot.ruleengine.contextstate.service;

import com.inxaiot.ruleengine.core.engine.RuleEngineTriggerService; // 用于触发规则引擎
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.ScheduledExecutorService;

@Service
public class DeviceStateManager {
    private static final Logger logger = LoggerFactory.getLogger(DeviceStateManager.class);

    // K: deviceId, V: 状态对象
    private final Map<String, OccupancyState> occupancyStates = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler; // 从App配置中注入
    private final RuleEngineTriggerService ruleEngineTriggerService; // 用于触发规则评估

    private static class OccupancyState {
        boolean currentlyOccupied;
        long lastBecameUnoccupiedTimestamp;
        ScheduledFuture<?> timeoutTaskFuture; // 用于取消任务
        String associatedPointId; // 如触发此状态的点位ID
        long configuredDurationMinutes; // 如此状态配置的超时时长
    }

    @Autowired
    public DeviceStateManager(ScheduledExecutorService scheduler, RuleEngineTriggerService ruleEngineTriggerService) {
        this.scheduler = scheduler;
        this.ruleEngineTriggerService = ruleEngineTriggerService;
    }

    // 由MQTT监听器或其他数据源调用
    public void processDeviceSensorEvent(String deviceId, String pointId, String occupancyValue, long durationMinutes) {
        // 假设 "UNOCCUPIED" 表示无人, 其他表示有人，这里应该还要再抽像，因为不同设备点位的状态值是不是一样的
        boolean isNowOccupied = !"UNOCCUPIED".equalsIgnoreCase(occupancyValue);
        OccupancyState state = occupancyStates.computeIfAbsent(deviceId, k -> new OccupancyState());
        state.associatedPointId = pointId;
        state.configuredDurationMinutes = durationMinutes;

        if (isNowOccupied) {
            if (!state.currentlyOccupied) { // 如从无人变为有人
                logger.debug("Device {} became occupied. Cancelling timeout task if any.", deviceId);
                if (state.timeoutTaskFuture != null) {
                    state.timeoutTaskFuture.cancel(false);
                    state.timeoutTaskFuture = null;
                }
            }
            state.currentlyOccupied = true;
        } else { // 如变为无人
            if (state.currentlyOccupied || state.timeoutTaskFuture == null || state.timeoutTaskFuture.isDone()) {
                // 如从有人变为无人，或者之前没有超时任务/任务已结束，则启动新的超时任务
                logger.debug("Device {} became unoccupied. Scheduling timeout check in {} minutes.", deviceId, durationMinutes);
                state.lastBecameUnoccupiedTimestamp = System.currentTimeMillis();
                state.currentlyOccupied = false;

                if (state.timeoutTaskFuture != null) { // 取消旧的（如果存在且未执行完）
                    state.timeoutTaskFuture.cancel(false);
                }
                state.timeoutTaskFuture = scheduler.schedule(() -> {
                    handleUnoccupiedTimeout(deviceId);
                }, durationMinutes, TimeUnit.MINUTES);
            } else {
                // 已经是无人状态，并且已有等待中的超时任务，则不重复启动
                logger.debug("Device {} is already unoccupied with a pending timeout task. No action.", deviceId);
            }
        }
    }

    private void handleUnoccupiedTimeout(String deviceId) {
        OccupancyState state = occupancyStates.get(deviceId);
        if (state != null && !state.currentlyOccupied) {
            long elapsedMinutes = (System.currentTimeMillis() - state.lastBecameUnoccupiedTimestamp) / (1000 * 60);
            if (elapsedMinutes >= state.configuredDurationMinutes) {
                logger.info("Unoccupied timeout confirmed for device {}. Triggering rules.", deviceId);
                // 触发规则引擎，携带特定事实
                // 'facts' Map should indicate this is a timeout event for 'deviceId'
                // and for 'state.associatedPointId' with 'state.configuredDurationMinutes'
                ruleEngineTriggerService.triggerRulesForDeviceTimeout(deviceId, state.associatedPointId, state.configuredDurationMinutes);
            } else {
                 logger.warn("Unoccupied timeout task for {} executed, but elapsed time {}m is less than configured {}m. State might have changed or task misfired.", deviceId, elapsedMinutes, state.configuredDurationMinutes);
            }
        } else if (state != null && state.currentlyOccupied) {
             logger.info("Unoccupied timeout task for {} executed, but device is now occupied. No action.", deviceId);
        }
        // 清理任务引用，避免重入或内存泄漏
        if(state != null) state.timeoutTaskFuture = null;
    }
}
```

**2.5. `com.inxaiot.ruleengine.core.adapter.RuleAdapterService.java`**

Java

```
package com.inxaiot.ruleengine.core.adapter;

import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.core.definition.TriggerCondition;
import com.inxaiot.ruleengine.core.definition.DeviceCondition;
import com.inxaiot.ruleengine.core.action.ActionExecutorService;
import com.inxaiot.ruleengine.scheduler.service.TimeConditionEvaluator;
import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.core.RuleBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Objects;

@Service
public class RuleAdapterService {
    private static final Logger logger = LoggerFactory.getLogger(RuleAdapterService.class);

    private final TimeConditionEvaluator timeConditionEvaluator;
    private final ActionExecutorService actionExecutorService;

    @Autowired
    public RuleAdapterService(TimeConditionEvaluator timeConditionEvaluator, ActionExecutorService actionExecutorService) {
        this.timeConditionEvaluator = timeConditionEvaluator;
        this.actionExecutorService = actionExecutorService;
    }

    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
                .name(ruleDefinition.getRuleId())
                .description(ruleDefinition.getRuleName())
                .priority(ruleDefinition.getPriority())
                .when(facts -> {
                    if (!ruleDefinition.isEnabled()) {
                        return false;
                    }
                    // 1. 评估时间条件
                    boolean timeMet = timeConditionEvaluator.isTimeConditionMet(ruleDefinition.getTimeConditions(), LocalDateTime.now());
                    if (!timeMet) {
                        logger.trace("Rule {} time condition not met.", ruleDefinition.getRuleId());
                        return false;
                    }
                    logger.trace("Rule {} time condition met.", ruleDefinition.getRuleId());

                    // 2. 评估设备触发条件
                    boolean deviceConditionsMet = evaluateDeviceTriggerConditions(ruleDefinition, facts);
                    if (!deviceConditionsMet) {
                        logger.trace("Rule {} device conditions not met.", ruleDefinition.getRuleId());
                        return false;
                    }
                    logger.debug("Rule {} all conditions met.", ruleDefinition.getRuleId());
                    return true;
                })
                .then(facts -> {
                    logger.info("Executing actions for rule {}.", ruleDefinition.getRuleId());
                    ruleDefinition.getActions().forEach(actionDef -> {
                        String targetDeviceId = actionDef.getTargetDeviceId() != null ? actionDef.getTargetDeviceId() : ruleDefinition.getTargetDeviceId();
                        actionExecutorService.executeAction(targetDeviceId, actionDef, facts);
                    });
                })
                .build();
    }

    private boolean evaluateDeviceTriggerConditions(RuleDefinition ruleDefinition, Facts facts) {
        TriggerConditions triggerCondition = ruleDefinition.getTriggerConditions();
        if (triggerCondition == null || triggerCondition.getConditions() == null || triggerCondition.getConditions().isEmpty()) {
            return true; // 没有设备条件，视为满足
        }

        // 获取触发此规则评估的源头设备ID (如果存在)
        // String triggeringDeviceId = facts.get("triggeringDeviceId"); // 约定一个事实名

        boolean overallResult;
        if (triggerCondition.getLogic() == TriggerConditions.MatchLogic.ANY) {
            overallResult = false; // 对于OR逻辑，默认false，只要有一个满足即为true
            for (DeviceCondition condition : triggerCondition.getConditions()) {
                // String sourceDeviceId = condition.getSourceDeviceId() != null ? condition.getSourceDeviceId() : ruleDefinition.getTargetDeviceId();
                // if (triggeringDeviceId != null && !triggeringDeviceId.equals(sourceDeviceId)) {
                // continue; // 如果条件与触发设备不符，则跳过 (此逻辑需根据实际情况调整)
                // }
                if (evaluateSingleCondition(condition, facts)) {
                    overallResult = true;
                    break;
                }
            }
        } else { // ALL (AND) logic
            overallResult = true; // 对于AND逻辑，默认true，只要有一个不满足即为false
            for (DeviceCondition condition : triggerCondition.getConditions()) {
                // String sourceDeviceId = condition.getSourceDeviceId() != null ? condition.getSourceDeviceId() : ruleDefinition.getTargetDeviceId();
                // if (triggeringDeviceId != null && !triggeringDeviceId.equals(sourceDeviceId)) {
                // continue; // (同上，此逻辑需根据实际情况调整)
                // }
                if (!evaluateSingleCondition(condition, facts)) {
                    overallResult = false;
                    break;
                }
            }
        }
        return overallResult;
    }

    private boolean evaluateSingleCondition(DeviceCondition condition, Facts facts) {
        // 对于 "UNOCCUPIED_FOR_MINUTES"，它不是直接比较传感器值，而是检查是否存在由DeviceStateManager生成的特定超时事实
        if ("UNOCCUPIED_FOR_MINUTES".equalsIgnoreCase(condition.getOperator())) {
            String factName = "UnoccupiedTimeoutFact_" + condition.getSourceDeviceId() + "_" + condition.getPointId(); // 约定一个事实名称
            Boolean timeoutOccurred = facts.get(factName); // DeviceStateManager在触发时放入
            return Boolean.TRUE.equals(timeoutOccurred) && condition.getDurationMinutes() > 0; // 简单示例
        }

        // 对于其他常规传感器值比较
        Object factValue = facts.get(condition.getPointId()); // 从Facts中获取对应点位的值
                                                            // 注意：facts中的key可能需要包含deviceId，如 "deviceId_pointId"
                                                            // 或者facts本身就是针对单个设备的快照

        if (factValue == null) {
            logger.warn("Fact for pointId '{}' not found for condition in rule.", condition.getPointId());
            return false; // 点位数据不存在，条件不满足
        }

        // 实现各种操作符的比较逻辑 (EQUALS, GREATER_THAN, LESS_THAN, etc.)
        // 需要注意类型转换 (factValue 和 condition.getValue() 的类型)
        // 例如:
        try {
            switch (condition.getOperator().toUpperCase()) {
                case "EQUALS":
                    return Objects.equals(String.valueOf(factValue), String.valueOf(condition.getValue()));
                case "NOT_EQUALS":
                    return !Objects.equals(String.valueOf(factValue), String.valueOf(condition.getValue()));
                case "GREATER_THAN": {
                    double factNum = Double.parseDouble(String.valueOf(factValue));
                    double condNum = Double.parseDouble(String.valueOf(condition.getValue()));
                    return factNum > condNum;
                }
                case "LESS_THAN": {
                    double factNum = Double.parseDouble(String.valueOf(factValue));
                    double condNum = Double.parseDouble(String.valueOf(condition.getValue()));
                    return factNum < condNum;
                }
                // ... 其他操作符
                default:
                    logger.warn("Unsupported operator: {}", condition.getOperator());
                    return false;
            }
        } catch (NumberFormatException e) {
            logger.error("Error comparing condition for pointId '{}': factValue '{}' or condition.value '{}' not a valid number.",
                         condition.getPointId(), factValue, condition.getValue(), e);
            return false;
        } catch (Exception e) {
            logger.error("Error evaluating condition for pointId '{}': {}", condition.getPointId(), e.getMessage(), e);
            return false;
        }
    }
}
```

**2.6. `com.inxaiot.ruleengine.core.engine.RuleEngineTriggerService.java`**

此类负责编排规则的加载和执行。

Java

```
package com.inxaiot.ruleengine.core.engine;

import com.inxaiot.ruleengine.core.adapter.RuleAdapterService;
import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.storage.RuleService; // 用于加载规则定义
import com.inxaiot.ruleengine.contextstate.service.SystemContextService; // 用于获取系统上下文

import org.jeasy.rules.api.Facts;
import org.jeasy.rules.api.Rule;
import org.jeasy.rules.api.Rules;
import org.jeasy.rules.core.DefaultRulesEngine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RuleEngineTriggerService {
    private static final Logger logger = LoggerFactory.getLogger(RuleEngineTriggerService.class);

    private final DefaultRulesEngine easyRulesEngine;
    private final RuleStorageService ruleStorageService;
    private final RuleAdapterService ruleAdapterService;
    private final SystemContextService systemContextService;


    @Autowired
    public RuleEngineTriggerService(RuleStorageService ruleStorageService, RuleAdapterService ruleAdapterService, SystemContextService systemContextService) {
        this.easyRulesEngine = new DefaultRulesEngine();
        // this.easyRulesEngine.setSkipOnFirstAppliedRule(false); // 根据需要设置是否触发所有适用规则
        // this.easyRulesEngine.setRulePriorityThreshold(Integer.MAX_VALUE); // 根据需要设置优先级阈值

        this.ruleStorageService = ruleStorageService;
        this.ruleAdapterService = ruleAdapterService;
        this.systemContextService = systemContextService;
    }

    // 由MQTT监听器或DeviceStateManager调用，当有设备数据或特定状态事件发生时
    public void processDeviceEvent(String deviceId, String pointId, Object value) {
        Facts facts = new Facts();
        facts.put("triggeringDeviceId", deviceId); // 标记触发事件的源设备
        facts.put(pointId, value);                 // 传感器点位数据
        // 也可以约定一个更通用的事实结构，如 facts.put("event", new DeviceEvent(deviceId, pointId, value));

        addGlobalContextToFacts(facts);
        triggerRulesWithFacts(facts, deviceId);
    }

    // 由DeviceStateManager在确认无人超时后调用
    public void triggerRulesForDeviceTimeout(String deviceId, String sensorPointId, long durationMinutes) {
        Facts facts = new Facts();
        facts.put("triggeringDeviceId", deviceId);
        // 约定一个事实名称，与RuleAdapterService.evaluateSingleCondition中对应
        String timeoutFactName = "UnoccupiedTimeoutFact_" + deviceId + "_" + sensorPointId;
        facts.put(timeoutFactName, true);
        facts.put("timeoutDurationMinutes", durationMinutes); // 附加信息

        addGlobalContextToFacts(facts);
        triggerRulesWithFacts(facts, deviceId); // 仍然可以基于deviceId筛选相关规则
    }

    // (可选) 如果有纯粹基于时间的场景规则，由时间调度服务调用
    public void triggerTimeBasedScene(String sceneIdOrRuleGroupId) {
        Facts facts = new Facts();
        facts.put("timeTriggeredSceneId", sceneIdOrRuleGroupId);
        addGlobalContextToFacts(facts);

        // 加载与此场景/规则组相关的所有规则定义
        List<RuleDefinition> definitions = ruleStorageService.findRulesByBizId(bizId);
        if (definitions.isEmpty()) {
            logger.debug("No rules found for time-triggered bizId: {}", bizId);
            return;
        }
        Rules easyRules = new Rules();
        definitions.stream()
            .filter(RuleDefinition::isEnabled) // 只处理启用的规则
            .map(ruleAdapterService::adapt)
            .forEach(easyRules::register);

        if (easyRules.isEmpty()) {
            logger.debug("No enabled rules adapted for time-triggered bizId: {}", bizId);
            return;
        }
        logger.info("Firing rules for time-triggered bizId: {}", bizId);
        easyRulesEngine.fire(easyRules, facts);
    }


    private void triggerRulesWithFacts(Facts facts, String relevantDeviceId) {
        // 1. 从存储中加载可能与此设备相关的已启用的规则定义
        //    这里的查询逻辑可以优化，例如，规则定义中包含它关心的 sourceDeviceId 或 targetDeviceId
        List<RuleDefinition> relevantDefinitions = ruleStorageService.findAllEnabledRulesRelevantToDevice(relevantDeviceId);

        if (relevantDefinitions.isEmpty()) {
            logger.trace("No relevant enabled rules found for deviceId: {} and facts: {}", relevantDeviceId, facts);
            return;
        }

        // 2. 将这些定义适配成Easy Rules对象
        Rules easyRules = new Rules();
        relevantDefinitions.stream()
                .map(ruleAdapterService::adapt) // adapt方法内部会处理规则的enabled状态
                .forEach(easyRules::register);

        if(easyRules.isEmpty()){
            logger.trace("No rules adapted or all are disabled for deviceId: {}", relevantDeviceId);
            return;
        }

        // 3. 执行规则
        logger.debug("Firing rules for deviceId: {} with facts: {}", relevantDeviceId, facts);
        easyRulesEngine.fire(easyRules, facts);
    }

    private void addGlobalContextToFacts(Facts facts){
        // 从SystemContextService获取全局上下文并添加到Facts
        // facts.put("buildingMode", systemContextService.getCurrentBuildingMode());
        // facts.put("currentSeason", systemContextService.getCurrentSeason());
    }

    // 暴露方法，供API调用以刷新规则缓存 (如果RuleStorageService有缓存机制)
    public void refreshRulesCache() {
        ruleStorageService.reloadRules(); // 假设RuleStorageService有此方法
        logger.info("Rule cache refreshed on demand.");
    }
}
```

**2.7. `com.inxaiot.ruleengine.transport.listener.MqttMessageListener.java` (示例)**

Java

```
package com.inxaiot.ruleengine.transport.listener;

import com.inxaiot.ruleengine.contextstate.service.DeviceStateManager;
import com.inxaiot.ruleengine.core.engine.RuleEngineTriggerService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
// 假设使用某种MQTT客户端库，例如Paho或Spring Integration MQTT

@Component
public class MqttMessageListener /* implements MessageListener (取决于MQTT库) */ {
    private static final Logger logger = LoggerFactory.getLogger(MqttMessageListener.class);

    private final RuleEngineTriggerService ruleEngineTriggerService;
    private final DeviceStateManager deviceStateManager;
    // private final ObjectMapper objectMapper; // 用于解析JSON payload

    @Autowired
    public MqttMessageListener(RuleEngineTriggerService ruleEngineTriggerService, DeviceStateManager deviceStateManager /*, ObjectMapper objectMapper*/) {
        this.ruleEngineTriggerService = ruleEngineTriggerService;
        this.deviceStateManager = deviceStateManager;
        // this.objectMapper = objectMapper;
        // 在这里配置MQTT客户端、订阅主题等
    }

    // 消息处理回调方法 (具体签名取决于MQTT库)
    public void handleMessage(String topic, String payload) {
        logger.debug("Received MQTT message. Topic: {}, Payload: {}", topic, payload);
        try {
            // 1. 解析Topic，提取deviceId, pointId等信息
            // String deviceId = parseDeviceIdFromTopic(topic);
            // String pointId = parsePointIdFromTopic(topic); (或从payload中解析)

            // 2. 解析Payload，获取上报的值
            // DeviceDataPojo data = objectMapper.readValue(payload, DeviceDataPojo.class);
            // Object value = data.getValue();
            String deviceId = "sampleDevice1"; // 示例
            String pointId = "occupancy_status"; // 示例
            String value = "UNOCCUPIED"; // 示例, 假设这是传感器的上报值

            // 3. 特殊处理：如果是占用传感器，且规则中定义了“无人XX分钟”的逻辑
            //    需要从规则定义中获取该传感器对应的超时时长
            //    这里简化，假设durationMinutes是已知的或与pointId关联的配置
            if ("occupancy_status".equals(pointId)) {
                long durationForTimeout = 15; // 应从配置或规则定义中获取
                deviceStateManager.processDeviceSensorEvent(deviceId, pointId, value, durationForTimeout);
            } else {
                // 4. 对于其他普通传感器事件，直接触发规则评估
                ruleEngineTriggerService.processDeviceEvent(deviceId, pointId, value);
            }

        } catch (Exception e) {
            logger.error("Error processing MQTT message. Topic: {}, Payload: {}", topic, payload, e);
        }
    }
}
```

**2.8. API接口 (`com.inxaiot.ruleengine.api.controller`)**

使用Spring MVC (@RestController, @PostMapping, @GetMapping等) 实现之前讨论的API端点。

例如，RuleAdminController.java:

Java

```
package com.inxaiot.ruleengine.api.controller;

import com.inxaiot.ruleengine.core.definition.RuleDefinition;
import com.inxaiot.ruleengine.storage.RuleService;
import com.inxaiot.ruleengine.core.engine.RuleEngineTriggerService; // 用于刷新规则
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;

@RestController
@RequestMapping("/api/engine/rules")
public class RuleAdminController {

    private final RuleStorageService ruleStorageService;
    private final RuleEngineTriggerService ruleEngineTriggerService;


    @Autowired
    public RuleAdminController(RuleStorageService ruleStorageService, RuleEngineTriggerService ruleEngineTriggerService) {
        this.ruleStorageService = ruleStorageService;
        this.ruleEngineTriggerService = ruleEngineTriggerService;
    }

    @PostMapping
    public ResponseEntity<String> addOrUpdateRule(@RequestBody RuleDefinition ruleDefinition) {
        try {
            ruleStorageService.saveRule(ruleDefinition);
            ruleEngineTriggerService.refreshRulesCache(); // 通知引擎刷新 (如果storage有缓存)
            return ResponseEntity.ok("Rule saved successfully: " + ruleDefinition.getRuleId());
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Error saving rule: " + e.getMessage());
        }
    }

    @GetMapping("/{ruleId}")
    public ResponseEntity<RuleDefinition> getRule(@PathVariable String ruleId) {
        RuleDefinition rule = ruleStorageService.findRuleById(ruleId);
        if (rule != null) {
            return ResponseEntity.ok(rule);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping
    public ResponseEntity<List<RuleDefinition>> getAllRules() {
        return ResponseEntity.ok(ruleStorageService.findAllRules());
    }

    @DeleteMapping("/{ruleId}")
    public ResponseEntity<String> deleteRule(@PathVariable String ruleId) {
         try {
            ruleStorageService.deleteRuleById(ruleId);
            ruleEngineTriggerService.refreshRulesCache();
            return ResponseEntity.ok("Rule deleted successfully: " + ruleId);
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Error deleting rule: " + e.getMessage());
        }
    }
    // ... 其他API，如按groupId/bizId操作rule、查询日志、更新全局配置 (全局日历) 等
    
}
```

**3. 关于AI编写代码的提示：**

- **明确的POJO结构：** 上述 `RuleDefinition` 及其内部类的结构非常关键，AI需要基于此来理解规则的构成。
- **接口与实现分离：** 定义清晰的服务接口（如`RuleStorageService`, `ActionExecutorService`），然后让AI针对接口和上述逻辑描述来实现。
- **方法职责单一：** 确保每个方法的职责尽量单一，便于AI理解和生成代码。
- **错误处理与日志：** 在需求中明确指出需要日志记录和基本的错误处理。
- **逐步生成与测试：** 可以先让AI生成骨架代码和POJO，然后逐步填充核心逻辑，每个部分完成后进行测试。
- **依赖管理：** 明确告知AI需要使用的库（Easy Rules, Quartz Core, MyBatis, SQLite JDBC, Spring Boot starters）。
- **配置：** 提醒AI哪些部分是可配置的（如数据库路径、MQTT服务器地址、调度周期）。
- **`evaluateSingleCondition`是核心难点：** `RuleAdapterService`中的`evaluateSingleCondition`方法，需要AI根据`DeviceCondition`中的`operator`和`value`，结合`Facts`中的实际数据，进行准确的比较。这里需要仔细设计各种操作符的实现逻辑和类型转换。
- **`TimeConditionEvaluator`的Cron和日期逻辑：** 确保AI使用正确的库（如Quartz）并正确处理`LocalDate`, `LocalDateTime`以及与`java.util.Date` (如果Cron库需要)的转换。

此细化方案提供了更具体的类和方法层面的设计思路，希望能更好地指导后续的AI代码生成，并减少偏离设计目标的可能性。

我已回顾了所有对话上下文及附件文档。上述方案已尽可能细化，目前没有立刻需要向您确认的不确定点。后续在AI生成代码的具体实现细节中，可能会遇到需要进一步明确的场景（例如，evaluateSingleCondition中操作符的具体行为、Facts中数据的确切命名约定等），但大的框架和组件职责已经比较清晰。
