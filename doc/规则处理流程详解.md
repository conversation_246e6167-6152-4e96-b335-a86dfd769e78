# 规则引擎处理流程详解

## 项目概述

本项目是基于Easy Rules 4.1.0的物联网规则引擎，核心处理流程为：**事实状态 → 规则评估 → 动作执行**。系统采用异步处理架构，规则评估和动作执行使用独立线程池，确保事件源快速响应。

## 核心架构图

```mermaid
graph TB
    subgraph "数据源层"
        MQTT[MQTT设备数据]
        Timer[时间触发器]
        API[API接口]
    end
    
    subgraph "事件处理层"
        ML[MqttListener]
        TSS[TimeSchedulerService]
        SM[StateManager]
        DEP[DeviceEventPublisher]
    end
    
    subgraph "规则引擎核心"
        RES[RuleEngineService]
        RAS[RuleAdapterService]
        FB[FactsBuilder]
        DA[DependencyAnalyzer]
        TCE[TimeConditionEvaluator]
    end
    
    subgraph "执行层"
        AE[ActionExecutor]
        DC[设备控制]
        MS[消息发送]
        AC[API调用]
    end
    
    subgraph "存储层"
        RS[RuleService]
        GCS[GlobalCalendarService]
        DB[(SQLite)]
    end
    
    MQTT --> ML
    Timer --> TSS
    API --> RES
    
    ML --> SM
    ML --> RES
    TSS --> RES
    SM --> DEP
    DEP --> RES
    
    RES --> FB
    FB --> DA
    RES --> RAS
    RAS --> TCE
    RAS --> AE
    
    AE --> DC
    AE --> MS
    AE --> AC
    
    RAS --> RS
    TCE --> GCS
    RS --> DB
    GCS --> DB
```

## 1. 事件驱动规则处理流程

### 1.1 单点触发处理

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant ML as MqttListener
    participant SM as StateManager
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant FB as FactsBuilder
    participant DA as DependencyAnalyzer
    participant RAS as RuleAdapterService
    participant TCE as TimeConditionEvaluator
    participant AE as ActionExecutor

    Device->>ML: 上报传感器数据
    Note over ML: 解析Topic和Payload<br/>提取deviceId, pointId, value

    ML->>SM: processDevicePointUpdate()
    Note over SM: 检查跨天处理<br/>更新设备点位状态<br/>记录状态变化

    SM->>SM: checkStateConditions()
    Note over SM: 检查相关的状态条件监控

    SM->>DEP: publishDeviceStateChange()
    Note over DEP: 发布设备状态变化事件<br/>解耦StateManager和RuleEngineService

    DEP->>RES: handleStateChangeEvent() [@EventListener]
    Note over RES: 异步处理事件<br/>使用@Async("ruleEvaluationExecutor")

    activate RES
    RES->>RES: findRulesRelatedToDevice()
    Note over RES: 根据deviceId查找<br/>EVENT_DRIVEN类型的规则

    loop 每个相关规则
        RES->>FB: buildCompleteFactsForRule()
        FB->>DA: extractRequiredDevicePoints()
        Note over DA: 分析规则依赖的所有设备点位
        FB->>SM: 批量获取设备状态
        FB-->>RES: 完整Facts对象

        RES->>RAS: adapt(RuleDefinition)
        Note over RAS: 将规则定义转换为<br/>Easy Rules的Rule对象

        RAS->>TCE: isTimeConditionMet()
        Note over TCE: 检查时间守卫条件<br/>Cron表达式、工作日、季节等
        TCE-->>RAS: true/false

        Note over RAS: 评估设备触发条件<br/>操作符比较、AND/OR逻辑

        alt 规则条件满足
            RAS->>AE: executeAction()
            Note over AE: 异步执行动作<br/>设备控制/消息发送/API调用
            AE-->>Device: 控制指令
        end
    end
    deactivate RES
```

### 1.2 持续时长触发处理

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant ML as MqttListener
    participant SM as StateManager
    participant SCM as StateConditionMonitor
    participant Scheduler as 定时调度器
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant AE as ActionExecutor

    Device->>ML: 上报状态变化<br/>(如：temperature=32)
    ML->>SM: processDevicePointUpdate()

    SM->>SM: checkAndHandleDateChange()
    Note over SM: 检查跨天处理<br/>如果跨天则清理设备状态和监控器

    alt 日期变化
        SM->>SM: 重置设备状态日计数器
        SM->>SCM: cancel() 所有监控器
        SM->>SM: 清空stateConditionMonitors
        Note over SM: 跨天清理完成
    end

    Note over SM: 检测到状态变化<br/>检查相关StateCondition
    SM->>SCM: checkCondition()
    Note over SCM: 评估条件匹配<br/>(如：温度>30度)

    alt 条件匹配且无活跃监控
        SCM->>Scheduler: 启动持续时间定时器
        Note over SCM: 记录条件开始满足的时间<br/>(如：持续10分钟)
    end

    alt 持续时间内状态变化
        Device->>ML: 新状态数据<br/>(如：temperature=28)
        ML->>SM: processDevicePointUpdate()
        SM->>SCM: checkCondition()
        SCM->>Scheduler: 取消定时器
        Note over SCM: 条件不再满足，清除监控
    else 持续时间到期
        Scheduler->>SCM: 定时器触发
        SCM->>SM: handleConditionTimeout()
        SM->>DEP: publishDeviceTimeout()
        DEP->>RES: handleStateChangeEvent(CONDITION_TIMEOUT)
        Note over RES: 处理持续条件满足事件
        RES->>AE: executeAction()
        AE-->>Device: 执行动作<br/>(如：开启空调)
    end
```

## 2. 时间驱动规则处理流程

### 2.1 时间点触发处理（POINT模式）

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TSS as TimeSchedulerService
    participant TCE as TimeConditionEvaluator
    participant RES as RuleEngineService
    participant RAS as RuleAdapterService
    participant AE as ActionExecutor
    participant Device as 目标设备

    Timer->>TSS: 每分钟执行检查
    Note over TSS: checkForTimeTriggers()

    TSS->>TSS: checkAndHandleDateChange()
    Note over TSS: 检查跨天处理

    alt 日期变化
        TSS->>TSS: 清空previousTimeConditionState
        Note over TSS: 清理所有时间条件状态<br/>避免跨天影响
    end

    TSS->>TSS: 查询TIME_DRIVEN规则
    Note over TSS: 过滤出时间驱动类型的规则

    loop 遍历每个时间驱动规则
        TSS->>TSS: inferTriggerMode()
        Note over TSS: 根据Cron表达式推断<br/>POINT模式 vs RANGE模式

        TSS->>TCE: isTimeConditionMet()
        Note over TCE: 评估复杂时间条件<br/>Cron、工作日、季节、个性化日历
        TCE-->>TSS: true/false

        alt 推断为POINT模式 && 时间条件满足
            Note over TSS: 无状态处理<br/>满足条件即触发
            TSS->>RES: triggerRuleActivation(ruleId)

            Note over RES: 创建时间触发Facts
            RES->>RES: 构建时间触发事实
            Note over RES: timeTriggerEvent=true<br/>triggeredRuleId=ruleId

            RES->>RAS: adapt(RuleDefinition)
            Note over RAS: 时间驱动规则的条件判断<br/>只检查时间触发事实

            RAS->>AE: executeAction()
            AE-->>Device: 执行动作
        end
    end
```

### 2.2 时间段触发处理（RANGE模式）

```mermaid
sequenceDiagram
    participant Timer as 系统定时器
    participant TSS as TimeSchedulerService
    participant TCE as TimeConditionEvaluator
    participant RES as RuleEngineService
    participant AE as ActionExecutor
    participant Device as 目标设备

    Note over TSS: 维护规则状态映射<br/>previousTimeConditionState

    Timer->>TSS: 每分钟执行检查

    TSS->>TSS: checkAndHandleDateChange()
    Note over TSS: 检查跨天处理

    alt 日期变化
        TSS->>TSS: 清空previousTimeConditionState
        Note over TSS: 清理所有时间条件状态<br/>避免跨天影响
    end

    TSS->>TSS: 查询TIME_DRIVEN规则

    loop 遍历每个时间驱动规则
        TSS->>TSS: inferTriggerMode()
        Note over TSS: 根据Cron表达式推断为RANGE模式<br/>(包含时间范围表达式)

        TSS->>TCE: isTimeConditionMet()
        TCE-->>TSS: isNowActive

        TSS->>TSS: processRangeMode(ruleId, isNowActive)
        TSS->>TSS: 获取上次状态
        Note over TSS: wasPreviouslyActive = <br/>previousTimeConditionState.get(ruleId)

        alt 首次检查 (wasPreviouslyActive == null)
            TSS->>TSS: 记录当前状态
            Note over TSS: previousTimeConditionState.put(ruleId, isNowActive)
            alt 当前满足条件
                TSS->>RES: triggerRuleActivation(ruleId)
                Note over TSS: 首次检查且当前活跃，触发激活
            end

        else 进入时间段 (isNowActive=true && wasPreviouslyActive=false)
            Note over TSS: 时间段开始，触发激活
            TSS->>RES: triggerRuleActivation(ruleId)
            RES->>AE: executeAction()
            Note over AE: 执行激活动作<br/>(actions字段)
            AE-->>Device: 激活设备
            TSS->>TSS: 更新状态为true

        else 离开时间段 (isNowActive=false && wasPreviouslyActive=true)
            Note over TSS: 时间段结束，触发失活
            TSS->>RES: triggerRuleDeactivation(ruleId)
            Note over RES: 直接执行失活动作<br/>不经过Easy Rules条件判断
            RES->>AE: executeAction()
            Note over AE: 执行失活动作<br/>(deactivationActions字段)
            AE-->>Device: 失活设备
            TSS->>TSS: 更新状态为false

        else 状态无变化
            Note over TSS: 状态保持不变<br/>无需触发
        end
    end
```

## 3. 核心组件详细实现

### 3.1 RuleEngineService - 规则引擎服务

#### 核心方法实现
```java
@Service
public class RuleEngineServiceImpl implements RuleEngineService {
    
    /**
     * 处理设备事件 - 异步处理入口
     */
    @Override
    public void processDeviceEvent(String deviceId, String pointId, Object value) {
        // 异步提交规则评估任务，立即返回，不阻塞事件源
        ruleEvaluationExecutor.execute(() -> {
            processDeviceEventInternal(deviceId, pointId, value);
        });
    }
    
    /**
     * 监听设备状态变化事件 - 事件驱动架构
     */
    @EventListener
    @Async("ruleEvaluationExecutor")
    public void handleStateChangeEvent(StateChangeEvent event) {
        // 根据事件类型分发处理
        switch (event.getEventType()) {
            case VALUE_CHANGED:
            case STATE_UPDATED:
                // 处理设备状态变化事件
                processDeviceEvent(event.getDeviceId(), event.getPointId(), event.getNewValue());
                break;
            case CONDITION_TIMEOUT:
                // 处理设备超时事件
                triggerRulesForDeviceTimeout(event.getDeviceId(), event.getPointId(),
                                           event.getDurationMinutes() != null ? event.getDurationMinutes() : 0L);
                break;
            case CONDITION_MET:
            case CONDITION_RESET:
                // 其他事件类型的处理
                logger.debug("Received condition event: {} for {}.{}",
                           event.getEventType(), event.getDeviceId(), event.getPointId());
                break;
        }
    }
    
    /**
     * 时间触发规则激活
     */
    @Override
    public void triggerRuleActivation(String ruleId) {
        // 创建时间触发Facts
        Facts facts = new Facts();
        facts.put("timeTriggerEvent", true);
        facts.put("triggeredRuleId", ruleId);
        facts.put("eventType", RuleDefinition.TriggerType.TIME_DRIVEN.toString());
        
        // 执行规则
        evaluateSingleRuleById(ruleId, facts);
    }
}
```

### 3.2 RuleAdapterService - 规则适配器

#### 核心适配逻辑
```java
@Service
public class RuleAdapterService {

    /**
     * 将RuleDefinition适配为Easy Rules的Rule对象
     */
    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
                .name(ruleDefinition.getRuleId())
                .description(ruleDefinition.getRuleName())
                .priority(ruleDefinition.getPriority())
                .when(facts -> {
                    if (!ruleDefinition.isEnabled()) {
                        return false;
                    }

                    // 根据规则的触发类型，应用不同的条件判断逻辑
                    if (ruleDefinition.getTriggerType() == RuleDefinition.TriggerType.TIME_DRIVEN) {
                        // 时间驱动规则：检查时间触发事实
                        boolean isTimeTrigger = Boolean.TRUE.equals(facts.get("timeTriggerEvent"))
                                             && ruleDefinition.getRuleId().equals(facts.get("triggeredRuleId"));
                        return isTimeTrigger;
                    } else {
                        // 事件驱动规则：检查时间守卫条件和设备触发条件

                        // 1. 评估时间条件（作为守卫条件）
                        boolean timeMet = timeConditionEvaluator.isTimeConditionMet(
                            ruleDefinition.getTimeConditions(), LocalDateTime.now());
                        if (!timeMet) {
                            return false;
                        }

                        // 2. 评估设备触发条件
                        return evaluateDeviceTriggerConditions(ruleDefinition, facts);
                    }
                })
                .then(facts -> {
                    // 执行动作
                    if (ruleDefinition.getActions() != null) {
                        ruleDefinition.getActions().forEach(actionDef -> {
                            String targetDeviceId = actionDef.getTargetDeviceId() != null
                                ? actionDef.getTargetDeviceId()
                                : ruleDefinition.getTargetDeviceId();
                            actionExecutor.executeAction(targetDeviceId, actionDef, facts);
                        });
                    }
                })
                .build();
    }

    /**
     * 评估设备触发条件
     */
    private boolean evaluateDeviceTriggerConditions(RuleDefinition rule, Facts facts) {
        TriggerCondition triggerCondition = rule.getTriggerCondition();
        if (triggerCondition == null || triggerCondition.getConditions().isEmpty()) {
            return true;
        }

        List<DeviceCondition> conditions = triggerCondition.getConditions();
        boolean result;

        if (triggerCondition.getLogic() == TriggerCondition.MatchLogic.ALL) {
            // AND逻辑：所有条件都必须满足
            result = conditions.stream().allMatch(condition ->
                evaluateSingleDeviceCondition(condition, facts));
        } else {
            // ANY逻辑：任一条件满足即可
            result = conditions.stream().anyMatch(condition ->
                evaluateSingleDeviceCondition(condition, facts));
        }

        return result;
    }
}
```

### 3.3 FactsBuilder - Facts构建器

#### 完整Facts构建逻辑
```java
@Component
public class FactsBuilder {

    /**
     * 为规则评估构建完整的Facts对象
     */
    public Facts buildCompleteFactsForRule(RuleDefinition rule, String triggerDeviceId,
                                         String triggerPointId, Object triggerValue) {
        Facts facts = new Facts();

        try {
            // 1. 添加触发信息
            addTriggerInfo(facts, triggerDeviceId, triggerPointId, triggerValue);

            // 2. 分析规则依赖
            Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);

            // 3. 聚合所有相关设备状态（基于内存缓存）
            int addedStates = 0;
            int expiredStates = 0;
            int missingStates = 0;

            for (DevicePointRef ref : dependencies) {
                StateAddResult result = addDeviceStateToFacts(facts, ref);
                switch (result) {
                    case ADDED:
                        addedStates++;
                        break;
                    case EXPIRED:
                        expiredStates++;
                        break;
                    case MISSING:
                        missingStates++;
                        break;
                }
            }

            // 4. 添加全局上下文信息
            addGlobalContextToFacts(facts);

            logger.debug("Facts built for rule {}: {} added, {} expired, {} missing",
                        rule.getRuleId(), addedStates, expiredStates, missingStates);

        } catch (Exception e) {
            logger.error("Error building facts for rule {}: {}", rule.getRuleId(), e.getMessage(), e);
        }

        return facts;
    }

    /**
     * 添加设备状态到Facts
     */
    private StateAddResult addDeviceStateToFacts(Facts facts, DevicePointRef ref) {
        DevicePointState state = stateManager.getDevicePointState(ref.getDeviceId(), ref.getPointId());

        if (state == null) {
            // 设备状态不存在
            String missingKey = ref.getStateKey() + "_missing";
            facts.put(missingKey, true);
            return StateAddResult.MISSING;
        }

        if (isStateExpired(state, ref.getMaxAge())) {
            // 设备状态已过期
            String expiredKey = ref.getStateKey() + "_expired";
            facts.put(expiredKey, true);
            return StateAddResult.EXPIRED;
        }

        // 添加有效的设备状态
        facts.put(ref.getStateKey(), state.getCurrentValue());
        facts.put(ref.getStateKey() + "_dataType", state.getDataType());
        facts.put(ref.getStateKey() + "_updateTime", state.getLastUpdateTime());

        return StateAddResult.ADDED;
    }
}
```

### 3.4 StateManager - 设备状态管理器

#### 通用状态管理实现
```java
@Service
public class StateManager {

    // K: deviceId_pointId, V: 点位状态对象
    private final Map<String, DevicePointState> devicePointStates = new ConcurrentHashMap<>();

    // K: conditionId, V: 状态条件监控
    private final Map<String, StateConditionMonitor> stateConditionMonitors = new ConcurrentHashMap<>();

    /**
     * 处理设备点位数据更新
     */
    public void processDevicePointUpdate(String deviceId, String pointId, Object value, String dataType) {
        // 检查是否跨天，如果跨天则清理状态
        checkAndHandleDateChange();

        String stateKey = generateStateKey(deviceId, pointId);

        // 获取或创建设备点位状态
        DevicePointState pointState = devicePointStates.computeIfAbsent(stateKey,
            k -> new DevicePointState(deviceId, pointId));

        // 记录旧值
        Object oldValue = pointState.getCurrentValue();

        // 更新点位状态
        pointState.updateValue(value, dataType);

        // 创建状态变化事件
        StateChangeEvent changeEvent = StateChangeEvent.createValueChangeEvent(
            deviceId, pointId, oldValue, value, dataType);

        // 检查所有相关的状态条件
        checkStateConditions(deviceId, pointId, pointState, changeEvent);

        // 发布事件而不是直接调用规则引擎
        eventPublisher.publishDeviceStateChange(deviceId, pointId, value);
    }

    /**
     * 检查并处理日期变化（跨天处理）
     */
    private void checkAndHandleDateChange() {
        LocalDate today = LocalDate.now();
        if (!today.equals(lastCheckDate)) {
            logger.info("Date changed from {} to {}, clearing device states and condition monitors", lastCheckDate, today);

            // 清理设备状态（保留当前值，但重置时间相关信息）
            int deviceStateCount = devicePointStates.size();
            devicePointStates.values().forEach(DevicePointState::resetDailyCounters);

            // 取消所有状态条件监控任务
            int monitorCount = stateConditionMonitors.size();
            stateConditionMonitors.values().forEach(StateConditionMonitor::cancel);
            stateConditionMonitors.clear();

            // 更新检查日期
            lastCheckDate = today;

            logger.info("Date change handled: reset {} device states, cancelled {} condition monitors", deviceStateCount, monitorCount);
        }
    }

    /**
     * 检查状态条件监控
     */
    private void checkStateConditions(String deviceId, String pointId, DevicePointState pointState) {
        // 遍历所有相关的状态条件监控
        stateConditionMonitors.values().stream()
            .filter(monitor -> monitor.isRelatedTo(deviceId, pointId))
            .forEach(monitor -> monitor.checkCondition(pointState));
    }

    /**
     * 注册状态条件监控
     */
    public void registerStateCondition(StateCondition condition) {
        String conditionId = condition.getConditionId();

        StateConditionMonitor monitor = new StateConditionMonitor(condition, scheduler, eventPublisher);
        stateConditionMonitors.put(conditionId, monitor);

        logger.info("Registered state condition monitor: {} for device {}.{}",
                   conditionId, condition.getDeviceId(), condition.getPointId());
    }
}
```

### 3.5 TimeSchedulerService - 时间调度服务

#### 时间触发核心逻辑
```java
@Service
public class TimeSchedulerService {

    /**
     * 存储RANGE模式规则的前一次时间条件状态
     */
    private final Map<String, Boolean> previousTimeConditionState = new ConcurrentHashMap<>();

    /**
     * 初始化时间触发器，启动定时调度
     */
    @PostConstruct
    private void initialize() {
        // 每分钟执行一次时间触发检查
        scheduler.scheduleAtFixedRate(this::checkForTimeTriggers, 1, 1, TimeUnit.MINUTES);
    }

    /**
     * 检查时间触发器
     */
    private void checkForTimeTriggers() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDate today = now.toLocalDate();

            logger.debug("Starting time trigger check at {}", now);

            // 检查是否跨天，如果跨天则清理状态
            checkAndHandleDateChange(today);

            // 获取所有启用的时间驱动规则
            List<RuleDefinition> timeDrivenRules = ruleService.findAllEnabledRules().stream()
                    .filter(rule -> rule.getTriggerType() == RuleDefinition.TriggerType.TIME_DRIVEN)
                    .collect(Collectors.toList());

            if (timeDrivenRules.isEmpty()) {
                logger.debug("No time-driven rules found, skipping check.");
                return;
            }

            logger.debug("Found {} time-driven rules to check", timeDrivenRules.size());

            for (RuleDefinition rule : timeDrivenRules) {
                try {
                    processTimeDrivenRule(rule, now);
                } catch (Exception e) {
                    logger.error("Error processing time-driven rule {}: {}", rule.getRuleId(), e.getMessage(), e);
                }
            }

            logger.debug("Completed time trigger check");

        } catch (Exception e) {
            logger.error("Error during time trigger check: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查并处理日期变化（跨天处理）
     */
    private void checkAndHandleDateChange(LocalDate today) {
        if (!today.equals(lastCheckDate)) {
            logger.info("Date changed from {} to {}, clearing time condition states", lastCheckDate, today);

            // 清理所有时间条件状态，避免跨天影响
            int clearedCount = previousTimeConditionState.size();
            previousTimeConditionState.clear();

            // 更新检查日期
            lastCheckDate = today;

            logger.info("Cleared {} time condition states due to date change", clearedCount);
        }
    }

    /**
     * 处理单个时间驱动规则
     */
    private void processTimeDrivenRule(RuleDefinition rule, LocalDateTime now) {
        String ruleId = rule.getRuleId();
        List<TimeCondition> timeConditions = rule.getTimeConditions();

        if (timeConditions == null || timeConditions.isEmpty()) {
            return;
        }

        // 推断触发模式
        InferredTriggerMode mode = inferTriggerMode(timeConditions);

        // 评估当前时间条件是否满足
        boolean isNowActive = timeConditionEvaluator.isTimeConditionMet(timeConditions, now);

        if (mode == InferredTriggerMode.POINT) {
            // 时间点模式：无状态，满足条件即触发
            if (isNowActive) {
                ruleEngineService.triggerRuleActivation(ruleId);
            }
        } else {
            // 时间段模式：有状态，检查状态变化
            processRangeMode(ruleId, isNowActive);
        }
    }

    /**
     * 处理RANGE模式的规则
     */
    private void processRangeMode(String ruleId, boolean isNowActive) {
        Boolean wasPreviouslyActive = previousTimeConditionState.get(ruleId);

        if (wasPreviouslyActive == null) {
            // 首次检查，记录当前状态
            previousTimeConditionState.put(ruleId, isNowActive);
            if (isNowActive) {
                logger.info("First check for RANGE mode rule {}: currently active, triggering activation", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            }
        } else {
            // 检查状态变化
            if (isNowActive && !wasPreviouslyActive) {
                // 从不活跃变为活跃：触发激活
                logger.info("RANGE mode rule {} became active, triggering activation", ruleId);
                ruleEngineService.triggerRuleActivation(ruleId);
            } else if (!isNowActive && wasPreviouslyActive) {
                // 从活跃变为不活跃：触发失活
                logger.info("RANGE mode rule {} became inactive, triggering deactivation", ruleId);
                ruleEngineService.triggerRuleDeactivation(ruleId);
            }

            // 更新状态
            previousTimeConditionState.put(ruleId, isNowActive);
        }
    }
}
```

## 4. 异步处理架构

### 4.1 异步处理层次图

```mermaid
graph TB
    subgraph "事件源层 (同步快速返回)"
        MQTT[MQTT消息]
        Timer[定时器]
        API[API调用]
    end

    subgraph "异步边界1: 规则评估线程池"
        REP[规则评估线程池<br/>CPU密集型<br/>核心数线程]
    end

    subgraph "异步边界2: 动作执行线程池"
        AEP[动作执行线程池<br/>IO密集型<br/>更多线程]
    end

    subgraph "执行结果"
        DC[设备控制]
        MS[消息发送]
        AC[API调用]
    end

    MQTT -->|立即返回| REP
    Timer -->|立即返回| REP
    API -->|立即返回| REP

    REP -->|规则条件判断| AEP
    AEP --> DC
    AEP --> MS
    AEP --> AC

    style MQTT fill:#e1f5fe
    style Timer fill:#e1f5fe
    style API fill:#e1f5fe
    style REP fill:#fff3e0
    style AEP fill:#f3e5f5
```

### 4.2 线程池配置

```java
@Configuration
public class RuleEngineConfig {

    /**
     * 规则评估线程池 - CPU密集型
     */
    @Bean("ruleEvaluationExecutor")
    public Executor ruleEvaluationExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors());
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setQueueCapacity(1000);
        executor.setThreadNamePrefix("RuleEval-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }

    /**
     * 动作执行线程池 - IO密集型
     */
    @Bean("actionExecutionExecutor")
    public Executor actionExecutionExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(Runtime.getRuntime().availableProcessors() * 2);
        executor.setMaxPoolSize(Runtime.getRuntime().availableProcessors() * 4);
        executor.setQueueCapacity(2000);
        executor.setThreadNamePrefix("ActionExec-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.initialize();
        return executor;
    }
}
```

## 5. 核心组件职责总结

| 组件 | 职责 | 处理方式 | 关键特性 |
|------|------|----------|----------|
| **MqttListener** | 接收设备数据，解析Topic和Payload | 同步解析，异步转发 | 快速响应，不阻塞 |
| **StateManager** | 管理设备状态，处理持续时间条件 | 状态化管理，定时器调度 | 通用条件监控 |
| **TimeSchedulerService** | 时间驱动规则的主动调度器 | 每分钟轮询，状态推断 | POINT/RANGE模式 |
| **TimeConditionEvaluator** | 复杂时间条件的被动评估器 | 按需评估，支持多层日历 | 优先级评估 |
| **RuleEngineService** | 规则触发的统一入口 | 异步编排，线程池管理 | 事件驱动架构 |
| **RuleAdapterService** | 规则定义到Easy Rules的适配器 | 条件逻辑适配，类型区分 | 双重条件判断 |
| **FactsBuilder** | Facts构建器，聚合规则评估数据 | 依赖分析，批量获取 | 性能优化 |
| **ActionExecutor** | 动作执行的异步处理器 | 多类型动作，异步执行 | 去重机制 |

## 6. 数据流向总结

### 6.1 事件驱动流程
```
设备数据 → MQTT监听 → 状态管理 → 事件发布 → 规则触发 → Facts构建 → 条件评估 → 动作执行
```

### 6.2 时间驱动流程
```
定时器 → 时间触发 → 条件评估 → 规则触发 → 动作执行
```

### 6.3 异步处理流程
```
事件源快速返回 → 规则评估线程池 → 动作执行线程池 → 最终执行
```

### 6.4 状态管理流程
```
设备状态持续监控 → 超时检测 → 条件满足触发 → 事件发布 → 规则执行
```

## 7. 关键设计模式

### 7.1 事件驱动架构
- **解耦设计**：StateManager通过DeviceEventPublisher与RuleEngineService解耦
- **异步处理**：使用Spring事件机制实现组件间异步通信
- **事件类型**：VALUE_CHANGE、CONDITION_TIMEOUT等不同事件类型

### 7.2 策略模式
- **触发类型策略**：EVENT_DRIVEN vs TIME_DRIVEN不同处理策略
- **时间模式策略**：POINT vs RANGE不同触发策略
- **动作类型策略**：DEVICE_CONTROL、SEND_MESSAGE、CALL_API等不同执行策略

### 7.3 适配器模式
- **规则适配**：RuleDefinition → Easy Rules Rule对象
- **条件适配**：复杂业务条件 → Easy Rules条件表达式
- **Facts适配**：设备状态 → Easy Rules Facts对象

### 7.4 观察者模式
- **状态监控**：StateConditionMonitor监控设备状态变化
- **事件监听**：@EventListener监听状态变化事件
- **定时观察**：TimeSchedulerService定时观察时间条件变化

## 8. 性能优化要点

### 8.1 内存优化
- **状态缓存TTL**：设备状态自动过期清理
- **跨天处理**：日期变化时自动清理状态和监控器
- **批量操作**：Facts构建时批量获取设备状态

### 8.2 并发优化
- **ConcurrentHashMap**：线程安全的状态缓存
- **异步处理**：规则评估和动作执行完全异步化
- **线程池隔离**：CPU密集型和IO密集型任务分离

### 8.3 执行优化
- **去重机制**：30分钟窗口内的重复操作检测
- **依赖分析**：只获取规则实际需要的设备状态
- **条件短路**：时间条件不满足时快速返回

这套规则处理流程确保了系统的高性能、高可靠性和良好的扩展性，为物联网场景下的复杂规则处理提供了完整的解决方案。
```
```
