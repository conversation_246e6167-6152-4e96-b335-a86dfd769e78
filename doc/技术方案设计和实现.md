# 物联网规则引擎技术方案设计和实现

## 开发背景

### 项目起源

本项目是为物联网楼宇智能化场景开发的边缘侧规则引擎，主要服务于医院楼宇、办公楼宇等应用场景的智能化控制。系统采用三层架构：屏端(N个屏) → 业务服务端 → 执行引擎(N个引擎)，本项目实现的是边缘侧执行引擎部分。

### 解决的业务问题

- **复杂场景联动**：支持基于时间、设备状态、环境条件的多维度规则控制
- **实时响应需求**：设备控制响应时间要求在3-10秒内
- **边缘计算**：支持离线运行，减少对中心服务的依赖
- **规模化部署**：单个区域引擎管理1000条以内规则，支持5000-10000个设备点位

## 技术选型

### 核心框架选择

- **Easy Rules 4.1.0**：轻量级规则引擎，支持Java注解和流式API
- **Spring Boot 2.7.18**：提供依赖注入、异步处理、事件驱动等基础能力
- **SQLite 3.44.1.0**：嵌入式数据库，支持离线存储和快速查询
- **MyBatis 2.3.2**：数据访问层，提供灵活的SQL映射

### 选型依据

1. **轻量化要求**：边缘服务器资源有限(8-16G内存，4-8核CPU)
2. **离线能力**：支持断网情况下的规则执行
3. **开发效率**：团队对Java技术栈熟悉，Easy Rules学习成本低
4. **扩展性**：支持自定义操作符和动作类型

## 整体架构

### 系统架构图

```mermaid
graph TB
    subgraph "业务服务端"
        BS[业务服务端]
    end

    subgraph "边缘规则引擎"
        subgraph "API接口层"
            RC[RuleController]
            MC[MonitorController]
            CC[ConfigController]
            RM[RuleManager]
        end

        subgraph "规则引擎核心"
            RES[RuleEngineService]
            RAS[RuleAdapterService]
            AE[ActionExecutor]
            FB[FactsBuilder]
            DA[DependencyAnalyzer]
        end

        subgraph "状态管理"
            SM[StateManager]
            DEP[DeviceEventPublisher]
            SC[StateCondition]
            SCM[StateConditionMonitor]
        end

        subgraph "时间处理"
            TCE[TimeConditionEvaluator]
            TSS[TimeSchedulerService]
            GC[GlobalCalendar]
        end

        subgraph "数据传输"
            ML[MqttListener]
        end

        subgraph "存储层"
            RS[RuleService]
            GCS[GlobalCalendarService]
            DB[(SQLite)]
        end
    end

    subgraph "外部系统"
        MQTT[MQTT Broker]
        DEVICES[IoT设备]
    end

    %% 主要数据流
    BS -->|推送规则| RC
    DEVICES -->|设备数据| MQTT
    MQTT -->|订阅消息| ML
    ML -->|状态更新| SM
    SM -->|发布事件| DEP
    DEP -->|状态变化| RES

    %% 规则执行流程
    RES -->|构建Facts| FB
    FB -->|分析依赖| DA
    RES -->|适配规则| RAS
    RAS -->|执行动作| AE
    RAS -->|时间评估| TCE

    %% 时间触发流程
    TSS -->|定时检查| TCE
    TSS -->|触发规则| RES

    %% API层与存储层直接调用
    RC -->|CRUD操作| RS
    MC -->|查询数据| RS
    CC -->|配置管理| GCS

    %% RuleManager与状态管理交互
    RC -->|规则变化| RM
    RM -->|注册StateCondition| SM
    SM -->|创建监控器| SCM

    %% 存储层
    RS -->|数据持久化| DB
    GCS -->|日历数据| DB

    %% 状态管理内部流程
    SM -->|检查条件| SCM
    SCM -->|超时触发| DEP
```

### 数据流程架构

#### 事件驱动规则执行流程

```mermaid
sequenceDiagram
    participant Device as IoT设备
    participant MQTT as MQTT Broker
    participant ML as MqttListener
    participant SM as StateManager
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant FB as FactsBuilder
    participant RAS as RuleAdapterService
    participant TCE as TimeConditionEvaluator
    participant AE as ActionExecutor

    Device->>MQTT: 发送设备数据
    MQTT->>ML: 推送消息
    ML->>SM: 更新设备状态
    SM->>DEP: 发布状态变化事件
    DEP->>RES: 异步处理事件
    RES->>FB: 构建完整Facts
    FB->>SM: 批量获取设备状态
    RES->>RAS: 适配规则条件
    RAS->>TCE: 评估时间条件
    TCE-->>RAS: 返回时间条件结果
    RAS-->>RES: 返回规则评估结果
    RES->>AE: 异步执行动作
    AE->>Device: 发送控制指令
```

#### 时间驱动规则执行流程

```mermaid
sequenceDiagram
    participant TSS as TimeSchedulerService
    participant TCE as TimeConditionEvaluator
    participant RES as RuleEngineService
    participant RAS as RuleAdapterService
    participant AE as ActionExecutor
    participant Device as IoT设备

    Note over TSS: 每分钟定时检查
    TSS->>TSS: 查找TIME_DRIVEN规则
    loop 每个时间驱动规则
        TSS->>TCE: 评估时间条件
        TCE-->>TSS: 返回条件满足状态
        alt 条件满足且状态变化
            TSS->>RES: triggerRuleActivation(ruleId)
            RES->>RAS: 适配规则
            RAS->>AE: 执行动作
            AE->>Device: 发送控制指令
        end
        alt RANGE模式规则失活
            TSS->>RES: triggerRuleDeactivation(ruleId)
            RES->>RAS: 适配规则
            RAS->>AE: 执行失活动作
            AE->>Device: 发送控制指令
        end
    end
```

## 核心功能模块详细设计

### 1. 规则生命周期管理

#### 功能描述

负责规则的增删改查、状态管理和版本控制，支持动态更新规则而不重启服务。

#### 核心类设计

- **RuleService**：规则数据访问服务
- **RuleController**：规则管理API接口
- **RuleDefinitionEntity**：规则持久化实体

#### 调用时序图

```mermaid
sequenceDiagram
    participant Client as 业务服务端
    participant RC as RuleController
    participant RM as RuleManager
    participant RS as RuleService
    participant SM as StateManager
    participant DB as SQLite数据库
    participant RES as RuleEngineService

    Client->>RC: POST /api/rules (创建规则)
    RC->>RS: saveRule(ruleDefinition)
    RS->>DB: INSERT rule_definition
    DB-->>RS: 返回规则ID
    RS-->>RC: 返回保存结果
    RC->>RM: registerStateConditionsForNewRule(rule)
    RM->>RM: 分析规则中的持续时间条件
    RM->>SM: registerStateCondition(stateCondition)
    SM->>SM: 创建StateConditionMonitor
    RC->>RES: refreshRulesCache()
    RC-->>Client: 返回创建结果
```

#### 数据状态流转

```mermaid
stateDiagram-v2
    [*] --> 草稿: 创建规则
    草稿 --> 启用: 启用规则
    启用 --> 禁用: 禁用规则
    禁用 --> 启用: 重新启用
    启用 --> 更新中: 更新规则
    更新中 --> 启用: 更新完成
    启用 --> 删除: 删除规则
    禁用 --> 删除: 删除规则
    删除 --> [*]
```

### 2. 规则执行引擎

#### 功能描述

基于Easy Rules封装的规则执行引擎，支持事件驱动和时间驱动两种触发模式，提供异步处理能力。

#### 核心组件

- **RuleEngineServiceImpl**：规则引擎服务实现，处理事件驱动和时间驱动规则
- **RuleAdapterService**：规则适配器，将RuleDefinition转换为Easy Rules对象
- **FactsBuilder**：Facts构建器，聚合规则评估所需的设备状态
- **TimeSchedulerService**：时间调度服务，每分钟检查时间驱动规则并触发执行

#### 执行流程

```mermaid
flowchart TD
    A[事件触发] --> B{触发类型}
    B -->|设备事件| C[查找相关规则]
    B -->|时间触发| D[TimeSchedulerService定时检查]

    C --> E[构建完整Facts]
    E --> F[规则适配]

    D --> G[评估时间条件]
    G --> H{时间满足?}
    H -->|是| I[触发规则激活/失活]
    H -->|否| J[跳过执行]
    I --> F

    F --> K{规则条件评估}
    K -->|满足| L[异步执行动作]
    K -->|不满足| M[跳过执行]
    L --> N[记录执行日志]
    M --> N
    J --> N
    N --> O[处理完成]
```

### 3. 时间触发器实现

#### 功能描述

支持复杂的时间条件评估，包括Cron表达式、工作日、季节、个性化日历等多维度时间判断。

#### 核心类

- **TimeConditionEvaluator**：时间条件评估器
- **GlobalCalendar**：全局日历管理
- **TimeSchedulerService**：时间调度服务

#### 时间条件层次结构

```mermaid
graph TD
    A[时间条件评估] --> B[强制包含日期]
    A --> C[强制排除日期]
    A --> D[季节条件]
    A --> E[工作日条件]
    A --> F[Cron表达式]

    B --> G{优先级最高}
    C --> H{优先级次高}
    D --> I{季节匹配}
    E --> J{工作日匹配}
    F --> K{时间段匹配}
```

### 4. 设备状态持续触发机制

#### 功能描述

通用设备状态管理器，支持任意设备条件的持续时间判断，包括但不限于：

- 温度传感器：温度 > 30度 持续 10分钟
- 占用传感器：占用状态 = "UNOCCUPIED" 持续 15分钟
- 照度传感器：照度 < 100 持续 5分钟
- 湿度传感器：湿度 > 60% 持续 20分钟
- 压力传感器：压力 < 1.0bar 持续 30分钟

#### 核心实现

- **StateManager**：通用设备状态管理器，管理所有类型设备的状态缓存和条件监控
- **StateConditionMonitor**：状态条件监控器，支持任意操作符的持续时间判断
- **StateCondition**：状态条件定义，包含设备ID、点位ID、操作符、目标值、持续时间
- **DevicePointState**：设备点位状态，记录当前值、历史值、更新时间、持续时间

#### 通用状态监控机制

```mermaid
sequenceDiagram
    participant Device as 任意设备
    participant SM as StateManager
    participant SCM as StateConditionMonitor
    participant Scheduler as 定时器
    participant DEP as DeviceEventPublisher

    Note over Device: 可以是温度、湿度、照度、占用等任意传感器
    Device->>SM: 状态变化(满足条件值)
    SM->>SCM: 检查所有相关条件
    SCM->>SCM: 评估条件匹配
    alt 条件匹配且无活跃监控
        SCM->>Scheduler: 启动持续时间定时器
        Note over SCM: 记录条件开始满足的时间
    end

    opt 持续时间内状态变化
        Device->>SM: 状态变化(不满足条件)
        SM->>SCM: 重新检查条件
        SCM->>Scheduler: 取消定时器
        Note over SCM: 条件不再满足，清除监控
    end

    opt 持续时间到期
        Scheduler->>SCM: 定时器触发
        SCM->>SM: 验证当前状态仍满足条件
        SM->>DEP: 发布条件超时事件
        DEP->>RuleEngineService: 触发相关规则
    end
```

### 5. 异步处理和线程池管理

#### 设计原则

- **分层异步**：规则评估和动作执行使用独立线程池
- **资源隔离**：CPU密集型和IO密集型任务分离
- **错误隔离**：动作执行失败不影响规则引擎核心

#### 线程池配置

| 线程池名称                   | 用途   | 线程数配置    | 队列大小 |
| ----------------------- | ---- | -------- | ---- |
| ruleEvaluationExecutor  | 规则评估 | CPU核心数   | 1000 |
| actionExecutionExecutor | 动作执行 | CPU核心数*2 | 2000 |
| deviceStateScheduler    | 状态管理 | 2        | 无界队列 |
| timeTriggerScheduler    | 时间触发 | 1        | 无界队列 |

### 6. 监控和日志设计

#### 监控指标

- **规则执行统计**：成功/失败次数、平均执行时间
- **设备状态统计**：活跃设备数、状态更新频率
- **线程池监控**：队列长度、活跃线程数、拒绝任务数
- **内存使用**：规则缓存大小、设备状态缓存大小

#### 日志分级

- **ERROR**：系统错误、规则执行失败
- **WARN**：配置问题、性能警告
- **INFO**：规则触发、动作执行
- **DEBUG**：详细执行流程
- **TRACE**：最详细的调试信息

## 性能优化策略

### 1. 规则匹配优化

- **索引优化**：基于设备ID和规则类型建立索引
- **缓存策略**：热点规则内存缓存，减少数据库查询
- **批量处理**：相同设备的多个事件批量处理

### 2. 状态管理优化

- **内存管理**：定期清理过期状态，控制内存使用
- **并发优化**：使用ConcurrentHashMap减少锁竞争
- **批量查询**：多条件规则的设备状态批量获取

### 3. 异步处理优化

- **线程池调优**：根据实际负载调整线程池参数
- **队列监控**：监控队列长度，防止内存溢出
- **背压处理**：高负载时的限流和降级策略

## 已知风险和待解决问题

### 1. 技术风险

- **内存泄漏**：长时间运行可能导致设备状态缓存过大
- **时间同步**：跨天处理和时区变化可能影响时间条件评估
- **并发安全**：高并发场景下的状态一致性问题

### 2. 业务风险

- **规则冲突**：多条规则同时触发可能产生冲突
- **循环依赖**：规则间的循环触发可能导致无限循环
- **数据一致性**：设备状态与实际设备状态的同步延迟

### 3. 解决方案

- **监控告警**：建立完善的监控体系，及时发现问题
- **限流机制**：对规则执行频率进行限制
- **状态校验**：定期与设备进行状态同步校验
- **规则验证**：规则创建时进行冲突检测和循环依赖检查

## 扩展性设计

### 1. 操作符扩展

- **插件化设计**：支持自定义操作符的动态加载
- **类型安全**：强类型操作符定义，减少运行时错误

### 2. 动作类型扩展

- **策略模式**：不同动作类型使用不同的执行策略
- **异步执行**：所有动作类型统一异步执行框架

### 3. 数据源扩展

- **适配器模式**：支持MQTT、HTTP、TCP等多种数据源
- **协议解析**：可插拔的协议解析器

## 具体使用示例

### 1. 典型业务场景实现

#### 场景一：办公室智能照明控制

```json
{
  "ruleId": "office_lighting_001",
  "ruleName": "办公室无人15分钟关灯",
  "targetDeviceId": "light_office_001",
  "triggerType": "EVENT_DRIVEN",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 8-18 ? * MON-FRI"],
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "logic": "AND"
    }
  ],
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceId": "occupancy_sensor_001",
        "pointId": "occupancy_status",
        "operator": "STATES_KEEP_MINUTES",
        "value": "UNOCCUPIED",
        "durationMinutes": 15
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "turn_off"
      }
    }
  ]
}
```

#### 场景二：空调智能温控

```json
{
  "ruleId": "hvac_temp_control_001",
  "ruleName": "温度过高自动开启空调",
  "targetDeviceId": "hvac_001",
  "triggerType": "EVENT_DRIVEN",
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceId": "temp_sensor_001",
        "pointId": "temperature",
        "operator": "GREATER_THAN",
        "value": 28,
        "durationMinutes": 5
      },
      {
        "sourceDeviceId": "occupancy_sensor_001",
        "pointId": "occupancy_status",
        "operator": "EQUALS",
        "value": "OCCUPIED"
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "turn_on",
        "temperature": 24,
        "mode": "cooling"
      }
    }
  ]
}
```

### 2. 复杂时间条件示例

#### 夏季工作日特殊时间段

```json
{
  "timeConditions": [
    {
      "timeCronExpressions": [
        "0 0 8-12 ? *",
        "0 0 14-18 ? *"
      ],
      "season": "summer",
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "excludeDates": ["2024-07-04", "2024-08-15"],
      "includeDates": ["2024-07-06"],
      "logic": "AND"
    }
  ]
}
```

### 3. TimeSchedulerService实现细节

#### 时间触发器核心逻辑

```java
@Service
public class TimeSchedulerService {

    @PostConstruct
    private void initialize() {
        // 每分钟执行一次时间触发检查
        scheduler.scheduleAtFixedRate(this::checkForTimeTriggers, 1, 1, TimeUnit.MINUTES);
    }

    private void checkForTimeTriggers() {
        LocalDateTime now = LocalDateTime.now();

        // 获取所有启用的时间驱动规则
        List<RuleDefinition> timeDrivenRules = ruleService.findAllEnabledRules().stream()
                .filter(rule -> rule.getTriggerType() == RuleDefinition.TriggerType.TIME_DRIVEN)
                .collect(Collectors.toList());

        for (RuleDefinition rule : timeDrivenRules) {
            processTimeDrivenRule(rule, now);
        }
    }

    private void processTimeDrivenRule(RuleDefinition rule, LocalDateTime now) {
        // 推断触发模式：POINT（时间点）或 RANGE（时间段）
        InferredTriggerMode mode = inferTriggerMode(rule.getTimeConditions());

        // 评估当前时间条件是否满足
        boolean isNowActive = timeConditionEvaluator.isTimeConditionMet(rule.getTimeConditions(), now);

        if (mode == InferredTriggerMode.POINT) {
            // 时间点模式：满足条件即触发
            if (isNowActive) {
                ruleEngineService.triggerRuleActivation(rule.getRuleId());
            }
        } else {
            // 时间段模式：检查状态变化，在进入/离开时间段时触发
            processRangeMode(rule.getRuleId(), isNowActive);
        }
    }
}
```

## 代码实现关键点

### 1. 异步处理实现

```java
@Service
public class RuleEngineServiceImpl implements RuleEngineService {

    @Async("ruleEvaluationExecutor")
    @EventListener
    public void handleStateChangeEvent(StateChangeEvent event) {
        // 异步处理状态变化事件
        processDeviceEventInternal(event.getDeviceId(),
                                 event.getPointId(),
                                 event.getNewValue());
    }

    @Override
    public void processDeviceEvent(String deviceId, String pointId, Object value) {
        // 立即返回，异步处理
        ruleEvaluationExecutor.execute(() -> {
            processDeviceEventInternal(deviceId, pointId, value);
        });
    }
}
```

### 2. 状态持续监控实现

```java
@Service
public class StateManager {

    public void processDevicePointUpdate(String deviceId, String pointId, Object value, String dataType) {
        // 更新设备状态
        DevicePointState pointState = updateDeviceState(deviceId, pointId, value, dataType);

        // 检查相关的状态条件监控
        checkStateConditions(deviceId, pointId, pointState);

        // 发布状态变化事件（解耦设计）
        eventPublisher.publishDeviceStateChange(deviceId, pointId, value);
    }

    private void checkStateConditions(String deviceId, String pointId, DevicePointState pointState) {
        // 遍历所有相关的状态条件监控
        stateConditionMonitors.values().stream()
            .filter(monitor -> monitor.isRelatedTo(deviceId, pointId))
            .forEach(monitor -> monitor.checkCondition(pointState));
    }
}
```

### 3. 规则适配器核心逻辑

```java
@Service
public class RuleAdapterService {

    public Rule adapt(RuleDefinition ruleDefinition) {
        return new RuleBuilder()
            .name(ruleDefinition.getRuleId())
            .when(facts -> evaluateConditions(ruleDefinition, facts))
            .then(facts -> executeActions(ruleDefinition, facts))
            .build();
    }

    private boolean evaluateConditions(RuleDefinition rule, Facts facts) {
        // 1. 检查规则是否启用
        if (!rule.isEnabled()) return false;

        // 2. 根据触发类型选择评估策略
        if (rule.getTriggerType() == RuleDefinition.TriggerType.TIME_DRIVEN) {
            return evaluateTimeDrivenRule(rule, facts);
        } else {
            return evaluateEventDrivenRule(rule, facts);
        }
    }
}
```

## 部署和运维

### 1. 部署要求

- **硬件要求**：最低4核8G，推荐8核16G
- **软件依赖**：Java 11+, SQLite 3.x
- **网络要求**：支持MQTT连接，HTTP API访问

### 2. 配置管理

- **环境配置**：支持dev/test/prod多环境配置
- **动态配置**：支持运行时配置更新
- **配置校验**：启动时进行配置有效性检查

### 3. 运维监控

- **健康检查**：提供HTTP健康检查接口
- **指标暴露**：支持Prometheus指标采集
- **日志管理**：支持日志轮转和远程日志收集

## 项目发现的问题和改进建议

### 1. 当前发现的问题

#### 功能实现问题

- **错误处理不统一**：不同模块的异常处理策略不一致
- **监控指标不完善**：缺少详细的业务监控指标
- **配置管理**：部分配置参数硬编码，需要外部化配置

### 2. 改进建议

#### 短期改进（1-2周）

1. **统一异常处理**：建立统一的异常处理机制和错误码体系
2. **完善单元测试**：提高核心业务逻辑的测试覆盖率
3. **增强监控指标**：添加更详细的业务监控指标

#### 中期改进（1-2月）

1. **配置外部化**：将硬编码配置参数移至配置文件
2. **性能优化**：基于实际运行数据进行性能调优
3. **API文档完善**：使用Swagger等工具自动生成API文档

#### 长期改进（3-6月）

1. **微服务拆分**：考虑将规则引擎拆分为多个微服务
2. **分布式支持**：支持多实例部署和负载均衡
3. **可视化管理**：开发规则管理的Web界面

### 3. 技术债务清单

- [ ] 完善单元测试覆盖率（目标80%+）
- [ ] 建立完整的集成测试套件
- [ ] 优化数据库查询性能
- [ ] 完善API文档和使用示例
- [ ] 建立代码质量检查流程
- [ ] 配置参数外部化管理
