# 物联网规则引擎使用说明

## 系统维护人员部分

### 部署和配置说明

#### 系统要求
- **硬件要求**：
  - 最低配置：4核CPU，8GB内存，50GB存储空间
  - 推荐配置：8核CPU，16GB内存，100GB存储空间
- **软件依赖**：
  - Java 11或更高版本
  - SQLite 3.x（内嵌，无需单独安装）
- **网络要求**：
  - MQTT Broker连接（端口1883或8883）
  - HTTP API访问（默认端口8080）

#### 部署步骤

1. **环境准备**
```bash
# 检查Java版本
java -version

# 创建应用目录
mkdir -p /opt/rule-engine
cd /opt/rule-engine
```

2. **应用部署**
```bash
# 复制应用JAR包
cp rule-engine-1.0.0.jar /opt/rule-engine/

# 复制配置文件
cp application.yml /opt/rule-engine/

# 创建日志目录
mkdir -p /opt/rule-engine/logs

# 创建数据目录
mkdir -p /opt/rule-engine/data
```

3. **配置文件修改**
```yaml
# application.yml 关键配置项
server:
  port: 8080

spring:
  datasource:
    url: *************************************************
    
logging:
  file:
    name: /opt/rule-engine/logs/rule-engine.log
    
rule-engine:
  mqtt:
    broker-url: tcp://localhost:1883
    username: your_username
    password: your_password
```

4. **启动服务**
```bash
# 前台启动（测试用）
java -jar rule-engine-1.0.0.jar

# 后台启动（生产用）
nohup java -jar rule-engine-1.0.0.jar > /dev/null 2>&1 &

# 使用systemd管理（推荐）
sudo systemctl start rule-engine
sudo systemctl enable rule-engine
```

#### 配置参数说明

| 配置项 | 默认值 | 说明 |
|--------|--------|------|
| `server.port` | 8080 | HTTP API服务端口 |
| `rule-engine.thread-pool.rule-evaluation.core-size` | CPU核心数 | 规则评估线程池大小 |
| `rule-engine.thread-pool.action-execution.core-size` | CPU核心数*2 | 动作执行线程池大小 |
| `rule-engine.cache.rule-cache-size` | 1000 | 规则缓存大小 |
| `rule-engine.cache.state-cache-ttl` | 3600 | 设备状态缓存TTL（秒） |
| `rule-engine.duplicate-prevention.window-minutes` | 30 | 重复操作防护窗口（分钟） |

### 监控指标和告警

#### 关键监控指标

1. **系统健康指标**
```bash
# 健康检查接口
curl http://localhost:8080/actuator/health

# 系统信息接口
curl http://localhost:8080/api/monitor/system-info
```

2. **业务监控指标**
- **规则执行统计**：成功/失败次数、平均执行时间
- **设备状态统计**：活跃设备数、状态更新频率
- **线程池监控**：队列长度、活跃线程数、拒绝任务数
- **内存使用**：规则缓存大小、设备状态缓存大小

3. **监控API接口**
```bash
# 获取规则执行统计
curl http://localhost:8080/api/monitor/rule-stats

# 获取设备状态统计
curl http://localhost:8080/api/monitor/device-stats

# 获取线程池状态
curl http://localhost:8080/api/monitor/thread-pool-stats
```

#### 告警配置建议

| 指标 | 告警阈值 | 告警级别 |
|------|----------|----------|
| CPU使用率 | >80% | 警告 |
| 内存使用率 | >85% | 警告 |
| 规则执行失败率 | >5% | 严重 |
| 线程池队列长度 | >500 | 警告 |
| 设备状态更新延迟 | >30秒 | 警告 |
| 磁盘使用率 | >90% | 严重 |

### 常见问题排查

#### 1. 服务启动失败
**症状**：服务无法启动或启动后立即退出

**排查步骤**：
```bash
# 检查Java版本
java -version

# 检查端口占用
netstat -tlnp | grep 8080

# 查看启动日志
tail -f logs/rule-engine.log

# 检查配置文件语法
java -jar rule-engine-1.0.0.jar --spring.config.location=application.yml --spring.profiles.active=validate-only
```

**常见原因**：
- Java版本不兼容
- 端口被占用
- 配置文件格式错误
- 数据库文件权限问题

#### 2. 规则不执行
**症状**：规则已创建但不触发执行

**排查步骤**：
```bash
# 检查规则状态
curl http://localhost:8080/api/rules/{ruleId}

# 查看规则执行日志
grep "rule_id:{ruleId}" logs/rule-engine.log

# 检查设备数据接收
grep "Device point updated" logs/rule-engine.log
```

**常见原因**：
- 规则未启用（enabled=false）
- 时间条件不满足
- 设备数据未接收到
- 设备ID或点位ID不匹配

#### 3. 内存使用过高
**症状**：系统内存持续增长，可能导致OOM

**排查步骤**：
```bash
# 检查内存使用
curl http://localhost:8080/api/monitor/memory-stats

# 查看设备状态缓存大小
curl http://localhost:8080/api/monitor/device-stats

# 检查规则缓存大小
curl http://localhost:8080/api/monitor/rule-stats
```

**解决方案**：
- 调整缓存TTL配置
- 增加内存清理频率
- 限制缓存大小上限

#### 4. MQTT连接问题
**症状**：无法接收设备数据

**排查步骤**：
```bash
# 检查MQTT连接状态
curl http://localhost:8080/api/monitor/mqtt-status

# 查看MQTT连接日志
grep "MQTT" logs/rule-engine.log

# 测试MQTT连接
mosquitto_pub -h localhost -p 1883 -t test/topic -m "test message"
```

### 性能调优建议

#### 1. JVM参数优化
```bash
# 推荐JVM参数
java -Xms2g -Xmx4g \
     -XX:+UseG1GC \
     -XX:MaxGCPauseMillis=200 \
     -XX:+HeapDumpOnOutOfMemoryError \
     -XX:HeapDumpPath=/opt/rule-engine/logs/ \
     -jar rule-engine-1.0.0.jar
```

#### 2. 线程池调优
```yaml
rule-engine:
  thread-pool:
    rule-evaluation:
      core-size: 8        # 根据CPU核心数调整
      max-size: 16        # 最大线程数
      queue-capacity: 1000 # 队列大小
    action-execution:
      core-size: 16       # IO密集型，可以设置更大
      max-size: 32
      queue-capacity: 2000
```

#### 3. 缓存优化
```yaml
rule-engine:
  cache:
    rule-cache-size: 2000      # 根据规则数量调整
    state-cache-ttl: 1800      # 30分钟TTL
    state-cleanup-interval: 300 # 5分钟清理一次
```

## API调用方部分

### 接口文档和示例

#### 1. 规则管理接口

**创建规则**
```http
POST /api/rules
Content-Type: application/json

{
  "ruleId": "rule_001",
  "ruleName": "办公室照明控制",
  "targetDeviceId": "light_001",
  "triggerType": "EVENT_DRIVEN",
  "enabled": true,
  "priority": 1,
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 8-18 ? * MON-FRI"],
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "season": "all",
      "logic": "AND",
      "enabled": true
    }
  ],
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceId": "sensor_001",
        "pointId": "occupancy",
        "operator": "EQUALS",
        "value": "UNOCCUPIED",
        "durationMinutes": 15,
        "enabled": true
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "targetDeviceId": "light_001",
      "enabled": true,
      "params": {
        "command": "turn_off"
      }
    }
  ]
}
```

**查询规则**
```http
GET /api/rules/{ruleId}
```

**更新规则**
```http
PUT /api/rules/{ruleId}
Content-Type: application/json

{
  "enabled": false
}
```

**删除规则**
```http
DELETE /api/rules/{ruleId}
```

#### 2. 监控接口

**系统状态查询**
```http
GET /api/monitor/system-info

# 响应示例
{
  "engineId": "engine_001",
  "version": "1.0.0",
  "uptime": 3600000,
  "ruleCount": 150,
  "activeDeviceCount": 500,
  "memoryUsage": {
    "used": "512MB",
    "max": "2GB",
    "usage": "25%"
  }
}
```

**规则执行统计**
```http
GET /api/monitor/rule-stats

# 响应示例
{
  "totalRules": 150,
  "enabledRules": 145,
  "executionStats": {
    "totalExecutions": 10000,
    "successfulExecutions": 9950,
    "failedExecutions": 50,
    "averageExecutionTime": 25
  }
}
```

#### 3. 配置管理接口

**全局日历管理**
```http
POST /api/config/calendar
Content-Type: application/json

{
  "holidays": ["2024-01-01", "2024-02-10", "2024-05-01"],
  "summerPeriod": {
    "startDate": "2024-06-01",
    "endDate": "2024-08-31"
  },
  "winterPeriod": {
    "startDate": "2024-12-01",
    "endDate": "2024-02-28"
  }
}
```

### 规则定义格式说明

#### 时间条件格式
```json
{
  "timeCronExpressions": [
    "0 0 8-12 ? * MON-FRI",  // 工作日上午8-12点
    "0 0 14-18 ? * MON-FRI"  // 工作日下午2-6点
  ],
  "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
  "season": "summer",
  "includeDates": ["2024-06-15"],  // 强制包含的日期
  "excludeDates": ["2024-06-16"],  // 强制排除的日期
  "logic": "AND",
  "enabled": true
}
```

#### 设备条件格式
```json
{
  "sourceDeviceId": "sensor_001",
  "pointId": "temperature",
  "operator": "GREATER_THAN",
  "value": 30,
  "durationMinutes": 10,  // 持续10分钟
  "enabled": true
}
```

#### 支持的操作符

| 操作符 | 说明 | 示例 |
|--------|------|------|
| `EQUALS` | 等于 | `"value": "ON"` |
| `NOT_EQUALS` | 不等于 | `"value": "OFF"` |
| `GREATER_THAN` | 大于 | `"value": 25` |
| `LESS_THAN` | 小于 | `"value": 30` |
| `BETWEEN` | 介于之间 | `"value": 20, "upperValue": 30` |
| `CONTAINS` | 包含 | `"value": "error"` |
| `STATES_KEEP_MINUTES` | 状态持续分钟数 | `"durationMinutes": 15` |

#### 动作类型格式

**设备控制动作**
```json
{
  "actionType": "DEVICE_CONTROL",
  "targetDeviceId": "light_001",
  "params": {
    "command": "turn_on",
    "brightness": 80,
    "color": "warm_white"
  }
}
```

**消息发送动作**
```json
{
  "actionType": "SEND_MESSAGE",
  "params": {
    "messageType": "email",
    "recipient": "<EMAIL>",
    "subject": "设备告警",
    "content": "设备 {deviceId} 温度过高"
  }
}
```

**API调用动作**
```json
{
  "actionType": "CALL_API",
  "params": {
    "url": "http://api.example.com/notify",
    "method": "POST",
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "deviceId": "{deviceId}",
      "alert": "temperature_high"
    }
  }
}
```

### 集成指南

#### 1. MQTT集成
```python
# Python示例：发送设备数据
import paho.mqtt.client as mqtt
import json

def publish_device_data(device_id, point_id, value, data_type="string"):
    client = mqtt.Client()
    client.connect("localhost", 1883, 60)
    
    topic = f"device/{device_id}/{point_id}"
    payload = {
        "deviceId": device_id,
        "pointId": point_id,
        "value": value,
        "dataType": data_type,
        "timestamp": int(time.time() * 1000)
    }
    
    client.publish(topic, json.dumps(payload))
    client.disconnect()

# 使用示例
publish_device_data("sensor_001", "temperature", 25.5, "double")
publish_device_data("sensor_001", "occupancy", "OCCUPIED", "string")
```

#### 2. HTTP API集成
```javascript
// JavaScript示例：创建规则
async function createRule(ruleDefinition) {
    const response = await fetch('http://localhost:8080/api/rules', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(ruleDefinition)
    });
    
    if (response.ok) {
        const result = await response.json();
        console.log('规则创建成功:', result);
        return result;
    } else {
        throw new Error('规则创建失败');
    }
}
```

### 最佳实践建议

#### 1. 规则设计原则
- **单一职责**：每个规则只负责一个具体的控制逻辑
- **条件简化**：避免过于复杂的条件组合
- **优先级设置**：重要规则设置更高优先级
- **测试验证**：规则上线前进行充分测试

#### 2. 性能优化建议
- **批量操作**：批量创建/更新规则，减少API调用次数
- **合理缓存**：利用规则缓存机制，避免频繁查询
- **监控告警**：建立完善的监控体系，及时发现问题

#### 3. 错误处理
- **重试机制**：API调用失败时实现重试逻辑
- **降级策略**：规则引擎不可用时的备用方案
- **日志记录**：详细记录操作日志，便于问题排查

#### 4. 安全考虑
- **访问控制**：限制API访问权限
- **参数验证**：严格验证输入参数
- **敏感信息**：避免在规则中存储敏感信息

## 高级功能使用指南

### 1. 复杂规则场景

#### 多条件组合规则
```json
{
  "ruleId": "complex_hvac_control",
  "ruleName": "复杂空调控制规则",
  "targetDeviceId": "hvac_001",
  "triggerType": "EVENT_DRIVEN",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 8-18 ? * MON-FRI"],
      "season": "summer",
      "logic": "AND"
    }
  ],
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceId": "temp_sensor_001",
        "pointId": "temperature",
        "operator": "GREATER_THAN",
        "value": 26,
        "durationMinutes": 5
      },
      {
        "sourceDeviceId": "humidity_sensor_001",
        "pointId": "humidity",
        "operator": "GREATER_THAN",
        "value": 60
      },
      {
        "sourceDeviceId": "occupancy_sensor_001",
        "pointId": "occupancy_status",
        "operator": "EQUALS",
        "value": "OCCUPIED"
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "set_mode",
        "mode": "cooling",
        "temperature": 24,
        "fan_speed": "auto"
      }
    },
    {
      "actionType": "SEND_MESSAGE",
      "params": {
        "messageType": "notification",
        "content": "空调已自动开启制冷模式"
      }
    }
  ]
}
```

#### 时间范围规则（TIME_DRIVEN）
```json
{
  "ruleId": "night_security_mode",
  "ruleName": "夜间安防模式",
  "triggerType": "TIME_DRIVEN",
  "timeMode": "RANGE",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 22 ? * *"],
      "logic": "AND"
    }
  ],
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "targetDeviceId": "security_system_001",
      "params": {
        "command": "enable_night_mode"
      }
    }
  ],
  "deactivationActions": [
    {
      "actionType": "DEVICE_CONTROL",
      "targetDeviceId": "security_system_001",
      "params": {
        "command": "disable_night_mode"
      }
    }
  ]
}
```

### 2. 批量操作示例

#### 批量创建规则
```bash
#!/bin/bash
# 批量创建照明控制规则

RULE_ENGINE_URL="http://localhost:8080"
ROOMS=("office_001" "office_002" "office_003" "meeting_room_001")

for room in "${ROOMS[@]}"; do
  cat > rule_${room}.json << EOF
{
  "ruleId": "lighting_${room}",
  "ruleName": "${room}照明控制",
  "targetDeviceId": "light_${room}",
  "triggerType": "EVENT_DRIVEN",
  "timeConditions": [
    {
      "timeCronExpressions": ["0 0 8-18 ? * MON-FRI"],
      "workDays": ["MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY"],
      "logic": "AND"
    }
  ],
  "triggerCondition": {
    "logic": "ALL",
    "conditions": [
      {
        "sourceDeviceId": "occupancy_${room}",
        "pointId": "occupancy_status",
        "operator": "STATES_KEEP_MINUTES",
        "value": "UNOCCUPIED",
        "durationMinutes": 15
      }
    ]
  },
  "actions": [
    {
      "actionType": "DEVICE_CONTROL",
      "params": {
        "command": "turn_off"
      }
    }
  ]
}
EOF

  # 创建规则
  curl -X POST "${RULE_ENGINE_URL}/api/rules" \
       -H "Content-Type: application/json" \
       -d @rule_${room}.json

  echo "Created rule for ${room}"
  rm rule_${room}.json
done
```

#### 批量查询和统计
```python
import requests
import json
from datetime import datetime, timedelta

class RuleEngineClient:
    def __init__(self, base_url="http://localhost:8080"):
        self.base_url = base_url

    def get_all_rules(self):
        """获取所有规则"""
        response = requests.get(f"{self.base_url}/api/rules")
        return response.json()

    def get_rule_stats(self):
        """获取规则统计信息"""
        response = requests.get(f"{self.base_url}/api/monitor/rule-stats")
        return response.json()

    def get_device_stats(self):
        """获取设备统计信息"""
        response = requests.get(f"{self.base_url}/api/monitor/device-stats")
        return response.json()

    def generate_report(self):
        """生成系统运行报告"""
        rules = self.get_all_rules()
        rule_stats = self.get_rule_stats()
        device_stats = self.get_device_stats()

        report = {
            "timestamp": datetime.now().isoformat(),
            "summary": {
                "total_rules": len(rules.get("data", [])),
                "enabled_rules": len([r for r in rules.get("data", []) if r.get("enabled", False)]),
                "rule_execution_success_rate": rule_stats.get("successRate", 0),
                "active_devices": device_stats.get("totalDevicePoints", 0)
            },
            "details": {
                "rules": rules,
                "rule_stats": rule_stats,
                "device_stats": device_stats
            }
        }

        return report

# 使用示例
client = RuleEngineClient()
report = client.generate_report()
print(json.dumps(report, indent=2))
```

### 3. 监控和告警集成

#### Prometheus监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'rule-engine'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s
```

#### Grafana仪表板配置
```json
{
  "dashboard": {
    "title": "规则引擎监控",
    "panels": [
      {
        "title": "规则执行成功率",
        "type": "stat",
        "targets": [
          {
            "expr": "rule_engine_execution_success_rate",
            "legendFormat": "成功率"
          }
        ]
      },
      {
        "title": "活跃设备数量",
        "type": "graph",
        "targets": [
          {
            "expr": "rule_engine_active_devices_total",
            "legendFormat": "活跃设备"
          }
        ]
      },
      {
        "title": "线程池状态",
        "type": "graph",
        "targets": [
          {
            "expr": "rule_engine_thread_pool_active_threads",
            "legendFormat": "活跃线程-{{pool_name}}"
          }
        ]
      }
    ]
  }
}
```

### 4. 故障排查工具

#### 规则调试脚本
```bash
#!/bin/bash
# 规则调试工具

RULE_ENGINE_URL="http://localhost:8080"
RULE_ID="$1"

if [ -z "$RULE_ID" ]; then
    echo "Usage: $0 <rule_id>"
    exit 1
fi

echo "=== 规则信息 ==="
curl -s "${RULE_ENGINE_URL}/api/rules/${RULE_ID}" | jq '.'

echo -e "\n=== 规则执行历史 ==="
curl -s "${RULE_ENGINE_URL}/api/monitor/rule-execution-history?ruleId=${RULE_ID}" | jq '.'

echo -e "\n=== 相关设备状态 ==="
curl -s "${RULE_ENGINE_URL}/api/monitor/device-states?ruleId=${RULE_ID}" | jq '.'

echo -e "\n=== 最近错误日志 ==="
curl -s "${RULE_ENGINE_URL}/api/monitor/recent-errors?ruleId=${RULE_ID}" | jq '.'
```

#### 性能分析脚本
```python
import requests
import time
import matplotlib.pyplot as plt
from datetime import datetime

def monitor_performance(duration_minutes=10):
    """监控系统性能指标"""
    base_url = "http://localhost:8080"
    metrics = {
        "timestamps": [],
        "cpu_usage": [],
        "memory_usage": [],
        "rule_execution_rate": [],
        "thread_pool_queue_size": []
    }

    end_time = time.time() + (duration_minutes * 60)

    while time.time() < end_time:
        try:
            # 获取系统信息
            response = requests.get(f"{base_url}/api/monitor/system-info")
            data = response.json()

            metrics["timestamps"].append(datetime.now())
            metrics["cpu_usage"].append(data.get("cpuUsage", 0))
            metrics["memory_usage"].append(data.get("memoryUsage", {}).get("usage", 0))

            # 获取规则统计
            response = requests.get(f"{base_url}/api/monitor/rule-stats")
            rule_data = response.json()
            metrics["rule_execution_rate"].append(rule_data.get("executionRate", 0))

            # 获取线程池状态
            response = requests.get(f"{base_url}/api/monitor/thread-pool-stats")
            thread_data = response.json()
            queue_size = sum([pool.get("queueSize", 0) for pool in thread_data.get("pools", [])])
            metrics["thread_pool_queue_size"].append(queue_size)

            time.sleep(30)  # 30秒采样一次

        except Exception as e:
            print(f"Error collecting metrics: {e}")
            time.sleep(30)

    # 生成性能报告图表
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))

    axes[0, 0].plot(metrics["timestamps"], metrics["cpu_usage"])
    axes[0, 0].set_title("CPU使用率")
    axes[0, 0].set_ylabel("百分比")

    axes[0, 1].plot(metrics["timestamps"], metrics["memory_usage"])
    axes[0, 1].set_title("内存使用率")
    axes[0, 1].set_ylabel("百分比")

    axes[1, 0].plot(metrics["timestamps"], metrics["rule_execution_rate"])
    axes[1, 0].set_title("规则执行速率")
    axes[1, 0].set_ylabel("次/分钟")

    axes[1, 1].plot(metrics["timestamps"], metrics["thread_pool_queue_size"])
    axes[1, 1].set_title("线程池队列大小")
    axes[1, 1].set_ylabel("任务数")

    plt.tight_layout()
    plt.savefig(f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
    plt.show()

    return metrics

# 使用示例
if __name__ == "__main__":
    metrics = monitor_performance(duration_minutes=5)
    print("性能监控完成，报告已生成")
```

### 5. 数据备份和恢复

#### 数据备份脚本
```bash
#!/bin/bash
# 规则引擎数据备份脚本

BACKUP_DIR="/opt/rule-engine/backup"
DATA_DIR="/opt/rule-engine/data"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${BACKUP_DIR}/rule_engine_backup_${TIMESTAMP}.tar.gz"

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 停止服务（可选，用于一致性备份）
# systemctl stop rule-engine

# 备份数据库文件
echo "开始备份数据..."
tar -czf "$BACKUP_FILE" -C "$DATA_DIR" .

# 备份配置文件
tar -czf "${BACKUP_DIR}/config_backup_${TIMESTAMP}.tar.gz" \
    /opt/rule-engine/application.yml \
    /opt/rule-engine/logback-spring.xml

# 重启服务
# systemctl start rule-engine

echo "备份完成: $BACKUP_FILE"

# 清理旧备份（保留最近7天）
find "$BACKUP_DIR" -name "rule_engine_backup_*.tar.gz" -mtime +7 -delete
find "$BACKUP_DIR" -name "config_backup_*.tar.gz" -mtime +7 -delete

echo "旧备份清理完成"
```

#### 数据恢复脚本
```bash
#!/bin/bash
# 规则引擎数据恢复脚本

BACKUP_FILE="$1"
DATA_DIR="/opt/rule-engine/data"

if [ -z "$BACKUP_FILE" ]; then
    echo "Usage: $0 <backup_file>"
    echo "Available backups:"
    ls -la /opt/rule-engine/backup/rule_engine_backup_*.tar.gz
    exit 1
fi

if [ ! -f "$BACKUP_FILE" ]; then
    echo "Backup file not found: $BACKUP_FILE"
    exit 1
fi

# 停止服务
echo "停止规则引擎服务..."
systemctl stop rule-engine

# 备份当前数据
echo "备份当前数据..."
mv "$DATA_DIR" "${DATA_DIR}.backup.$(date +%Y%m%d_%H%M%S)"

# 创建数据目录
mkdir -p "$DATA_DIR"

# 恢复数据
echo "恢复数据..."
tar -xzf "$BACKUP_FILE" -C "$DATA_DIR"

# 设置权限
chown -R rule-engine:rule-engine "$DATA_DIR"
chmod -R 755 "$DATA_DIR"

# 启动服务
echo "启动规则引擎服务..."
systemctl start rule-engine

# 检查服务状态
sleep 5
if systemctl is-active --quiet rule-engine; then
    echo "数据恢复成功，服务已启动"
else
    echo "服务启动失败，请检查日志"
    systemctl status rule-engine
fi
```

## 运维最佳实践

### 1. 日常维护检查清单
- [ ] 检查服务运行状态
- [ ] 查看错误日志
- [ ] 监控内存和CPU使用率
- [ ] 检查磁盘空间
- [ ] 验证规则执行统计
- [ ] 检查数据库文件大小
- [ ] 验证MQTT连接状态

### 2. 定期维护任务
- **每日**：检查错误日志，监控关键指标
- **每周**：清理过期日志，检查备份完整性
- **每月**：性能分析，优化配置参数
- **每季度**：系统升级，安全检查

### 3. 应急响应流程
1. **服务异常**：检查日志 → 重启服务 → 恢复数据
2. **性能问题**：监控指标 → 调整配置 → 扩容资源
3. **数据丢失**：停止服务 → 恢复备份 → 验证数据
4. **安全事件**：隔离系统 → 分析日志 → 修复漏洞
